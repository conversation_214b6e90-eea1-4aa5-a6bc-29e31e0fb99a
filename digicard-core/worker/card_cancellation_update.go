// Package worker ...
package worker

import (
	"context"
	"fmt"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/cancel"
	"path/filepath"
	"strings"
	"time"

	"github.com/go-co-op/gocron"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/utils/cache"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
	cardCancellationUpdateLogTag                = "cardCancellationUpdateWorker"
	cardCancellationUpdateLock                  = "cardCancellationUpdateLock"
	cardCancellationUpdateLockDurationInMinutes = 5
)

// scheduleCardCancellationUpdate cancellation
func scheduleCardCancellationUpdate(ctx context.Context, cfg *config.AppConfig, physicalCardWorker *PhysicalCardWorker, scheduler *gocron.Scheduler) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("worker", cardCancellationUpdateLogTag))
	slog.FromContext(ctx).Info(cardCancellationUpdateLogTag, "Running cronjob to schedule update card cancellation.")

	cron := cfg.WorkerConfig.CardCancellationUpdateWorker.CronExpression
	for _, cronExp := range cron {
		if _, err := scheduler.Cron(cronExp).Do(cardCancellationUpdate, ctx, physicalCardWorker); err != nil {
			slog.FromContext(ctx).Fatal(cardCancellationUpdateLogTag, "failed to schedule update card cancellation.")
		}
	}
}

// cardCancellationUpdate ...
var cardCancellationUpdate = func(ctx context.Context, physicalCardWorker *PhysicalCardWorker) error {
	lock, err := cache.RedisClient.TryLock(ctx, cardCancellationUpdateLock, time.Duration(cardCancellationUpdateLockDurationInMinutes)*time.Minute)
	if err != nil {
		// most likely another pod has the lock
		slog.FromContext(ctx).Warn(cardCancellationUpdateLogTag, "unable to get lock into redis", slog.Error(err))
		return nil
	}
	defer func() {
		if lock != nil {
			unlockErr := lock.Unlock(ctx)
			if unlockErr != nil {
				slog.FromContext(ctx).Error(cardCancellationUpdateLogTag, "unable to release redis lock", slog.Error(err))
			}
		}
	}()

	timeZone, _ := time.LoadLocation(constant.TimeZoneMalaysia)
	today := time.Now().In(timeZone).Format(constant.FormatYYYYMMDD)
	sourceFileContentPath := fmt.Sprintf("%s/%s/%s", "CasaRecords", today, "CARD_CASA_CLOSED")
	fileList, err := logic.ListFileFromS3(ctx, cardCancellationUpdateLogTag, sourceFileContentPath, 10)

	if err != nil {
		slog.FromContext(ctx).Error(cardCancellationUpdateLogTag, "Error listing file", slog.Error(err))
		return err
	}
	if len(fileList) <= 0 {
		slog.FromContext(ctx).Info(cardCancellationUpdateLogTag, fmt.Sprintf("No files found on %s", sourceFileContentPath))
	}

	for _, file := range fileList {
		filePath := *file.Key
		fileExt := filepath.Ext(filePath)
		if strings.ToLower(fileExt) != ".csv" {
			slog.FromContext(ctx).Warn(cardCancellationUpdateLogTag, fmt.Sprintf("Invalid file format, skiped %s ", filePath))
			continue
		}
		if err := cancel.ProcessCardCASAFile(ctx, filePath, physicalCardWorker.AccountServiceClient, physicalCardWorker.EuronetAdapterClient); err != nil {
			slog.FromContext(ctx).Warn(cardCancellationUpdateLogTag, fmt.Sprintf("Error processing file, %s ", err))
			continue
		}
	}
	slog.FromContext(ctx).Info(cardCancellationUpdateLogTag, fmt.Sprintf("Update card cancel complete %s", sourceFileContentPath))
	return nil
}
