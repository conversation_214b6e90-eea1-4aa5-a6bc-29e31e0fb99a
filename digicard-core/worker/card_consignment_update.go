// Package worker ...
package worker

import (
	"context"
	"fmt"
	"time"

	"github.com/go-co-op/gocron"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/consignment"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/utils/cache"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
	cardConsignmentUpdateLogTag            = "cardConsignmentUpdateWorker"
	cardConsignmentUpdateLock              = "cardConsignmentUpdateLock"
	cardConsignmentUpdateLockDurationInSec = 10
)

// scheduleCardConsignmentUpdate
func scheduleCardConsignmentUpdate(ctx context.Context, cfg *config.AppConfig, physicalCardWorker *PhysicalCardWorker, scheduler *gocron.Scheduler) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("worker", cardConsignmentUpdateLogTag))
	slog.FromContext(ctx).Info(cardConsignmentUpdateLogTag, "Running cronjob to schedule update card consignment number.")

	cron := cfg.WorkerConfig.CardConsignmentUpdateWorker.CronExpression
	for _, cronExp := range cron {
		if _, err := scheduler.Cron(cronExp).Do(cardConsignmentUpdate, ctx, physicalCardWorker); err != nil {
			slog.FromContext(ctx).Fatal(cardConsignmentUpdateLogTag, "failed to schedule update card consignment number.")
		}
	}
}

// cardConsignmentUpdate ...
var cardConsignmentUpdate = func(ctx context.Context, physicalCardWorker *PhysicalCardWorker) error {
	lock, err := cache.RedisClient.TryLock(ctx, cardConsignmentUpdateLock, time.Duration(cardConsignmentUpdateLockDurationInSec)*time.Second)
	if err != nil {
		// most likely another pod has the lock
		slog.FromContext(ctx).Warn(cardConsignmentUpdateLogTag, "unable to get lock into redis", slog.Error(err))
		return nil
	}
	defer func() {
		if lock != nil {
			unlockErr := lock.Unlock(ctx)
			if unlockErr != nil {
				slog.FromContext(ctx).Error(cardConsignmentUpdateLogTag, "unable to release redis lock", slog.Error(err))
			}
		}
	}()

	courierName := logic.FetchCourierName(physicalCardWorker.PhysicalCardDeliveryConfig.CourierList)
	today := time.Now().Format(constant.FormatYYYYMMDD)
	todaySourceFilePath := fmt.Sprintf("%s/%s/%s", "Embossing", today, "DBMY_Docket_Listing")
	fileList, err := logic.ListFileFromS3(ctx, cardConsignmentUpdateLogTag, todaySourceFilePath, 10)
	if err != nil {
		slog.FromContext(ctx).Error(cardConsignmentUpdateLogTag, "Error listing file", slog.Error(err))
		return err
	}
	if len(fileList) == 0 {
		slog.FromContext(ctx).Info(cardConsignmentUpdateLogTag, fmt.Sprintf("No files found on %s", today))
	}

	for _, file := range fileList {
		filePath := *file.Key
		fileExt := filePath[len(filePath)-4:]
		if fileExt != "xlsx" {
			slog.FromContext(ctx).Warn(cardConsignmentUpdateLogTag, fmt.Sprintf("Invalid file format, skiped %s ", filePath))
			continue
		}
		if err := consignment.ProcessDocketListingFile(ctx, filePath, courierName); err != nil {
			slog.FromContext(ctx).Warn(cardConsignmentUpdateLogTag, fmt.Sprintf("Error processing file, %s ", err))
			continue
		}
	}
	slog.FromContext(ctx).Info(cardConsignmentUpdateLogTag, fmt.Sprintf("Update consignment complete %s", today))
	return nil
}
