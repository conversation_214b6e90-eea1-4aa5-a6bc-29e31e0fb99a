{"name": "digicard-core Service", "serviceName": "digicard-core", "host": "0.0.0.0", "port": 8083, "ownerInfo": {"name": "digibank", "email": "<EMAIL>", "url": "digibank.myteksi.com"}, "env": "local", "data": {"mysql": {"master": {"dsn": "{{mysql_username}}:{{mysql_password}}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "slave": {"dsn": "{{mysql_username}}:{{mysql_password}}@tcp($MYSQL_HOST$:3306)/$DB_NAME$?parseTime=true&loc=UTC", "maxIdle": 25, "maxOpen": 50, "connMaxLifetime": "1800s"}, "masterCircuitBreaker": {"timeoutInMs": 100000}, "slaveCircuitBreaker": {"timeoutInMs": 100000}}}, "paymentCore": {"serviceName": "payment-core", "baseURL": "https://backend.dev.g-bank.app/payment-core"}, "accountService": {"serviceName": "account-service", "baseURL": "https://backend.dev.g-bank.app/account-service"}, "customerMaster": {"serviceName": "customer-master", "baseURL": "https://backend.dev.g-bank.app/customer-master"}, "euronetAdapter": {"serviceName": "euronet-adapter", "baseURL": "https://backend.dev.g-bank.app/euronet-adapter"}, "digicardTxn": {"serviceName": "digicard-txn", "baseURL": "https://backend.dev.g-bank.app/digicard-txn"}, "pigeon": {"serviceName": "pigeon", "baseURL": "https://backend.dev.g-bank.app/pigeon"}, "transactionLimit": {"serviceName": "transaction-limit", "baseURL": "https://backend.dev.g-bank.app/transaction-limit"}, "riskService": {"serviceName": "gd-proxy", "baseURL": "https://backend.dev.g-bank.app/foobar"}, "whitelistService": {"serviceName": "whitelist-service", "baseURL": "https://backend.dev.g-bank.app/whitelist-service"}, "aegis": {"serviceName": "aegis", "baseURL": "https://backend.dev.g-bank.app/aegis"}, "hedwigConfig": {"hostAddress": "https://backend.dev.g-bank.app/foobar", "pushServerPath": "/hedwig/v1/push", "circuitConfig": {"hedwig": {"timeout": 15}}, "templateMappings": {"card_creation_success_push_notification": "3bacf313-08f6-48f5-b5ed-511e0d8ca656", "card_creation_failed_push_notification": "3bacf313-08f6-48f5-b5ed-511e0d8ca656", "virtual_issued_push_notification": "38882ce0-607d-4a66-adff-ffc610fe0510"}}, "statsd": {"host": "localhost", "port": 8125}, "trace": {"host": "localhost", "port": 8126}, "redisTTLConfig": {"cardDesignLimit": 300}, "redisConfig": {"addr": "localhost:30001", "idleTimeoutInSec": 1, "poolSize": 300, "readOnlyFromSlaves": false, "readTimeoutInSec": 1, "writeTimeoutInSec": 1}, "lockConfig": {"durationInSeconds": 1}, "digicardActivityStream": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "digicard-core-tx-local", "clusterType": "critical", "topicName": "dev-digicard-activity-tx", "enableTLS": true, "initOffset": "oldest"}, "digicardAuditStream": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "digicard-core-tx-local", "clusterType": "critical", "topicName": "dev-audit-log", "enableTLS": true, "initOffset": "oldest"}, "depositsAccountDetailStream": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "digicard-core-tx-local", "clusterType": "critical", "topicName": "dev-deposits-account-detail-event", "enableTLS": true, "initOffset": "oldest", "streamID": "local_deposits_account_detail_event", "dtoName": "AccountDetail"}, "logger": {"syslogTag": "structuredlog.todolist", "workerCount": 10, "bufferSize": 10000, "logLevel": 1, "stacktraceLevel": 4, "logFormat": "cglsdebug", "development": true}, "paymentCoreStream": {"brokers": ["b-1.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-2.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094", "b-3.msk05msk.tlb15z.c5.kafka.ap-southeast-1.amazonaws.com:9094"], "clientID": "digicard-core-local", "clusterType": "critical", "topicName": "dev-payment-core-tx", "enableTLS": true, "initOffset": "newest", "retry": {"intervalInMilliSeconds": 100, "attempts": 3}}, "pcRetryStream": {"environment": "local", "queueURL": "http://sqs.ap-southeast-1.localhost.localstack.cloud:4566/************/queuehandler-sqs", "awsRegion": "ap-southeast-1", "exponentialBackoffBaseIntervalSeconds": 1, "waitTimeSeconds": 5, "maxNumberOfMessages": 1, "maxConcurrency": 5}, "featureFlags": {"enableAccountStatusEnquiryCheck": false, "enableCardCreationPermissionAndAccountStatusCheck": true, "enableActivationCodeCheck": false, "enablePhysicalCardCreationRiskCheck": false, "enableVirtualCardCreationRiskCheck": true, "enableNewCardUpdateStatusFlow": true, "enablePushNotificationFlow": false, "enableWorker": true, "enableWhitelistCheck": true, "enableHelpCenter": false, "enableCardReplacement": false, "enableNameCheck": true, "enableDepositsAccountDetailStream": true, "enableDeliveryTracker": true}, "s3BucketName": "dbmy-dev-cards-euronet-adapter-test", "s3ClientDevMode": true, "timeZone": "Asia/Kuala_Lumpur", "limitConfigs": [{"name": "DIGICARD_DEBITCARD_LIMIT", "title": "Set daily card limit", "subTitle": "Card payment", "preferredCardLimits": [1000, 5000, 10000]}, {"name": "DIGICARD_DEBITCARD_ATM_LIMIT", "title": "Set daily card ATM withdrawal limit", "subTitle": "Card ATM", "preferredCardLimits": [200, 500, 1000]}, {"name": "DIGICARD_DEBITCARD_PINPAY_LIMIT", "title": "Set daily card Pin & Pay limit", "subTitle": "Card Pin & Pay limit", "preferredCardLimits": [200, 500, 1000]}, {"name": "DIGICARD_DEBITCARD_ONLINE_LIMIT", "title": "Set daily card Online limit", "subTitle": "Card Online Payment limit", "preferredCardLimits": [200, 500, 1000]}, {"name": "DIGICARD_DEBITCARD_CONTACTLESS_LIMIT", "title": "Set daily card Contactless limit", "subTitle": "Card Contactless Payment limit", "preferredCardLimits": [1000, 5000, 10000]}], "countryConfig": {"currencyCode": "MYR", "currencyNum": "458", "countryCode": "MY", "countryCodeAlpha3": "MYS", "countryName": "Malaysia"}, "cardIssuanceConfig": {"maxAllowedCards": 99, "maxAllowedActiveCards": 2}, "physicalCardDeliveryConfig": {"orderConfirmed": 1, "posted": 13, "notReceivedYet": 14, "canActivateCard": 2, "canViewHelpCenter": 14, "estimatedArrivalDays": 7, "estimatedArrivalDaysBuffer": 2, "physicalCardDeliveredDispatched": 2, "physicalCardActivationReminder1": 21, "physicalCardActivationReminder2": 37, "physicalCardActivationReminder3": 67, "physicalCardActivationReminder4": 97, "courierList": {"City-Link Express": "https://www.citylinkexpress.com/tracking-result/?track0=consignmentNo"}}, "workflowRetryConfig": {"virtualCardCreation": {"intervalInSeconds": 300, "maxAttempt": 5}, "virtualCardNotificationRetry": {"intervalInSeconds": 120, "maxAttempt": 5}}, "workerConfig": {"cardArrivalNotification": {"cronExpression": ["00 12 * * *"]}, "physicalCardDispatcherNotificationWorker": {"cronExpression": ["00 2 * * *"]}, "cardConsignmentUpdateWorker": {"cronExpression": ["*/1 * * * *"]}, "cardCancellationUpdateWorker": {"cronExpression": ["*/1 * * * *"]}}, "cardPinEncryptionPublicKey": "{{CARD_PIN_ENCRYPTION_PUBLIC_KEY}}", "physicalCardOrderFee": {"amount": 1200, "creditCardFeeAmount": 1200, "debitCardFeeAmount": 1200}}