// Package cache provide utility methods to use caching functionality
package cache

import (
	"context"
	"encoding/json"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2"
)

var (
	// RedisClient defines the redis client
	RedisClient redis.Client
)

// Init initialize the RedisClient
func Init(app *servus.Application, conf *config.AppConfig) redis.Client {
	redisClient := redis.MakeClusterClient(conf.RedisConf, redis.StatsD(app.GetStatsD()), redis.Logger(app.GetLogger()))
	RedisClient = redisClient
	return redisClient
}

// Set ...
func Set(ctx context.Context, key string, value interface{}, cacheExpiry time.Duration) error {
	p, err := json.Marshal(value)
	if err != nil {
		return err
	}

	_, err = RedisClient.Set(ctx, key, p, cacheExpiry*time.Second)
	return err
}

// Get ...
func Get(ctx context.Context, key string, dest interface{}) error {
	p, err := RedisClient.GetBytes(ctx, key)
	if err != nil {
		return err
	}
	return json.Unmarshal(p, dest)
}
