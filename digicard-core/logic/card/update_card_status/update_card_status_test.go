package updatecardstatus

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	customerjournalapi "gitlab.myteksi.net/dbmy/customer-journal/api"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	euronetAdapterMock "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	accountAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	accountServiceMock "gitlab.myteksi.net/dbmy/core-banking/account-service/api/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/audit"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
)

var (
	cardID    = uuid.NewString()
	userID    = uuid.NewString()
	accountID = uuid.NewString()
)

func TestUpdateCardStatus(t *testing.T) {
	card := getCard()
	mockEuronetAdapter := &euronetAdapterMock.EuronetAdapter{}
	mockAccountService := &accountServiceMock.AccountService{}
	updateReq := &api.UpdateCardRequest{
		CardID: cardID,
		Status: string(constant.CardStatusLocked),
	}
	mockCardDAO := &storage.MockICardDAO{}
	storage.CardDao = mockCardDAO
	mockCardDesignDAO := &storage.MockICardDesignDAO{}
	storage.CardDesignDao = mockCardDesignDAO
	cardLogService := &audit.MockCardActivityLogService{}
	c := &updateCardStatusClientImpl{
		EuronetAdapterClient:   mockEuronetAdapter,
		AccountServiceClientV2: mockAccountService,
		PhysicalCardDeliveryConfig: config.PhysicalCardDeliveryConfig{
			OrderConfirmed:    1,
			Posted:            13,
			NotReceivedYet:    14,
			CanActivateCard:   2,
			CanViewHelpCenter: 14,
		},
		CardActivityLog: cardLogService,
	}
	logic.GetUserID = func(ctx context.Context) string {
		return userID
	}
	t.Run("Valid flow", func(t *testing.T) {
		cardDesignMockDao := test.SetCardDesignMockDao()
		mockRedis := test.SetMockRedisClient()
		test.SetMockDataForCardDesign(cardDesignMockDao)
		test.SetMockDataForRedisClient(mockRedis)
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything, mock.Anything).Return(&accountAPI.GetAccountResponse{Account: &accountAPI.Account{
			Id:                  accountID,
			ProductVariantID:    "",
			PermittedCurrencies: nil,
			CifNumber:           "",
			Status:              accountAPI.AccountStatus_ACTIVE,
		}}, nil).Once()
		vendorResp := &euronetAdapterAPI.UpdateCardStatusResponse{
			ReferenceID:        uuid.NewString(),
			Status:             constant.Success,
			StatusReason:       "",
			ProxyNumber:        "1234",
			CardSequenceNumber: 1,
		}
		mockEuronetAdapter.On("CardStatusUpdate", mock.Anything, mock.Anything, mock.Anything).Return(vendorResp, nil).Once()
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		cardLogService.On("SendActivityLog", mock.Anything, mock.Anything).Once()
		ctx := context.Background()
		ctx = context.WithValue(ctx, audit.CardActivitySourceKey, customerjournalapi.TriggeredByGXBankAppSource)
		resp, err := c.UpdateCardStatus(ctx, updateReq, true, false)
		assert.NotNil(t, resp)
		assert.Equal(t, string(constant.CardStatusLocked), resp.Card.Status)
		assert.Equal(t, constant.Success, resp.Status)
		assert.Nil(t, err)
	})
	t.Run("valid flow - new status and current status are equal", func(t *testing.T) {
		card.Status = updateReq.Status
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		resp, err := c.UpdateCardStatus(context.Background(), updateReq, true, false)
		assert.NotNil(t, resp)
		assert.Equal(t, updateReq.Status, resp.Card.Status)
		assert.Equal(t, constant.Success, resp.Status)
		assert.Nil(t, err)
	})
	card.Status = string(constant.CardStatusActive)
	t.Run("logic.FetchCardByCardAndUserID Fails", func(t *testing.T) {
		expectedErr := servus.ServiceError{
			Code:     string(api.CardNotFound),
			Message:  "Card not found",
			HTTPCode: api.BadRequest.HTTPStatusCode(),
		}
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		ctx := context.Background()
		ctx = context.WithValue(ctx, audit.CardActivitySourceKey, customerjournalapi.TriggeredByGXBankAppSource)
		cardLogService.On("SendActivityLog", mock.Anything, mock.Anything).Once()
		resp, err := c.UpdateCardStatus(ctx, updateReq, true, false)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("validateCard Fails", func(t *testing.T) {
		card.ReasonCode = string(constant.ReasonCodeBankBlock)
		expectedErr := logic.ErrCardBlocked
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		ctx := context.Background()
		ctx = context.WithValue(ctx, audit.CardActivitySourceKey, customerjournalapi.TriggeredByGXBankAppSource)
		cardLogService.On("SendActivityLog", mock.Anything, mock.Anything).Once()
		resp, err := c.UpdateCardStatus(ctx, updateReq, true, false)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
		card.ReasonCode = ""
	})
	t.Run("enquiry.CheckCasaAccount Fails", func(t *testing.T) {
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything, mock.Anything).Return(&accountAPI.GetAccountResponse{
			Account: &accountAPI.Account{
				Id:     accountID,
				Status: accountAPI.AccountStatus_CLOSED,
			},
		}, nil).Once()
		ctx := context.Background()
		ctx = context.WithValue(ctx, audit.CardActivitySourceKey, customerjournalapi.TriggeredByGXBankAppSource)
		cardLogService.On("SendActivityLog", mock.Anything, mock.Anything).Once()
		resp, err := c.UpdateCardStatus(ctx, updateReq, true, false)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, logic.ErrInactiveAccount, err)
	})
	t.Run("valid flow - without holdcodes", func(t *testing.T) {
		cardDesignMockDao := test.SetCardDesignMockDao()
		mockRedis := test.SetMockRedisClient()
		test.SetMockDataForCardDesign(cardDesignMockDao)
		test.SetMockDataForRedisClient(mockRedis)
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything, mock.Anything).Return(&accountAPI.GetAccountResponse{Account: &accountAPI.Account{
			Id:                        accountID,
			ProductVariantID:          "",
			PermittedCurrencies:       nil,
			CifNumber:                 "",
			Status:                    accountAPI.AccountStatus_ACTIVE,
			ProductSpecificParameters: map[string]string{"applicableHoldcodes": "[\"NO_CREDIT\"]"},
		}}, nil).Once()
		vendorResp := &euronetAdapterAPI.UpdateCardStatusResponse{
			ReferenceID:        uuid.NewString(),
			Status:             constant.Success,
			StatusReason:       "",
			ProxyNumber:        "1234",
			CardSequenceNumber: 1,
		}
		mockEuronetAdapter.On("CardStatusUpdate", mock.Anything, mock.Anything, mock.Anything).Return(vendorResp, nil).Once()
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		cardLogService.On("SendActivityLog", mock.Anything, mock.Anything).Once()
		ctx := context.Background()
		ctx = context.WithValue(ctx, audit.CardActivitySourceKey, customerjournalapi.TriggeredByGXBankAppSource)
		resp, err := c.UpdateCardStatus(ctx, &api.UpdateCardRequest{
			CardID: cardID,
			Status: string(constant.CardStatusRetired),
		}, true, false)
		assert.NotNil(t, resp)
		assert.Equal(t, string(constant.CardStatusRetired), resp.Card.Status)
		assert.Equal(t, constant.Success, resp.Status)
		assert.Nil(t, err)
	})

	t.Run("invokeVendor fails", func(t *testing.T) {
		expectedErr := servus.ServiceError{
			Code:     constant.Failed,
			Message:  "Sample Failed",
			HTTPCode: api.BadRequest.HTTPStatusCode(),
		}
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything, mock.Anything).Return(&accountAPI.GetAccountResponse{Account: &accountAPI.Account{
			Id:                  accountID,
			ProductVariantID:    "",
			PermittedCurrencies: nil,
			CifNumber:           "",
			Status:              accountAPI.AccountStatus_ACTIVE,
		}}, nil).Once()
		vendorResp := &euronetAdapterAPI.UpdateCardStatusResponse{
			ReferenceID:        uuid.NewString(),
			Status:             constant.Failed,
			StatusReason:       "Sample Failed",
			ProxyNumber:        "1234",
			CardSequenceNumber: 1,
		}
		mockEuronetAdapter.On("CardStatusUpdate", mock.Anything, mock.Anything).Return(vendorResp, nil).Once()
		ctx := context.Background()
		ctx = context.WithValue(ctx, audit.CardActivitySourceKey, customerjournalapi.TriggeredByGXBankAppSource)
		cardLogService.On("SendActivityLog", mock.Anything, mock.Anything).Once()
		resp, err := c.UpdateCardStatus(ctx, updateReq, true, false)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("persistCardUpdate fails", func(t *testing.T) {
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything, mock.Anything).Return(&accountAPI.GetAccountResponse{Account: &accountAPI.Account{
			Id:                  accountID,
			ProductVariantID:    "",
			PermittedCurrencies: nil,
			CifNumber:           "",
			Status:              accountAPI.AccountStatus_ACTIVE,
		}}, nil).Once()
		vendorResp := &euronetAdapterAPI.UpdateCardStatusResponse{
			ReferenceID:        uuid.NewString(),
			Status:             constant.Success,
			StatusReason:       "",
			ProxyNumber:        "1234",
			CardSequenceNumber: 1,
		}
		mockEuronetAdapter.On("CardStatusUpdate", mock.Anything, mock.Anything).Return(vendorResp, nil).Once()
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(data.ErrNotSupported).Once()
		ctx := context.Background()
		ctx = context.WithValue(ctx, audit.CardActivitySourceKey, customerjournalapi.TriggeredByGXBankAppSource)
		cardLogService.On("SendActivityLog", mock.Anything, mock.Anything).Once()
		resp, err := c.UpdateCardStatus(ctx, updateReq, true, false)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
	t.Run("convertResponse fails", func(t *testing.T) {
		cardDesignMockDao := test.SetCardDesignMockDao()
		mockRedis := test.SetMockRedisClient()
		test.SetEmptyMockForCardDesign(cardDesignMockDao)
		test.SetMockDataForRedisClient(mockRedis)
		mockCardDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		mockAccountService.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything, mock.Anything).Return(&accountAPI.GetAccountResponse{Account: &accountAPI.Account{
			Id:                  accountID,
			ProductVariantID:    "",
			PermittedCurrencies: nil,
			CifNumber:           "",
			Status:              accountAPI.AccountStatus_ACTIVE,
		}}, nil).Once()
		vendorResp := &euronetAdapterAPI.UpdateCardStatusResponse{
			ReferenceID:        uuid.NewString(),
			Status:             constant.Success,
			StatusReason:       "",
			ProxyNumber:        "1234",
			CardSequenceNumber: 1,
		}
		mockEuronetAdapter.On("CardStatusUpdate", mock.Anything, mock.Anything).Return(vendorResp, nil).Once()
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		ctx := context.Background()
		ctx = context.WithValue(ctx, audit.CardActivitySourceKey, customerjournalapi.TriggeredByGXBankAppSource)
		cardLogService.On("SendActivityLog", mock.Anything, mock.Anything).Once()
		resp, err := c.UpdateCardStatus(ctx, updateReq, true, false)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, data.ErrNoData, err)
	})
}

func TestValidateCard(t *testing.T) {
	c := &updateCardStatusClientImpl{}
	card := getCard()
	status := string(constant.CardStatusLocked)
	t.Run("Valid flow", func(t *testing.T) {
		err := c.validateCard(card, status, true)
		assert.Nil(t, err)
	})
	t.Run("Card is bank blocked", func(t *testing.T) {
		card.ReasonCode = string(constant.ReasonCodeBankBlock)
		err := c.validateCard(card, status, true)
		assert.NotNil(t, err)
		assert.Equal(t, logic.ErrCardBlocked, err, true)
	})
	t.Run("Card is bank blocked and ingore this validation", func(t *testing.T) {
		card.ReasonCode = string(constant.ReasonCodeBankBlock)
		err := c.validateCard(card, status, false)
		assert.Nil(t, err)
	})
	t.Run("invalid card status", func(t *testing.T) {
		card.ReasonCode = ""
		status = "DUMMY"
		err := c.validateCard(card, status, true)
		assert.NotNil(t, err)
		assert.Equal(t, logic.ErrInvalidCardStatus, err)
	})
}

func TestInvokeVendor(t *testing.T) {
	mockEuronetAdapter := &euronetAdapterMock.EuronetAdapter{}
	c := &updateCardStatusClientImpl{
		EuronetAdapterClient: mockEuronetAdapter,
	}
	updateReq := &api.UpdateCardRequest{
		CardID: cardID,
		Status: string(constant.CardStatusLocked),
	}
	t.Run("Valid flow", func(t *testing.T) {
		resp := &euronetAdapterAPI.UpdateCardStatusResponse{
			ReferenceID:        uuid.NewString(),
			Status:             constant.Success,
			StatusReason:       "",
			ProxyNumber:        "1234",
			CardSequenceNumber: 1,
		}
		mockEuronetAdapter.On("CardStatusUpdate", mock.Anything, mock.Anything).Return(resp, nil).Once()
		err := c.invokeVendor(context.Background(), c.constructCardUpdateRequest(updateReq, string(constant.OrderStatusOrdered)))
		assert.Nil(t, err)
	})
	t.Run("Valid flow with failed status", func(t *testing.T) {
		resp := &euronetAdapterAPI.UpdateCardStatusResponse{
			ReferenceID:        uuid.NewString(),
			Status:             constant.Failed,
			StatusReason:       "Sample Reason",
			ProxyNumber:        "1234",
			CardSequenceNumber: 1,
		}
		expectedErr := servus.ServiceError{
			Code:     resp.Status,
			Message:  resp.StatusReason,
			HTTPCode: api.BadRequest.HTTPStatusCode(),
		}
		mockEuronetAdapter.On("CardStatusUpdate", mock.Anything, mock.Anything).Return(resp, nil).Once()
		err := c.invokeVendor(context.Background(), c.constructCardUpdateRequest(updateReq, string(constant.OrderStatusOrdered)))
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("EuronetAdapterClient.CardStatusUpdate Fails", func(t *testing.T) {
		mockEuronetAdapter.On("CardStatusUpdate", mock.Anything, mock.Anything).Return(nil, api.DefaultInternalServerError).Once()
		err := c.invokeVendor(context.Background(), c.constructCardUpdateRequest(updateReq, string(constant.OrderStatusOrdered)))
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
}

func TestConstructCardUpdateRequest(t *testing.T) {
	c := &updateCardStatusClientImpl{}
	req := &api.UpdateCardRequest{
		CardID: cardID,
		Status: string(constant.CardStatusLocked),
	}
	t.Run("Valid flow", func(t *testing.T) {
		vendorRequest := c.constructCardUpdateRequest(req, string(constant.OrderStatusOrdered))
		assert.NotNil(t, vendorRequest)
		assert.Equal(t, string(constant.CardStatusLocked), vendorRequest.TargetCardStatus)
		assert.Equal(t, cardID, vendorRequest.CardID)
	})
}

func TestPersistCardUpdate(t *testing.T) {
	card := getCard()
	mockCardDAO := &storage.MockICardDAO{}
	storage.CardDao = mockCardDAO
	c := &updateCardStatusClientImpl{}

	t.Run("Valid flow", func(t *testing.T) {
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		updatedCard, err := c.persistCardUpdate(context.Background(), card, string(constant.CardStatusLocked))
		assert.NotNil(t, updatedCard)
		assert.Equal(t, string(constant.CardStatusLocked), updatedCard.Status)
		assert.Equal(t, cardID, updatedCard.CardID)
		assert.Nil(t, err)
	})
	t.Run("storage.CardDao.UpdateEntity Fails", func(t *testing.T) {
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(api.DefaultInternalServerError).Once()
		updatedCard, err := c.persistCardUpdate(context.Background(), card, string(constant.CardStatusRetired))
		assert.Nil(t, updatedCard)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
}

func TestConvertResponse(t *testing.T) {
	mockCardDesignDAO := &storage.MockICardDesignDAO{}
	storage.CardDesignDao = mockCardDesignDAO
	c := &updateCardStatusClientImpl{
		PhysicalCardDeliveryConfig: config.PhysicalCardDeliveryConfig{
			OrderConfirmed:    1,
			Posted:            13,
			NotReceivedYet:    14,
			CanActivateCard:   2,
			CanViewHelpCenter: 14,
		},
	}
	card := getCard()
	t.Run("Valid flow", func(t *testing.T) {
		cardDesignMockDao := test.SetCardDesignMockDao()
		mockRedis := test.SetMockRedisClient()

		test.SetMockDataForCardDesign(cardDesignMockDao)
		test.SetMockDataForRedisClient(mockRedis)
		resp, err := c.convertResponse(context.Background(), card)
		assert.NotNil(t, resp)
		assert.Equal(t, constant.Success, resp.Status)
		assert.Equal(t, cardID, resp.Card.CardID)
		assert.Nil(t, err)
	})

	t.Run("logic.FetchCardDesignByCardDesignID Fails", func(t *testing.T) {
		cardDesignMockDao := test.SetCardDesignMockDao()
		mockRedis := test.SetMockRedisClient()

		test.SetEmptyMockForCardDesign(cardDesignMockDao)
		test.SetMockDataForRedisClient(mockRedis)
		resp, err := c.convertResponse(context.Background(), card)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, data.ErrNoData, err)
	})

	t.Run("ToggleInfo json.Unmarshal Fails", func(t *testing.T) {
		cardDesignMockDao := test.SetCardDesignMockDao()
		mockRedis := test.SetMockRedisClient()
		card.ToggleInfo = json.RawMessage(`[]`)
		test.SetMockDataForCardDesign(cardDesignMockDao)
		test.SetMockDataForRedisClient(mockRedis)
		resp, err := c.convertResponse(context.Background(), card)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
}

func getCard() *storage.Card {
	card := &storage.Card{
		Status:                  string(constant.CardStatusActive),
		CardID:                  cardID,
		UserID:                  userID,
		OrderStatus:             string(constant.OrderStatusOrdered),
		PhysicalCardOrderedDate: time.Now().AddDate(0, 0, -3),
		PostScript:              []byte(`{"ActivationCode": "J6A7B", "DeliveryAddress": null}`),
		CardDesignID:            test.TestCardDesignID,
		AccountID:               accountID,
	}
	return card
}
