// Package namechecker ...
package namechecker

import (
	"context"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	aegisclient "gitlab.myteksi.net/dbmy/aegis/api"
)

const (
	nameCheckTag = "nameCheck"
)

// Client defines the client to perform name check.
type Client interface {
	NameCheck(ctx context.Context, req *api.CardNameCheckRequest) (*api.CardNameCheckResponse, error)
}

//go:generate mockery --name Client --inpackage --case=underscore

// NewClient creates a new name check client.
func NewClient() Client {
	return &impl{}
}

type impl struct {
	AegisClient aegisclient.Aegis `inject:"client.aegisClient,optional"`
}

var getUUID = func() string {
	return uuid.NewString()
}

// NameCheck ...
func (i *impl) NameCheck(ctx context.Context, req *api.CardNameCheckRequest) (*api.CardNameCheckResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(constant.CustomerIDTag, logic.GetUserID(ctx)))

	checkID := getUUID()
	aegisReq := &aegisclient.FilterTextRequest{
		Contents: []aegisclient.TextContent{
			{
				ID:      checkID,
				Content: req.DisplayName,
			},
		},
	}
	aegisResp, err := i.AegisClient.FilterText(ctx, aegisReq)
	if err != nil {
		slog.FromContext(ctx).Error(nameCheckTag, "error from aegis FilterText api", slog.Error(err))
		return nil, err
	}
	var resp *api.CardNameCheckResponse
	for _, v := range aegisResp.Results {
		if v.ID == checkID {
			resp = &api.CardNameCheckResponse{
				OriginalContent: v.OriginalContent,
				IsBad:           v.IsBad,
			}
			break
		}
	}
	if resp == nil {
		slog.FromContext(ctx).Error(nameCheckTag, "not able to match checkID from aegis FilterText response")
		return nil, logic.ErrInvalidParameters
	}
	return resp, nil
}
