package getallcards

import (
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

const (
	userID = "USER_ID"
	empty  = ""
)

func TestGetAllCards(t *testing.T) {
	setMockDataSet()

	t.Run("valid flow", func(t *testing.T) {
		logic.GetUserID = func(ctx context.Context) string {
			return userID
		}
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{CardDesignID: test.TestCardDesignID}}, nil)

		_, err := c.GetAllCards(context.Background())
		assert.Equal(t, err, nil)
	})
	t.Run("error finding active/locked cards for the user in card table", func(t *testing.T) {
		logic.GetUserID = func(ctx context.Context) string {
			return userID
		}
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		expectedResp := &api.GetAllCardsResponse{Card: []api.CardDetail{}}
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		resp, err := c.GetAllCards(context.Background())
		assert.NoError(t, err)
		assert.Equal(t, expectedResp, resp)
	})
	t.Run("user ID missing", func(t *testing.T) {
		logic.GetUserID = func(ctx context.Context) string {
			return empty
		}
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		expectedErr := logic.CustomError(http.StatusForbidden, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "'X-Grab-Id-Userid' is missing.",
				Path:      "X-Grab-Id-Userid",
			},
		})
		_, err := c.GetAllCards(context.Background())
		assert.Equal(t, expectedErr, err)
	})
}

func setMockDataSet() (*storage.MockICardDesignDAO, *mocks.Client) {
	cardDesignMockDao := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	test.SetMockDataForCardDesign(cardDesignMockDao)
	test.SetMockDataForRedisClient(mockRedis)

	return cardDesignMockDao, mockRedis
}
