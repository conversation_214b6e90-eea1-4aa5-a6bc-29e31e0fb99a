package specificcard

import (
	"context"
	"net/http"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	card_design_utils "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// GetCardByVendorCardID ...
func (c *ClientImpl) GetCardByVendorCardID(ctx context.Context, req *api.GetSpecificCardRequest) (*api.CardDetail, error) {
	if errs := validateGetCardByVendorCardIDReq(req); len(errs) != 0 {
		slog.FromContext(ctx).Warn(logTag, "invalid request parameters")
		c.Stats(constant.Failed, invalidReq)
		return nil, logic.CustomError(http.StatusBadRequest, &errs)
	}

	query := []data.Condition{
		data.EqualTo("VendorCardID", req.VendorCardID),
	}
	querySearch, err := storage.CardDao.Find(ctx, query...)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "error in fetching card details", slog.Error(err))
		c.Stats(constant.Failed, errorVendorID)
		return nil, logic.BuildErrorResponse(api.VendorCardIDInvalid, errorVendorID)
	}

	cardDesign, err := logic.FetchCardDesignByCardDesignID(ctx, querySearch[0].CardDesignID)
	if err != nil {
		return nil, err
	}

	c.Stats(constant.Success, "")
	return &api.CardDetail{
		CardID:         querySearch[0].CardID, //TODO: is this the expected behavior?
		DisplayName:    querySearch[0].DisplayName,
		Status:         querySearch[0].Status,
		AccountID:      querySearch[0].AccountID,
		CustomerID:     querySearch[0].UserID,
		TailCardNumber: querySearch[0].TailCardNumber,
		HeadCardNumber: querySearch[0].HeadCardNumber,
		OrderStatus:    querySearch[0].OrderStatus,
		VendorCardID:   querySearch[0].VendorCardID,
		CardDesign:     card_design_utils.ToCardDesignDetail(*cardDesign, false),
		CardType:       logic.GetCardType(querySearch[0]),
	}, nil
}

// validateGetCardByVendorCardIDReq ...
func validateGetCardByVendorCardIDReq(req *api.GetSpecificCardRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	if req.VendorCardID == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "VendorCardID is missing",
			Path:      "VendorCardID",
		})
	}
	return errs
}
