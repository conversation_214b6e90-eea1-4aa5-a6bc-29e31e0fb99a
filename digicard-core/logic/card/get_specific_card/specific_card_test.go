package specificcard

import (
	"context"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

const (
	cardID = "123"
	userID = "USER_ID"
	empty  = ""
)

func TestGetSpecificCard(t *testing.T) {
	setMockDataSet()

	t.Run("valid flow", func(t *testing.T) {
		request := api.GetSpecificCardRequest{}
		request.CardID = cardID
		logic.GetUserID = func(ctx context.Context) string {
			return userID
		}
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		mockCardDao.On("Find", mock.Anything, mock.Anything).Return(nil, nil)
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{CardDesignID: test.TestCardDesignID}}, nil)

		_, err := c.GetSpecificCard(context.Background(), &request)
		assert.Equal(t, err, nil)
	})
	t.Run("missing user ID", func(t *testing.T) {
		request := api.GetSpecificCardRequest{}
		request.CardID = cardID
		logic.GetUserID = func(ctx context.Context) string {
			return empty
		}
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		expectedErr := logic.CustomError(http.StatusForbidden, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "'X-Grab-Id-Userid' is missing.",
				Path:      "X-Grab-Id-Userid",
			},
		})
		_, err := c.GetSpecificCard(context.Background(), &request)
		assert.Equal(t, err, expectedErr)
	})
	t.Run("missing card ID", func(t *testing.T) {
		request := api.GetSpecificCardRequest{}
		request.CardID = empty
		logic.GetUserID = func(ctx context.Context) string {
			return userID
		}
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		expectedErr := logic.CustomError(http.StatusBadRequest, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldMissing),
				Message:   "CardID is missing",
				Path:      "CardID",
			},
		})
		mockCardDao.On("Find", mock.Anything, mock.Anything).Return(nil, nil)
		_, err := c.GetSpecificCard(context.Background(), &request)
		assert.Equal(t, err, expectedErr)
	})
	t.Run("invalid card ID", func(t *testing.T) {
		request := api.GetSpecificCardRequest{}
		request.CardID = cardID
		logic.GetUserID = func(ctx context.Context) string {
			return userID
		}
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		expectedErr := logic.CustomError(http.StatusForbidden, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldInvalid),
				Message:   "No such userID-cardID pair found",
				Path:      "UserID-CardID",
			},
		})
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData)
		_, err := c.GetSpecificCard(context.Background(), &request)
		assert.Equal(t, err, expectedErr)
	})
	t.Run("error finding userID and cardID combo in card table", func(t *testing.T) {
		request := api.GetSpecificCardRequest{}
		request.CardID = cardID
		logic.GetUserID = func(ctx context.Context) string {
			return userID
		}
		mockCardDao := &storage.MockICardDAO{}
		storage.CardDao = mockCardDao
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		expectedErr := logic.CustomError(http.StatusForbidden, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldInvalid),
				Message:   errorUserID,
				Path:      "UserID-CardID",
			},
		})
		mockCardDao.On("Find", mock.Anything, mock.Anything).Return(nil, nil)
		mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Card{{}}, api.DefaultInternalServerError)
		_, err := c.GetSpecificCard(context.Background(), &request)
		assert.Equal(t, err, expectedErr)
	})
}

func setMockDataSet() {
	cardDesignMockDao := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	test.SetMockDataForCardDesign(cardDesignMockDao)
	test.SetMockDataForRedisClient(mockRedis)
}
