package getalldesigns

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

const (
	userID = "USER_ID"
)

func TestGetAllCards(t *testing.T) {
	setMockDataSet()

	t.Run("valid flow", func(t *testing.T) {
		logic.GetUserID = func(ctx context.Context) string {
			return userID
		}
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		expectedResp := test.ExpectedCardDesignResponse()
		resp, err := c.GetAllCardDesigns(context.Background())
		assert.NoError(t, err)
		assert.Equal(t, expectedResp, resp)
	})
}

func TestGetCardsAllUnavailable(t *testing.T) {
	setMockUnavailableDataSet()

	t.Run("valid flow", func(t *testing.T) {
		logic.GetUserID = func(ctx context.Context) string {
			return userID
		}
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		expectedResp := test.ExpectedUnavailableCardDesignResponse()
		resp, err := c.GetAllCardDesigns(context.Background())
		assert.NoError(t, err)
		assert.Equal(t, expectedResp, resp)
	})
}

func TestGetPhysicalCardInventory(t *testing.T) {
	cardDesignDaoMock := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	cardDesignDaoMock.On("Find", mock.Anything, mock.Anything).Return(test.GetMockCardDesigns(), nil)
	mockRedis.On("Set", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(true, nil)
	mockRedis.On("GetBytes", mock.Anything, mock.Anything).Return(nil, redis.ErrNoData)

	t.Run("valid flow", func(t *testing.T) {
		c := ClientImpl{
			StatsD: statsd.NewNoop(),
		}
		expectedResp := test.ExpectedGetPhysicalCardInventoryResponse()
		resp, err := c.GetPhysicalCardInventory(context.Background())
		assert.NoError(t, err)
		assert.Equal(t, expectedResp, resp)
	})
}

func setMockDataSet() (*storage.MockICardDesignDAO, *mocks.Client) {
	cardDesignMockDao := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	test.SetMockDataForCardDesign(cardDesignMockDao)
	test.SetMockDataForRedisClient(mockRedis)

	return cardDesignMockDao, mockRedis
}

func setMockUnavailableDataSet() (*storage.MockICardDesignDAO, *mocks.Client) {
	cardDesignMockDao := test.SetCardDesignMockDao()
	mockRedis := test.SetMockRedisClient()

	test.SetMockUnavailableDataForCardDesign(cardDesignMockDao)
	test.SetMockDataForRedisClient(mockRedis)

	return cardDesignMockDao, mockRedis
}
