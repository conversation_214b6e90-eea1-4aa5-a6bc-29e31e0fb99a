// Package cancel ...
package cancel

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"github.com/google/uuid"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	accountAPIV2 "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
	"io"
	"time"

	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/service/s3"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
	logTag         = "CardCancelUpdate"
	processFileTag = "CardCASAFileProcessing"
)

type CloseCustomerCard struct {
	EuronetAdapterClient euronetAdapterAPI.EuronetAdapter `inject:"client.euronetAdapter"`
	AccountServiceClient accountAPIV2.AccountService      `inject:"client.accountServiceV2"`
	AccountID            string
	CardID               string
	UserID               string
}

func ProcessCardCASAFile(ctx context.Context, filePath string, accountServiceClient accountAPIV2.AccountService, euronetAdapterClient euronetAdapterAPI.EuronetAdapter) error {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag("filePath", filePath))
	slog.FromContext(ctx).Info(processFileTag, fmt.Sprintf("Fetching file %s", filePath))

	// Fetch CSV file from S3
	content, err := logic.FetchFromS3(ctx, processFileTag, filePath)
	if err != nil {
		if er, ok := err.(awserr.Error); ok {
			if er.Code() == s3.ErrCodeNoSuchKey {
				slog.FromContext(ctx).Warn(processFileTag, fmt.Sprintf("File not found %s", filePath))
				return er
			}
		}
		slog.FromContext(ctx).Error(processFileTag, fmt.Sprintf("Failed to fetch %s file from S3", filePath), slog.Error(err))
		return err
	}
	reader := bytes.NewReader(content)

	// Use CSV Reader
	csvReader := csv.NewReader(reader)
	csvReader.FieldsPerRecord = -1 // Allow variable number of fields

	lineNumber := 0

	// Read CSV line-by-line
	for {
		record, err := csvReader.Read()
		if err == io.EOF {
			slog.FromContext(ctx).Info(logTag, fmt.Sprintf("reached the end of the file at line %d", lineNumber), slog.Error(err))
			break // End of file
		}
		if err != nil {
			slog.FromContext(ctx).Error(processFileTag, "Error reading CSV line", slog.Error(err))
			return err
		}

		lineNumber++

		// Skip the header
		if lineNumber == 1 {
			continue
		}

		// Validate row length
		if len(record) < 6 {
			slog.FromContext(ctx).Warn(processFileTag, fmt.Sprintf("Skipping invalid line %d, insufficient columns", lineNumber))
			continue
		}

		cardId := record[0]
		accountId := record[1]
		cardProxy := record[2]
		cardStatus := record[3]
		orderStatus := record[4]
		cardSafeId := record[5]

		// Add contextual tags
		ctx = slog.AddTagsToContext(ctx,
			slog.CustomTag("lineNumber", lineNumber),
			slog.CustomTag("cardId", cardId),
			slog.CustomTag("accountId", accountId),
			slog.CustomTag("cardProxy", cardProxy),
			slog.CustomTag("cardStatus", cardStatus),
			slog.CustomTag("orderStatus", orderStatus),
			slog.CustomTag("cardSafeId", cardSafeId),
		)

		slog.FromContext(ctx).Info(processFileTag, fmt.Sprintf("Processing card by ID: %s cardProxy: %s", cardId, cardProxy))

		// Perform close card operation
		closeCustomerCard := &CloseCustomerCard{
			EuronetAdapterClient: euronetAdapterClient,
			AccountServiceClient: accountServiceClient,
			AccountID:            accountId,
			CardID:               cardId,
			UserID:               cardSafeId,
		}
		err = closeCustomerCard.closeCard(ctx)
		if err != nil {
			slog.FromContext(ctx).Error(processFileTag, "Error in closing card", slog.Error(err))
			continue
		}

		// Delay between requests to prevent over utilizing resources
		const delayBetweenCalls = 500 * time.Millisecond
		time.Sleep(delayBetweenCalls)
	}

	return nil
}

func (c *CloseCustomerCard) checkCASAAccountStatus(ctx context.Context) (bool, error) {
	const maxRetries = 3
	const retryDelay = 2 * time.Second

	var lastErr error
	slog.FromContext(ctx).Info(processFileTag, fmt.Sprintf("Start checking casa account status with account ID: %s", c.AccountID))

	for attempt := 1; attempt <= maxRetries; attempt++ {

		req := &accountAPIV2.GetCASAAccountRequest{
			AccountID: c.AccountID,
		}
		res, err := c.AccountServiceClient.GetCASAAccount(ctx, req)
		if err == nil {
			slog.FromContext(ctx).Info(logTag, fmt.Sprintf("Account status data: %v", res.Account.Status))
			canCloseCustomer := res.Account.Status == accountAPIV2.AccountStatus_CLOSED
			return canCloseCustomer, nil // Success, no need to retry further
		}

		lastErr = err
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Attempt %d: Account Service error - %s", attempt, err.Error()))

		// Delay before retrying, unless it's the last attempt
		if attempt < maxRetries {
			time.Sleep(retryDelay)
		}
	}

	return false, lastErr
}

func (c *CloseCustomerCard) closeCard(ctx context.Context) error {
	canCloseCustomer, err := c.checkCASAAccountStatus(ctx)
	if err != nil {
		slog.FromContext(ctx).Error(processFileTag, "Account Service error", slog.Error(err))
		return err
	}

	if !canCloseCustomer {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Skipping, Invalid record for CardId: %s", c.CardID))

		return nil
	}

	req := &euronetAdapterAPI.UpdateCardStatusRequest{
		TargetCardStatus: string(constant.CardStatusRetired),
		CardID:           c.CardID,
		IdempotencyKey:   uuid.NewString(),
		ReasonCode:       string(constant.ReasonCodeCASAAccountClose),
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("Start close card - user: %s - card id: %s", c.UserID, c.CardID))
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, c.UserID)
	res, err := c.EuronetAdapterClient.CardStatusUpdate(ctx, req)

	if err != nil {
		slog.FromContext(ctx).Error(logTag, "Error calling Euronet adapter cardStatusUpdate API", slog.Error(err))
		return err
	}
	if res.Status != constant.Success {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Invalid card update response status : %v %v", res.Status, res.StatusReason))
		return err
	}

	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("Try fetch card details"))
	cardRecord, err := c.fetchCard(ctx)
	if err != nil {
		slog.FromContext(ctx).Error(processFileTag, "Error in fetching card details", slog.Error(err))
		return nil
	}

	if cardRecord != nil {
		slog.FromContext(ctx).Info(processFileTag, fmt.Sprintf("Updating CardID: %s", c.CardID))
		if err = updateCardStatusToRetired(ctx, cardRecord); err != nil {
			slog.FromContext(ctx).Error(logTag, "Error persisting card after ", slog.Error(err))
			return err
		}
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("Retired card %s for account %s successful", c.CardID, c.AccountID))

	return nil
}

func (c *CloseCustomerCard) fetchCard(ctx context.Context) (*storage.Card, error) {
	query := []data.Condition{
		data.EqualTo("CardID", c.CardID),
		data.EqualTo("AccountID", c.AccountID),
		data.EqualTo("Status", constant.CardStatusActive),
	}
	card, err := storage.CardDao.FindOnSlave(ctx, query...)
	if err != nil {
		if err == data.ErrNoData {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Skipping, No card found CardId: %s", c.CardID))
			return nil, nil
		}
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Error fetching card CardId: %s", c.CardID))
		return nil, err
	}
	if len(card) > 1 {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Skipping, multiple records found for CardId: %s", c.CardID))
		return nil, nil
	}
	return card[0], nil
}

func updateCardStatusToRetired(ctx context.Context, card *storage.Card) error {
	updateCard := *card
	updateCard.Status = string(constant.CardStatusRetired)
	updateCard.CancelledAt = time.Now()
	updateCard.UpdatedAt = time.Now()
	err := storage.CardDao.UpdateEntity(ctx, card, &updateCard)

	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "Error updating status in db", slog.Error(err))
		return err
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("Update Success CardID: %s", card.CardID))

	return nil
}
