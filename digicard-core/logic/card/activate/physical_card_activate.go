// Package activate ...
package activate

import (
	"context"
	"errors"
	"time"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	digiCard "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
	physicalCardActivationTag = "PhysicalCardActivation"
)

// PhysicalCardActivateClient defines the client to activate physical card.
type PhysicalCardActivateClient interface {
	PhysicalCardActivation(ctx context.Context, req *api.PhysicalCardActivationRequest) error
}

//go:generate mockery --name PhysicalCardActivateClient --inpackage --case=underscore

// NewPhysicalCardActivateClient creates a new physical card activation client.
func NewPhysicalCardActivateClient() PhysicalCardActivateClient {
	return &PhysicalCardActivateClientImpl{}
}

// PhysicalCardActivateClientImpl ...
type PhysicalCardActivateClientImpl struct {
	EuronetAdapterClient       euronetAdapterAPI.EuronetAdapter  `inject:"client.euronetAdapter"`
	Publisher                  digiCard.Publisher                `inject:"publisher.card"`
	PhysicalCardDeliveryConfig config.PhysicalCardDeliveryConfig `inject:"config.physicalCardDelivery"`
	FeatureFlags               *config.FeatureFlags              `inject:"config.featureFlags"`
}

// PhysicalCardActivation ...
func (c *PhysicalCardActivateClientImpl) PhysicalCardActivation(ctx context.Context, req *api.PhysicalCardActivationRequest) error {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(constant.CustomerIDTag, logic.GetUserID(ctx)), slog.CustomTag("cardID", req.CardID))
	card, err := c.validateCard(ctx, req)
	if err != nil {
		return err
	}

	if err = c.invokeENAPhysicalCardActivate(ctx, req); err != nil {
		return api.DefaultInternalServerError
	}

	if err = c.updateCardOrderStatusToActivate(ctx, card); err != nil {
		return api.DefaultInternalServerError
	}

	return nil
}

func (c *PhysicalCardActivateClientImpl) validateCard(ctx context.Context, req *api.PhysicalCardActivationRequest) (*storage.Card, error) {
	card, err := logic.FetchCardByCardAndUserID(ctx, logic.GetUserID(ctx), req.CardID)
	if err != nil {
		slog.FromContext(ctx).Warn(physicalCardActivationTag, "CARD_NOT_FOUND", slog.Error(err))
		return nil, err
	}

	if card != nil {
		if c.FeatureFlags.EnableActivationCodeCheck {
			postScript, postScriptErr := dto.ConvertJSONToPostScript(card.PostScript)
			if postScriptErr != nil {
				slog.FromContext(ctx).Warn(physicalCardActivationTag, "card.PostScript.json.Unmarshal Failed", slog.Error(err))
				return nil, api.DefaultInternalServerError
			}
			if postScript.ActivationCode != req.ActivationCode {
				return nil, servus.ServiceError{
					Code:     string(api.InvalidActivationCode),
					Message:  "Invalid activation code",
					HTTPCode: api.InvalidActivationCode.HTTPStatusCode(),
				}
			}
		}
		if card.Status != string(constant.CardStatusActive) {
			if card.Status == string(constant.CardStatusLocked) {
				return nil, logic.ErrCardLocked
			}
			errorCode := api.Forbidden
			return nil, servus.ServiceError{
				Code:     "CARD_CANNOT_BE_ACTIVATED",
				Message:  "Card cannot be activate",
				HTTPCode: errorCode.HTTPStatusCode(),
			}
		}
		if card.OrderStatus == string(constant.OrderStatusActivated) {
			errorCode := api.Forbidden
			return nil, servus.ServiceError{
				Code:     "CARD_ALREADY_ACTIVATED",
				Message:  "Card is already activated",
				HTTPCode: errorCode.HTTPStatusCode(),
			}
		}
		deliveryInfo := logic.GetPhysicalCardDeliveryInfo(c.PhysicalCardDeliveryConfig, card)
		if !deliveryInfo.CanActivateCard {
			errorCode := api.Forbidden
			return nil, servus.ServiceError{
				Code:     "CARD_CANNOT_BE_ACTIVATED",
				Message:  "Card cannot be activate",
				HTTPCode: errorCode.HTTPStatusCode(),
			}
		}
	}
	return card, nil
}

func (c *PhysicalCardActivateClientImpl) invokeENAPhysicalCardActivate(ctx context.Context, req *api.PhysicalCardActivationRequest) error {
	enaReq := c.constructPhysicalCardActivateRequest(req)
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(constant.IdempotencyKeyTag, enaReq.IdempotencyKey))
	slog.FromContext(ctx).Info(physicalCardActivationTag, "Euronet adapter PhysicalCardActivate API - Initiated")

	enaResp, err := c.EuronetAdapterClient.PhysicalCardActivate(ctx, enaReq)
	if err != nil {
		slog.FromContext(ctx).Warn(physicalCardActivationTag, "error calling euronet adapter PhysicalCardActivate API", slog.Error(err))
		return err
	}

	if enaResp.Status == constant.Failed {
		err = errors.New(enaResp.StatusReason)
		slog.FromContext(ctx).Warn(physicalCardActivationTag, "euronet adapter PhysicalCardActivate API - Failed", slog.Error(err))
		return err
	}
	slog.FromContext(ctx).Info(physicalCardActivationTag, "Euronet adapter PhysicalCardActivate API - Succeeded")
	return nil
}

func (c *PhysicalCardActivateClientImpl) constructPhysicalCardActivateRequest(req *api.PhysicalCardActivationRequest) *euronetAdapterAPI.PhysicalCardActivateRequest {
	return &euronetAdapterAPI.PhysicalCardActivateRequest{
		CardID:         req.CardID,
		IdempotencyKey: uuid.NewString(),
	}
}

func (c *PhysicalCardActivateClientImpl) updateCardOrderStatusToActivate(ctx context.Context, card *storage.Card) error {
	newCard := *card
	newCard.OrderStatus = string(constant.OrderStatusActivated)
	newCard.PhysicalCardActivatedAt = time.Now()
	newCard.UpdatedAt = time.Now()
	err := storage.CardDao.UpdateEntity(ctx, card, &newCard)
	if err != nil {
		slog.FromContext(ctx).Warn(physicalCardActivationTag, "storage.CardDao.UpdateEntity : card activation status update Failed", slog.Error(err))
		return err
	}

	// TODO: Need to handle an edge case when save is successful but publishing fails
	if err = logic.PublishCardActivity(ctx, c.Publisher, &newCard, logic.PhysicalCardActivate, physicalCardActivationTag); err != nil {
		slog.FromContext(ctx).Warn(physicalCardActivationTag, "publishCardActivity: publishing card activity to kafka failed", slog.Error(err))
		return err
	}

	return nil
}
