// Package cnp ...
package cnp

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/toggle"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

const (
	logTag              = "UpdateToggle"
	serviceTag          = "digicard-core"
	errorUserID         = "No such userID-cardID pair found"
	invalidToggleAction = "Invalid Toggle Action"
	invalidToggleType   = "Invalid Toggle Type"
)

//go:generate mockery --name Client --inpackage --case=underscore

// NewClient creates a new CNP transaction client.
func NewClient() toggle.Client {
	return &ClientImpl{}
}

// ClientImpl ...
type ClientImpl struct {
	StatsD statsd.Client `inject:"statsD"`
}

// ActionUpdate ...
func (c *ClientImpl) ActionUpdate(ctx context.Context, request *api.ToggleUpdateRequest) (*api.ToggleUpdateResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.UserIDTag, logic.GetUserID(ctx)))
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.CardIDTag, request.CardID))
	card, err := c.fetchCard(ctx, request)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "error in fetching card details", slog.Error(err))
		return nil, err
	}
	isValidToggle := logic.ValidateCardTxnToggleAction(constant.CardStatus(card.Status),
		constant.OrderStatus(card.OrderStatus),
		request.Type)
	if !isValidToggle {
		slog.FromContext(ctx).Error(logTag, fmt.Sprintf("not able to toggle cnpTransaction, due to card status: %s, order status: %s", card.Status, card.OrderStatus))
		return nil, logic.BuildErrorResponse(api.InvalidToggleType, invalidToggleType)
	}
	updatedCard := *card
	toggleCnpInfo, err := dto.ConvertJSONToToggleInfo(updatedCard.ToggleInfo)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "ToggleInfo : error in json.Unmarshal", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}

	toggleCnpInfo[request.Type] = request.Action
	toggleCnpInfoJSON, err := dto.ConvertToggleInfoToJSON(toggleCnpInfo)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "ToggleInfo : error in json.Marshal", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}

	updatedCard.ToggleInfo = toggleCnpInfoJSON
	updatedCard.UpdatedAt = time.Now()
	err = storage.CardDao.UpdateEntity(ctx, card, &updatedCard)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "error in updating card details", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}

	return &api.ToggleUpdateResponse{
		Type: request.Type,
		Code: request.Action,
	}, nil
}

// IsValidAction ...
func (c *ClientImpl) IsValidAction(ctx context.Context, request *api.ToggleUpdateRequest) error {
	if request.Action == string(constant.ToggleDisabled) || request.Action == string(constant.ToggleEnabled) {
		return nil
	}
	return logic.BuildErrorResponse(api.InvalidToggleAction, invalidToggleAction)
}

// IsValidToggle ...
func (c *ClientImpl) IsValidToggle(ctx context.Context, request *api.ToggleUpdateRequest) error {
	if constant.ValidToggles[request.Type] {
		return nil
	}
	return logic.BuildErrorResponse(api.InvalidToggleType, invalidToggleType)
}

func (c *ClientImpl) fetchCard(ctx context.Context, request *api.ToggleUpdateRequest) (*storage.Card, error) {
	userID := logic.GetUserID(ctx)
	query := []data.Condition{
		data.EqualTo("UserID", userID),
		data.EqualTo("CardID", request.CardID),
	}

	querySearch, err := storage.CardDao.FindOnSlave(ctx, query...)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "error in fetching card details", slog.Error(err))
		if err == data.ErrNoData {
			slog.FromContext(ctx).Error(logTag, errorUserID)
			return nil, logic.BuildErrorResponse(api.CardNotFound, errorUserID)
		}
		slog.FromContext(ctx).Error(logTag, "storage.CardDao.FindOnSlave failed", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}
	return querySearch[0], nil
}

// Stats ...
func (c *ClientImpl) Stats(status string, statusReason string) {
	c.StatsD.Count1(serviceTag, logTag,
		fmt.Sprintf("status:%s", status),
		fmt.Sprintf("status_reason:%s", statusReason))
}
