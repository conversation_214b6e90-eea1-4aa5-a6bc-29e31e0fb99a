package strategy

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	riskAPI "gitlab.myteksi.net/dakota/client-sdk/risk-service"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	accountAPIV2 "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// CreditCardStrategy implements ProductStrategy for credit cards
type CreditCardStrategy struct {
	AccountServiceClient accountAPIV2.AccountService
	ProductConfig        *constant.ProductConfig
}

// NewCreditCardStrategy creates a new credit card strategy
func NewCreditCardStrategy(accountService accountAPIV2.AccountService) *CreditCardStrategy {
	return &CreditCardStrategy{
		AccountServiceClient: accountService,
		ProductConfig:        constant.GetProductConfig(constant.MYCreditCard),
	}
}

// ValidateAccount performs credit account validation for CCC cards
func (s *CreditCardStrategy) ValidateAccount(ctx context.Context, accountID string) error {
	// For CCC cards, validate the account exists and is active
	// Account information is already available from account service response
	accountReq := &accountAPIV2.GetAccountRequest{AccountID: accountID}
	accountResp, err := s.AccountServiceClient.GetAccountDetailsByAccountID(ctx, accountReq)
	if err != nil {
		return fmt.Errorf("failed to validate credit account: %w", err)
	}

	if accountResp.Account.Status != accountAPIV2.AccountStatus_ACTIVE {
		return fmt.Errorf("credit account is not active")
	}

	return nil
}

// GetRiskCheckParams returns credit card specific risk check parameters
func (s *CreditCardStrategy) GetRiskCheckParams(ctx context.Context, card *storage.Card) *riskAPI.ValidatePartnerTxnRequest {
	payload := &riskAPI.CardActivityPayload{
		CustomerID: card.UserID,
		DeviceID:   commonCtx.GetDeviceID(ctx),
		IPAddress:  commonCtx.GetTrueClientIP(ctx),
		CardID:     card.CardID,
		ReasonCode: "NEW_VIRTUAL CARD",
		CreatedAt:  card.CreatedAt,
	}

	payloadByte, _ := json.Marshal(payload)
	return &riskAPI.ValidatePartnerTxnRequest{
		TransactionType: riskAPI.CardActivity,
		Partner:         riskAPI.DIGIBANK,
		Payload:         (*json.RawMessage)(&payloadByte),
	}
}

// GetDefaultTransactionLimits returns default limits for credit cards
func (s *CreditCardStrategy) GetDefaultTransactionLimits() map[string]int64 {
	limitNames := s.ProductConfig.TxnLimitNames
	return map[string]int64{
		limitNames.Contactless: 50000,  // 500 MYR
		limitNames.Online:      100000, // 1000 MYR
		limitNames.PinAndPay:   50000,  // 500 MYR
		limitNames.ATM:         30000,  // 300 MYR
	}
}

// GetCardIssuanceParams returns credit card specific issuance parameters
func (s *CreditCardStrategy) GetCardIssuanceParams(ctx context.Context, card *storage.Card, baseRequest *euronetAdapterAPI.CardIssuanceRequest) *euronetAdapterAPI.CardIssuanceRequest {
	baseRequest.ProductVariant = string(constant.MYCreditCard)
	//baseRequest.VendorIdentifier = s.ProductConfig.VendorIdentifier // "CCM"
	return baseRequest
}

// GetCardLimits returns credit card count limits
func (s *CreditCardStrategy) GetCardLimits() *CardLimits {
	return &CardLimits{
		MaxActiveCards: 2,
		MaxTotalCards:  99,
	}
}

// GetProductConfig returns the credit card product configuration
func (s *CreditCardStrategy) GetProductConfig() *constant.ProductConfig {
	return s.ProductConfig
}
