package strategy

import (
	"context"
	"encoding/json"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/enquiry"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	riskAPI "gitlab.myteksi.net/dakota/client-sdk/risk-service"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	accountAPIV2 "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// DebitCardStrategy implements ProductStrategy for debit cards
type DebitCardStrategy struct {
	AccountServiceClient accountAPIV2.AccountService
	ProductConfig        *constant.ProductConfig
}

// NewDebitCardStrategy creates a new debit card strategy
func NewDebitCardStrategy(accountService accountAPIV2.AccountService) *DebitCardStrategy {
	return &DebitCardStrategy{
		AccountServiceClient: accountService,
		ProductConfig:        constant.GetProductConfig(constant.MYDebitCard),
	}
}

// ValidateAccount performs standard CASA account validation for debit cards
func (s *DebitCardStrategy) ValidateAccount(ctx context.Context, accountID string) error {
	return enquiry.CheckCasaAccount(ctx, accountID, s.AccountServiceClient)
}

// GetRiskCheckParams returns debit card specific risk check parameters
func (s *DebitCardStrategy) GetRiskCheckParams(ctx context.Context, card *storage.Card) *riskAPI.ValidatePartnerTxnRequest {
	payload := &riskAPI.CardActivityPayload{
		CustomerID: card.UserID,
		DeviceID:   commonCtx.GetDeviceID(ctx),
		IPAddress:  commonCtx.GetTrueClientIP(ctx),
		CardID:     card.CardID,
		ReasonCode: "NEW_VIRTUAL CARD",
		CreatedAt:  card.CreatedAt,
	}

	payloadByte, _ := json.Marshal(payload)
	return &riskAPI.ValidatePartnerTxnRequest{
		TransactionType: riskAPI.CardActivity,
		Partner:         riskAPI.DIGIBANK,
		Payload:         (*json.RawMessage)(&payloadByte),
	}
}

// GetDefaultTransactionLimits returns default limits for debit cards
func (s *DebitCardStrategy) GetDefaultTransactionLimits() map[string]int64 {
	limitNames := s.ProductConfig.TxnLimitNames
	return map[string]int64{
		limitNames.Contactless: 25000,  // 250 MYR
		limitNames.Online:      50000,  // 500 MYR
		limitNames.PinAndPay:   25000,  // 250 MYR
		limitNames.ATM:         150000, // 1500 MYR
	}
}

// GetCardIssuanceParams returns debit card specific issuance parameters
func (s *DebitCardStrategy) GetCardIssuanceParams(ctx context.Context, card *storage.Card, baseRequest *euronetAdapterAPI.CardIssuanceRequest) *euronetAdapterAPI.CardIssuanceRequest {
	baseRequest.ProductVariant = string(constant.MYDebitCard)
	//baseRequest.VendorIdentifier = s.ProductConfig.VendorIdentifier // "MDP"
	return baseRequest
}

// GetCardLimits returns debit card count limits
func (s *DebitCardStrategy) GetCardLimits() *CardLimits {
	return &CardLimits{
		MaxActiveCards: 2,
		MaxTotalCards:  99,
	}
}

// GetProductConfig returns the debit card product configuration
func (s *DebitCardStrategy) GetProductConfig() *constant.ProductConfig {
	return s.ProductConfig
}
