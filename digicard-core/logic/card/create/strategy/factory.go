package strategy

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	accountAPIV2 "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

// StrategyFactory creates product strategies based on product variant
type StrategyFactory struct {
	AccountServiceClient accountAPIV2.AccountService
}

// NewStrategyFactory creates a new strategy factory
func NewStrategyFactory(accountService accountAPIV2.AccountService) *StrategyFactory {
	return &StrategyFactory{
		AccountServiceClient: accountService,
	}
}

// CreateStrategy creates the appropriate strategy for the given product variant
func (f *StrategyFactory) CreateStrategy(productVariant constant.ProductVariant) (ProductStrategy, error) {
	switch productVariant {
	case constant.MYDebitCard:
		return NewDebitCardStrategy(f.AccountServiceClient), nil
	case constant.MYCreditCard:
		return NewCreditCardStrategy(f.AccountServiceClient), nil
	default:
		return nil, fmt.Errorf("unsupported product variant: %s", productVariant)
	}
}

// GetStrategyForAccount creates a strategy based on account's product variant
func (f *StrategyFactory) GetStrategyForAccount(ctx context.Context, accountID string) (ProductStrategy, constant.ProductVariant, error) {
	// Get account details to determine product variant
	accountReq := &accountAPIV2.GetAccountRequest{AccountID: accountID}
	accountResp, err := f.AccountServiceClient.GetAccountDetailsByAccountID(ctx, accountReq)
	if err != nil {
		return nil, "", fmt.Errorf("failed to get account details: %w", err)
	}

	productVariant := constant.ProductVariant(accountResp.Account.ProductVariantID)
	strategy, err := f.CreateStrategy(productVariant)
	if err != nil {
		return nil, "", err
	}

	return strategy, productVariant, nil
}
