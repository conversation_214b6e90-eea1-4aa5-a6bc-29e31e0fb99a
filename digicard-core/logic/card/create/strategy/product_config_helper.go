package strategy

import (
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
)

// filterCardsByProductVariant filters cards by product variant
func FilterCardsByProductVariant(cards []*storage.Card, productVariant string) []*storage.Card {
	var filtered []*storage.Card
	for _, card := range cards {
		if card.ProductVariant == productVariant {
			filtered = append(filtered, card)
		}
	}
	return filtered
}

// GetProductVariantFromCard extracts product variant from card
func GetProductVariantFromCard(card *storage.Card) constant.ProductVariant {
	return constant.ProductVariant(card.ProductVariant)
}

// IsProductVariantSupported checks if a product variant is supported
func IsProductVariantSupported(productVariant constant.ProductVariant) bool {
	return constant.GetProductConfig(productVariant) != nil
}
