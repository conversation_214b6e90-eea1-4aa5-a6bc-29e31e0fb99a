package strategy

import (
	"context"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	riskAPI "gitlab.myteksi.net/dakota/client-sdk/risk-service"
)

// ProductStrategy defines the interface for product-specific card creation behaviors
type ProductStrategy interface {
	// ValidateAccount performs product-specific account validation
	ValidateAccount(ctx context.Context, accountID string) error

	// GetRiskCheckParams returns product-specific risk check parameters
	GetRiskCheckParams(ctx context.Context, card *storage.Card) *riskAPI.ValidatePartnerTxnRequest

	// GetDefaultTransactionLimits returns product-specific default transaction limits
	GetDefaultTransactionLimits() map[string]int64

	// GetCardIssuanceParams returns product-specific card issuance parameters
	GetCardIssuanceParams(ctx context.Context, card *storage.Card, baseRequest *euronetAdapterAPI.CardIssuanceRequest) *euronetAdapterAPI.CardIssuanceRequest

	// GetCardLimits returns product-specific card count limits
	GetCardLimits() *CardLimits

	// GetProductConfig returns the product configuration
	GetProductConfig() *constant.ProductConfig
}

// CardLimits defines card count limits for a product
type CardLimits struct {
	MaxActiveCards int
	MaxTotalCards  int
}
