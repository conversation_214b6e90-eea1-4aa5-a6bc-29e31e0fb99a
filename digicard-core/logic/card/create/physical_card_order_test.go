package createcard

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"testing"
	"time"

	"gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/dbmy/payment/common/transaction-workflow/charge"
	pigeon "gitlab.myteksi.net/dbmy/pigeon/api/mock"

	digiCard "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/design/test"
	"gitlab.myteksi.net/snd/streamsdk/kafka/kafkawriter"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"
	riskAPI "gitlab.myteksi.net/dakota/client-sdk/risk-service"

	euronetAdapterAPI "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	euronetAdapterMock "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api/mock"

	customerMasterDBMYAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	customerMasterDBMYMock "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/dto"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
)

var (
	cardDesignID   = uuid.NewString()
	cardID         = uuid.NewString()
	activationCode = "ABCD3"
	customerID     = uuid.NewString()
	displayName    = "BAT MAN"
)

func TestPhysicalCardOrder(t *testing.T) {
	featureFlags := &config.FeatureFlags{
		EnablePhysicalCardCreationRiskCheck: true,
	}
	mockRiskCheckClient := &riskAPI.MockRiskService{}
	mockEuronetAdapter := &euronetAdapterMock.EuronetAdapter{}
	mockCustomerMaster := &customerMasterDBMYMock.CustomerMaster{}
	mockKafkaWriter := kafkawriter.MockClient{}
	mockPigeon := &pigeon.Pigeon{}
	mockPublisher := &digiCard.PublisherImpl{
		KafkaWriter: &mockKafkaWriter,
	}

	client := &PhysicalCardOrderClientImpl{
		FeatureFlags:         featureFlags,
		RiskServiceClient:    mockRiskCheckClient,
		EuronetAdapterClient: mockEuronetAdapter,
		CustomerMasterClient: mockCustomerMaster,
		Publisher:            mockPublisher,
		PigeonClient:         mockPigeon,
	}

	cardDesign := getCardDesign()
	mockCardDesignDAO := &storage.MockICardDesignDAO{}
	storage.CardDesignDao = mockCardDesignDAO

	mockRedis := test.SetMockRedisClient()
	test.SetMockDataForRedisClient(mockRedis)

	card := getCard()
	mockCardDAO := &storage.MockICardDAO{}
	storage.CardDao = mockCardDAO

	riskCheckSuccess := getRiskCheckAllowResponse()
	enaSuccessResp := getENASuccessResponse()
	customerMasterResp := getCustomerMasterResponse()

	req := &api.CreateCardRequest{
		CardID:         cardID,
		CardDesignId:   cardDesignID,
		DisplayName:    displayName,
		Type:           string(constant.PhysicalCard),
		IdempotencyKey: uuid.NewString(),
		AccountID:      uuid.NewString(),
	}

	logic.GetUserID = func(ctx context.Context) string {
		return customerID
	}

	t.Run("Valid flow", func(t *testing.T) {
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.CardDesign{cardDesign}, nil).Times(3)
		mockCardDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		mockRiskCheckClient.On("ValidatePartnerTxn", mock.Anything, mock.Anything).Return(riskCheckSuccess, nil).Once()
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(customerMasterResp, nil).Once()
		mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(enaSuccessResp, nil).Once()
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		mockCardDesignDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		mockKafkaWriter.On("Save", mock.Anything).Return(nil).Once()
		mockPigeon.On("Push", mock.Anything, mock.Anything).Return(nil, nil).Once()
		cardResp, err := client.PhysicalCardOrder(context.Background(), req)
		assert.NotNil(t, cardResp)
		assert.Nil(t, err)
	})
	t.Run("validateCardDesign Failed", func(t *testing.T) {
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		cardResp, err := client.PhysicalCardOrder(context.Background(), req)
		assert.Nil(t, cardResp)
		assert.NotNil(t, err)
	})
	t.Run("fetchCard Failed", func(t *testing.T) {
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.CardDesign{cardDesign}, nil).Once()
		mockCardDAO.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		cardResp, err := client.PhysicalCardOrder(context.Background(), req)
		assert.Nil(t, cardResp)
		assert.NotNil(t, err)
	})
	t.Run("validateCard Failed ", func(t *testing.T) {
		tempCard := *card
		tempCard.Status = string(constant.CardStatusRetired)
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.CardDesign{cardDesign}, nil).Once()
		mockCardDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{&tempCard}, nil).Once()
		cardResp, err := client.PhysicalCardOrder(context.Background(), req)
		assert.Nil(t, cardResp)
		assert.NotNil(t, err)
	})
	t.Run("physicalCardRiskCheck failed", func(t *testing.T) {
		expectedErr := servus.ServiceError{
			Code:     string(api.ResourceConflict),
			Message:  api.ResourceConflict.ErrorMessage(),
			HTTPCode: api.ResourceConflict.HTTPStatusCode(),
		}
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.CardDesign{cardDesign}, nil).Once()
		mockCardDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		mockRiskCheckClient.On("ValidatePartnerTxn", mock.Anything, mock.Anything).Return(nil, expectedErr).Once()
		cardResp, err := client.PhysicalCardOrder(context.Background(), req)
		assert.Nil(t, cardResp)
		assert.NotNil(t, err)
	})
	t.Run("issuePhysicalCard Failed", func(t *testing.T) {
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.CardDesign{cardDesign}, nil).Once()
		mockCardDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		mockRiskCheckClient.On("ValidatePartnerTxn", mock.Anything, mock.Anything).Return(riskCheckSuccess, nil).Once()
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(customerMasterResp, nil).Once()
		mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(nil, api.DefaultInternalServerError).Once()
		cardResp, err := client.PhysicalCardOrder(context.Background(), req)
		assert.Nil(t, cardResp)
		assert.NotNil(t, err)
	})
	t.Run("updateCard failed", func(t *testing.T) {
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.CardDesign{cardDesign}, nil).Once()
		mockCardDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		mockRiskCheckClient.On("ValidatePartnerTxn", mock.Anything, mock.Anything).Return(riskCheckSuccess, nil).Once()
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(customerMasterResp, nil).Once()
		mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(enaSuccessResp, nil).Once()
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(data.ErrNotSupported).Once()
		cardResp, err := client.PhysicalCardOrder(context.Background(), req)
		assert.Nil(t, cardResp)
		assert.NotNil(t, err)
	})
	t.Run("UpdateCardDesignStockCount Failed", func(t *testing.T) {
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.CardDesign{cardDesign}, nil).Times(2)
		mockCardDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		mockRiskCheckClient.On("ValidatePartnerTxn", mock.Anything, mock.Anything).Return(riskCheckSuccess, nil).Once()
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(customerMasterResp, nil).Once()
		mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(enaSuccessResp, nil).Once()
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		mockCardDesignDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(data.ErrNotSupported).Once()
		mockKafkaWriter.On("Save", mock.Anything).Return(nil).Once()
		mockPigeon.On("Push", mock.Anything, mock.Anything).Return(nil, nil).Once()
		cardResp, err := client.PhysicalCardOrder(context.Background(), req)
		assert.NotNil(t, cardResp)
		assert.Nil(t, err)
	})
	t.Run("pushPhysicalCardOrderedNotification failed", func(t *testing.T) {
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.CardDesign{cardDesign}, nil).Times(3)
		mockCardDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		mockRiskCheckClient.On("ValidatePartnerTxn", mock.Anything, mock.Anything).Return(riskCheckSuccess, nil).Once()
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(customerMasterResp, nil).Once()
		mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(enaSuccessResp, nil).Once()
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		mockCardDesignDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		mockKafkaWriter.On("Save", mock.Anything).Return(nil).Once()
		mockPigeon.On("Push", mock.Anything, mock.Anything).Return(nil, errors.New("push noti failed")).Once()
		cardResp, err := client.PhysicalCardOrder(context.Background(), req)
		assert.NotNil(t, cardResp)
		assert.Nil(t, err)
	})
}

func TestValidateCardDesign(t *testing.T) {
	client := &PhysicalCardOrderClientImpl{}
	cardDesign := getCardDesign()
	mockCardDesignDAO := &storage.MockICardDesignDAO{}
	storage.CardDesignDao = mockCardDesignDAO
	mockRedis := test.SetMockRedisClient()
	test.SetMockDataForRedisClient(mockRedis)
	t.Run("Valid flow", func(t *testing.T) {
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.CardDesign{cardDesign}, nil).Once()
		cardDesignResp, err := client.validateCardDesign(context.Background(), cardDesignID)
		assert.NotNil(t, cardDesignResp)
		assert.Equal(t, cardDesignID, cardDesignResp.CardDesignID)
		assert.Nil(t, err)
	})
	t.Run("FetchCardDesignByCardDesignID Failed", func(t *testing.T) {
		expectedErr := servus.ServiceError{
			Code:     string(api.InternalServerError),
			Message:  "Failed to retrieve the card design",
			HTTPCode: api.InternalServerError.HTTPStatusCode(),
		}
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		cardDesignResp, err := client.validateCardDesign(context.Background(), cardDesignID)
		assert.Nil(t, cardDesignResp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("Stock count is less than benchmark", func(t *testing.T) {
		cardDesign.StockCount = 9
		expectedErr := servus.ServiceError{
			Code:     string(api.ResourceConflict),
			Message:  "Selected card design is not available.",
			HTTPCode: api.ResourceConflict.HTTPStatusCode(),
		}
		mockCardDesignDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.CardDesign{cardDesign}, nil).Once()
		cardDesignResp, err := client.validateCardDesign(context.Background(), cardDesignID)
		assert.Nil(t, cardDesignResp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func TestFetchCard(t *testing.T) {
	client := &PhysicalCardOrderClientImpl{}
	card := getCard()
	mockCardDAO := &storage.MockICardDAO{}
	storage.CardDao = mockCardDAO
	t.Run("Valid flow", func(t *testing.T) {
		mockCardDAO.On("Find", mock.Anything, mock.Anything).Return([]*storage.Card{card}, nil).Once()
		cardResp, err := client.fetchCard(context.Background(), cardID)
		assert.NotNil(t, cardResp)
		assert.Equal(t, cardID, cardResp.CardID)
		assert.Nil(t, err)
	})
	t.Run("No card found", func(t *testing.T) {
		expectedErr := servus.ServiceError{
			Code:     string(api.ResourceConflict),
			Message:  "invalid cardID.",
			HTTPCode: api.ResourceConflict.HTTPStatusCode(),
		}
		mockCardDAO.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		cardResp, err := client.fetchCard(context.Background(), cardID)
		assert.Nil(t, cardResp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("storage.CardDao.Find Failed", func(t *testing.T) {
		expectedErr := api.DefaultInternalServerError
		mockCardDAO.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		cardResp, err := client.fetchCard(context.Background(), cardID)
		assert.Nil(t, cardResp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func TestValidateCard(t *testing.T) {
	client := &PhysicalCardOrderClientImpl{}
	card := getCard()
	t.Run("Valid flow", func(t *testing.T) {
		card.OrderStatus = ""
		err := client.validateCard(context.Background(), card, customerID)
		assert.Nil(t, err)
	})
	t.Run("Invalid user", func(t *testing.T) {
		card.UserID = uuid.NewString()
		err := client.validateCard(context.Background(), card, customerID)
		assert.NotNil(t, err)
		assert.Equal(t, logic.ErrInvalidUserCard, err)
	})
	t.Run("Invalid status", func(t *testing.T) {
		card.UserID = customerID
		card.Status = string(constant.CardStatusRetired)
		expectedErr := &servus.ServiceError{
			Code:     string(api.ResourceConflict),
			Message:  "card is not in active or locked status.",
			HTTPCode: api.ResourceConflict.HTTPStatusCode(),
		}
		err := client.validateCard(context.Background(), card, customerID)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("card is already ordered", func(t *testing.T) {
		card.UserID = customerID
		card.Status = string(constant.CardStatusActive)
		card.OrderStatus = string(constant.OrderStatusOrdered)
		expectedErr := &servus.ServiceError{
			Code:     string(api.ResourceConflict),
			Message:  "physical card has already been ordered.",
			HTTPCode: api.ResourceConflict.HTTPStatusCode(),
		}
		err := client.validateCard(context.Background(), card, customerID)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func TestShouldRejectPhysicalCardOrder(t *testing.T) {
	client := &PhysicalCardOrderClientImpl{}
	card := getCard()
	t.Run("Valid flow", func(t *testing.T) {
		isTrue := client.shouldRejectPhysicalCardOrder(card)
		assert.False(t, isTrue)
	})
	t.Run("Invalid flow", func(t *testing.T) {
		card.Status = string(constant.CardStatusRetired)
		isTrue := client.shouldRejectPhysicalCardOrder(card)
		assert.True(t, isTrue)
	})
}

func TestIsReasonCodeEmptyOrBankBlock(t *testing.T) {
	client := &PhysicalCardOrderClientImpl{}
	t.Run("Valid flow", func(t *testing.T) {
		isTrue := client.isReasonCodeEmptyOrBankBlock(string(constant.ReasonCodeBankBlock))
		assert.True(t, isTrue)
	})
	t.Run("Invalid flow", func(t *testing.T) {
		isTrue := client.isReasonCodeEmptyOrBankBlock("NON-BLOCK")
		assert.False(t, isTrue)
	})
}

func TestConvertPhysicalCardCheckpointRequest(t *testing.T) {
	card := getCard()
	client := &PhysicalCardOrderClientImpl{}
	t.Run("Valid flow", func(t *testing.T) {
		riskCheckReq := client.convertPhysicalCardCheckpointRequest(context.Background(), card)
		assert.NotNil(t, riskCheckReq)
	})
}

func TestPhysicalCardRiskCheck(t *testing.T) {
	mockRiskCheckClient := &riskAPI.MockRiskService{}
	featureFlags := &config.FeatureFlags{
		EnablePhysicalCardCreationRiskCheck: true,
	}
	client := &PhysicalCardOrderClientImpl{
		FeatureFlags:      featureFlags,
		RiskServiceClient: mockRiskCheckClient,
	}
	card := getCard()
	riskCheckSuccess := getRiskCheckAllowResponse()
	riskCheckDeny := getRiskCheckDenyResponse()
	riskCheckMaybe := getRiskCheckMaybeResponse()
	t.Run("Valid flow", func(t *testing.T) {
		mockRiskCheckClient.On("ValidatePartnerTxn", mock.Anything, mock.Anything).Return(riskCheckSuccess, nil).Once()
		isRiskCheckPassed, err := client.physicalCardRiskCheck(context.Background(), card)
		assert.True(t, isRiskCheckPassed)
		assert.Nil(t, err)
	})
	t.Run("Valid flow with risk deny", func(t *testing.T) {
		mockRiskCheckClient.On("ValidatePartnerTxn", mock.Anything, mock.Anything).Return(riskCheckDeny, nil).Once()
		isRiskCheckPassed, err := client.physicalCardRiskCheck(context.Background(), card)
		expectedErr := &servus.ServiceError{
			Code:     "PHYSICAL_RISK_CHECK_FAILED",
			Message:  "physical card risk check failed",
			HTTPCode: api.Forbidden.HTTPStatusCode(),
		}
		assert.False(t, isRiskCheckPassed)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("Valid flow with risk maybe", func(t *testing.T) {
		mockRiskCheckClient.On("ValidatePartnerTxn", mock.Anything, mock.Anything).Return(riskCheckMaybe, nil).Once()
		isRiskCheckPassed, err := client.physicalCardRiskCheck(context.Background(), card)
		expectedErr := api.DefaultInternalServerError
		assert.False(t, isRiskCheckPassed)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("RiskServiceClient.ValidatePartnerTxn Failed with 500", func(t *testing.T) {
		mockRiskCheckClient.On("ValidatePartnerTxn", mock.Anything, mock.Anything).Return(nil, api.DefaultInternalServerError).Once()
		isRiskCheckPassed, err := client.physicalCardRiskCheck(context.Background(), card)
		assert.True(t, isRiskCheckPassed)
		assert.Nil(t, err)
	})
	t.Run("RiskServiceClient.ValidatePartnerTxn Failed with other than 500", func(t *testing.T) {
		expectedErr := servus.ServiceError{
			Code:     string(api.ResourceConflict),
			Message:  api.ResourceConflict.ErrorMessage(),
			HTTPCode: api.ResourceConflict.HTTPStatusCode(),
		}
		mockRiskCheckClient.On("ValidatePartnerTxn", mock.Anything, mock.Anything).Return(nil, expectedErr).Once()
		isRiskCheckPassed, err := client.physicalCardRiskCheck(context.Background(), card)
		assert.False(t, isRiskCheckPassed)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
	t.Run("Valid flow with FeatureFlags.EnablePhysicalCardCreationRiskCheck is false", func(t *testing.T) {
		featureFlags.EnablePhysicalCardCreationRiskCheck = false
		isRiskCheckPassed, err := client.physicalCardRiskCheck(context.Background(), card)
		assert.True(t, isRiskCheckPassed)
		assert.Nil(t, err)
	})
}

func TestIssuePhysicalCardOrder(t *testing.T) {
	mockEuronetAdapter := &euronetAdapterMock.EuronetAdapter{}
	mockCustomerMaster := &customerMasterDBMYMock.CustomerMaster{}
	client := &PhysicalCardOrderClientImpl{
		EuronetAdapterClient: mockEuronetAdapter,
		CustomerMasterClient: mockCustomerMaster,
	}
	enaSuccessResp := getENASuccessResponse()
	customerMasterResp := getCustomerMasterResponse()
	enaFailedResp := getENAFailedResponse()
	t.Run("Valid flow", func(t *testing.T) {
		cardStorage := &storage.Card{
			CardID:      cardID,
			UserID:      customerID,
			DisplayName: displayName,
		}
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(customerMasterResp, nil).Once()
		mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(enaSuccessResp, nil).Once()
		respState, err := client.issuePhysicalCard(context.Background(), cardStorage, activationCode)
		assert.NotNil(t, respState)
		assert.Equal(t, constant.Success, respState.Status)
		assert.Nil(t, err)
	})
	t.Run("Valid flow with failed status", func(t *testing.T) {
		cardStorage := &storage.Card{
			CardID:      cardID,
			UserID:      customerID,
			DisplayName: displayName,
		}
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(customerMasterResp, nil).Once()
		mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(enaFailedResp, nil).Once()
		respState, err := client.issuePhysicalCard(context.Background(), cardStorage, activationCode)
		assert.NotNil(t, respState)
		assert.Equal(t, constant.Failed, respState.Status)
		assert.Nil(t, err)
	})
	t.Run("Valid flow with status neither success nor failed", func(t *testing.T) {
		cardStorage := &storage.Card{
			CardID:      cardID,
			UserID:      customerID,
			DisplayName: displayName,
		}
		enaProcessingResp := *enaFailedResp
		enaProcessingResp.Data.Status = constant.Processing
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(customerMasterResp, nil).Once()
		mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(&enaProcessingResp, nil).Once()
		respState, err := client.issuePhysicalCard(context.Background(), cardStorage, activationCode)
		assert.Nil(t, respState)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
	t.Run("getDeliveryInformation Failed", func(t *testing.T) {
		cardStorage := &storage.Card{
			CardID:      cardID,
			UserID:      customerID,
			DisplayName: displayName,
		}
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(nil, api.DefaultInternalServerError).Once()
		respState, err := client.issuePhysicalCard(context.Background(), cardStorage, activationCode)
		assert.Nil(t, respState)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
	t.Run("EuronetAdapterClient.CardIssuance Failed", func(t *testing.T) {
		cardStorage := &storage.Card{
			CardID:      cardID,
			UserID:      customerID,
			DisplayName: displayName,
		}
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(customerMasterResp, nil).Once()
		mockEuronetAdapter.On("CardIssuance", mock.Anything, mock.Anything).Return(nil, api.DefaultInternalServerError).Once()
		respState, err := client.issuePhysicalCard(context.Background(), cardStorage, activationCode)
		assert.Nil(t, respState)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
}

func TestGetDeliveryInformation(t *testing.T) {
	mockCustomerMaster := &customerMasterDBMYMock.CustomerMaster{}
	client := &PhysicalCardOrderClientImpl{
		CustomerMasterClient: mockCustomerMaster,
	}
	customerMasterResp := getCustomerMasterResponse()
	t.Run("Valid flow", func(t *testing.T) {
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(customerMasterResp, nil).Once()
		mailingAddress, phoneNumber, err := client.getDeliveryInformation(context.Background(), customerID)
		assert.NotNil(t, mailingAddress)
		assert.NotNil(t, phoneNumber)
		assert.Nil(t, err)
	})
	t.Run("Customer.Data.Unmarshal Failed", func(t *testing.T) {
		tempData := *customerMasterResp
		tempData.Customer.Data = json.RawMessage(`[]`)
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(customerMasterResp, nil).Once()
		mailingAddress, phoneNumber, err := client.getDeliveryInformation(context.Background(), customerID)
		assert.Nil(t, mailingAddress)
		assert.NotNil(t, err)
		assert.Equal(t, "", phoneNumber)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
	t.Run("CustomerMasterClient.GetCustomer Failed", func(t *testing.T) {
		mockCustomerMaster.On("GetCustomer", mock.Anything, mock.Anything).Return(nil, api.DefaultInternalServerError).Once()
		mailingAddress, phoneNumber, err := client.getDeliveryInformation(context.Background(), customerID)
		assert.Nil(t, mailingAddress)
		assert.NotNil(t, err)
		assert.Equal(t, "", phoneNumber)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
}

func TestUpdateActivationCode(t *testing.T) {
	card := getCard()
	client := &PhysicalCardOrderClientImpl{}
	t.Run("Valid flow", func(t *testing.T) {
		postScriptJSON, err := client.updateActivationCode(context.Background(), card.PostScript, activationCode)
		cardPostScript := &dto.CardPostScript{}
		_ = json.Unmarshal(postScriptJSON, cardPostScript)
		assert.NotNil(t, postScriptJSON)
		assert.Equal(t, activationCode, cardPostScript.ActivationCode)
		assert.Nil(t, err)
	})
	t.Run("Card.PostScript.Unmarshal Failed", func(t *testing.T) {
		tempCard := *card
		tempCard.PostScript = json.RawMessage(`[]`)
		postScriptJSON, err := client.updateActivationCode(context.Background(), tempCard.PostScript, activationCode)
		assert.Nil(t, postScriptJSON)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
}

func TestUpdateCard(t *testing.T) {
	card := getCard()
	mockCardDAO := &storage.MockICardDAO{}
	storage.CardDao = mockCardDAO
	client := &PhysicalCardOrderClientImpl{}
	t.Run("Valid flow", func(t *testing.T) {
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		updatedCard, err := client.updateCard(context.Background(), constant.Success, activationCode, card)
		cardPostScript := &dto.CardPostScript{}
		_ = json.Unmarshal(updatedCard.PostScript, cardPostScript)
		assert.NotNil(t, updatedCard)
		assert.Equal(t, updatedCard.OrderStatus, string(constant.OrderStatusOrdered))
		assert.Equal(t, activationCode, cardPostScript.ActivationCode)
		assert.Nil(t, err)
	})
	t.Run("Valid flow with Failed status", func(t *testing.T) {
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
		updatedCard, err := client.updateCard(context.Background(), constant.Failed, activationCode, card)
		cardPostScript := &dto.CardPostScript{}
		_ = json.Unmarshal(updatedCard.PostScript, cardPostScript)
		assert.NotNil(t, updatedCard)
		assert.Equal(t, updatedCard.OrderStatus, string(constant.OrderStatusFailed))
		assert.NotEqual(t, activationCode, cardPostScript.ActivationCode)
		assert.Nil(t, err)
	})
	t.Run("storage.CardDao.UpdateEntity Failed", func(t *testing.T) {
		mockCardDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(data.ErrNotSupported).Once()
		updatedCard, err := client.updateCard(context.Background(), constant.Success, activationCode, card)
		assert.Nil(t, updatedCard)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
	t.Run("updateActivationCode Failed", func(t *testing.T) {
		card.PostScript = json.RawMessage(`[]`)
		updatedCard, err := client.updateCard(context.Background(), constant.Success, activationCode, card)
		assert.Nil(t, updatedCard)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
	})
}

func TestPhysicalCardOrderConvertResponse(t *testing.T) {
	client := &PhysicalCardOrderClientImpl{}
	respStateSuccess := &responseState{Status: constant.Success}
	respStateFail := &responseState{Status: constant.Failed, StatusReason: "ENTITY_LINKED", StatusReasonDescription: "Entity is linked"}
	card := getCard()
	cardDesign := getCardDesign()
	t.Run("Valid flow", func(t *testing.T) {
		resp := client.convertResponse(respStateSuccess, card, cardDesign)
		assert.NotNil(t, resp)
		assert.Equal(t, resp.Status, respStateSuccess.Status)
	})
	t.Run("Invalid flow", func(t *testing.T) {
		resp := client.convertResponse(respStateFail, card, cardDesign)
		assert.NotNil(t, resp)
		assert.Equal(t, resp.Status, respStateFail.Status)
		assert.Equal(t, resp.StatusReason, respStateFail.StatusReason)
	})
}

func getCard() *storage.Card {
	card := &storage.Card{
		CardID:                  cardID,
		Status:                  string(constant.CardStatusActive),
		OrderStatus:             "",
		PhysicalCardOrderedDate: time.Now(),
		CardDesignID:            cardDesignID,
		PostScript:              json.RawMessage(`{}`),
		UserID:                  customerID,
	}
	return card
}

func getCardDesign() *storage.CardDesign {
	cardDesign := &storage.CardDesign{
		CardDesignID: cardDesignID,
		Color:        "GREEN",
		FrontImage:   "FRONT.jpg",
		BackImage:    "BACK.jpg",
		StockCount:   100,
	}
	return cardDesign
}

func getCustomerMasterResponse() *customerMasterDBMYAPI.GetCustomerResponse {
	contactType, email, phone := customerMasterDBMYAPI.ContactType("PRIMARY"), "<EMAIL>", "+6591234567"
	addressType, street, block, unit, postal, city, state, country :=
		customerMasterDBMYAPI.AddressType("MAILING"), "street test", "123", "#1-123", "651234", "SGD", "SGD", "SG"
	customerData := &customerMasterDBMYAPI.Customer{
		Contacts: []*customerMasterDBMYAPI.ContactDetail{
			{
				ContactType: &contactType,
				Email:       &email,
				PhoneNumber: &phone,
			},
		},
		Addresses: []*customerMasterDBMYAPI.Address{
			{
				AddressType: &addressType,
				Street:      &street,
				Block:       &block,
				Unit:        &unit,
				City:        &city,
				State:       &state,
				Country:     &country,
				PostalCode:  &postal,
			},
		},
	}
	customerBytes, _ := json.Marshal(customerData)
	testGetCustomerResponse := &customerMasterDBMYAPI.GetCustomerResponse{Customer: &customerMasterDBMYAPI.CustomerData{Data: customerBytes}}
	return testGetCustomerResponse
}

func getENASuccessResponse() *euronetAdapterAPI.CardIssuanceResponse {
	return &euronetAdapterAPI.CardIssuanceResponse{
		Data: &euronetAdapterAPI.CardResource{
			Status:             constant.Success,
			StatusReason:       "",
			StatusMessage:      "",
			MaskedCardNumber:   "123456****9102",
			ExpiryDate:         "03/26",
			Cvv:                "233",
			ProxyNumber:        "87654321",
			CardSequenceNumber: 0,
		},
	}
}

func getENAFailedResponse() *euronetAdapterAPI.CardIssuanceResponse {
	return &euronetAdapterAPI.CardIssuanceResponse{
		Data: &euronetAdapterAPI.CardResource{
			Status:        constant.Failed,
			StatusReason:  "error",
			StatusMessage: "",
		},
	}
}

func getRiskCheckAllowResponse() *riskAPI.ValidatePartnerTxnResponse {
	return &riskAPI.ValidatePartnerTxnResponse{
		StatusCode: riskAPI.Allow,
		Action:     riskAPI.Default,
	}
}

func getRiskCheckDenyResponse() *riskAPI.ValidatePartnerTxnResponse {
	return &riskAPI.ValidatePartnerTxnResponse{
		StatusCode: riskAPI.Deny,
		Action:     riskAPI.Pin,
	}
}

func getRiskCheckMaybeResponse() *riskAPI.ValidatePartnerTxnResponse {
	return &riskAPI.ValidatePartnerTxnResponse{
		StatusCode: riskAPI.MayBe,
		Action:     riskAPI.Pin,
	}
}

func TestPhysicalCardOrderClientImpl_issuePhysicalCardWorkflowFn(t *testing.T) {
	type fields struct {
		ChargeWorkflow       charge.IWorkflow
		PhysicalCardOrderFee config.PhysicalCardOrderFee
		EuronetAdapterClient euronetAdapterAPI.EuronetAdapter
		CustomerMasterClient customerMasterDBMYAPI.CustomerMaster
	}
	type args struct {
		ctx          context.Context
		transitionID string
		execData     workflowengine.ExecutionData
		params       interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    workflowengine.ExecutionData
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "Test 1 - happy path",
			fields: fields{
				ChargeWorkflow: func() charge.IWorkflow {
					mockChargeWorkflow := &charge.MockIWorkflow{}
					mockChargeWorkflow.On("Charge", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
					return mockChargeWorkflow
				}(),
				PhysicalCardOrderFee: config.PhysicalCardOrderFee{
					Amount: 100,
				},
				EuronetAdapterClient: func() euronetAdapterAPI.EuronetAdapter {
					mc := &euronetAdapterMock.EuronetAdapter{}
					mc.On("CardIssuance", mock.Anything, mock.Anything).Return(getENASuccessResponse(), nil)
					return mc
				}(),
				CustomerMasterClient: func() customerMasterDBMYAPI.CustomerMaster {
					mc := &customerMasterDBMYMock.CustomerMaster{}
					mc.On("GetCustomer", mock.Anything, mock.Anything).Return(getCustomerMasterResponse(), nil)
					return mc
				}(),
			},
			args: args{
				ctx:          context.Background(),
				transitionID: "test-transition-id",
				execData: &charge.ExecutionData{
					State:         0,
					ChargeDto:     nil,
					StreamMessage: nil,
					AdditionalData: func() json.RawMessage {
						marshaled, _ := json.Marshal(&LogicCreatePhysicalCardContext{
							CardStorage:    &storage.Card{},
							ActivationCode: "",
							ResponseState:  nil,
						})
						return marshaled
					}(),
				},
				params: nil,
			},
			want: &charge.ExecutionData{
				State:         charge.StInvokeFnSuccess,
				ChargeDto:     nil,
				StreamMessage: nil,
				AdditionalData: func() json.RawMessage {
					marshaled, _ := json.Marshal(&LogicCreatePhysicalCardContext{
						CardStorage:    &storage.Card{},
						ActivationCode: "",
						ResponseState:  &responseState{Status: constant.Success},
					})
					return marshaled
				}(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "Test 2 - euronet failed response",
			fields: fields{
				ChargeWorkflow: func() charge.IWorkflow {
					mockChargeWorkflow := &charge.MockIWorkflow{}
					mockChargeWorkflow.On("Charge", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
					return mockChargeWorkflow
				}(),
				PhysicalCardOrderFee: config.PhysicalCardOrderFee{
					Amount: 100,
				},
				EuronetAdapterClient: func() euronetAdapterAPI.EuronetAdapter {
					mc := &euronetAdapterMock.EuronetAdapter{}
					mc.On("CardIssuance", mock.Anything, mock.Anything).Return(getENAFailedResponse(), nil)
					return mc
				}(),
				CustomerMasterClient: func() customerMasterDBMYAPI.CustomerMaster {
					mc := &customerMasterDBMYMock.CustomerMaster{}
					mc.On("GetCustomer", mock.Anything, mock.Anything).Return(getCustomerMasterResponse(), nil)
					return mc
				}(),
			},
			args: args{
				ctx:          context.Background(),
				transitionID: "test-transition-id",
				execData: &charge.ExecutionData{
					State:         0,
					ChargeDto:     nil,
					StreamMessage: nil,
					AdditionalData: func() json.RawMessage {
						marshaled, _ := json.Marshal(&LogicCreatePhysicalCardContext{
							CardStorage:    &storage.Card{},
							ActivationCode: "",
							ResponseState:  nil,
						})
						return marshaled
					}(),
				},
				params: nil,
			},
			want: &charge.ExecutionData{
				State:         charge.StInvokeFnFailed,
				ChargeDto:     nil,
				StreamMessage: nil,
				AdditionalData: func() json.RawMessage {
					marshaled, _ := json.Marshal(&LogicCreatePhysicalCardContext{
						CardStorage:    &storage.Card{},
						ActivationCode: "",
						ResponseState:  &responseState{Status: constant.Failed, StatusReason: "error"},
					})
					return marshaled
				}(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "Customer master error",
			fields: fields{
				ChargeWorkflow: func() charge.IWorkflow {
					mockChargeWorkflow := &charge.MockIWorkflow{}
					mockChargeWorkflow.On("Charge", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
					return mockChargeWorkflow
				}(),
				PhysicalCardOrderFee: config.PhysicalCardOrderFee{
					Amount: 100,
				},
				EuronetAdapterClient: func() euronetAdapterAPI.EuronetAdapter {
					mc := &euronetAdapterMock.EuronetAdapter{}
					return mc
				}(),
				CustomerMasterClient: func() customerMasterDBMYAPI.CustomerMaster {
					mc := &customerMasterDBMYMock.CustomerMaster{}
					mc.On("GetCustomer", mock.Anything, mock.Anything).Return(nil, errors.New("customer master error"))
					return mc
				}(),
			},
			args: args{
				ctx:          context.Background(),
				transitionID: "test-transition-id",
				execData: &charge.ExecutionData{
					State:         0,
					ChargeDto:     nil,
					StreamMessage: nil,
					AdditionalData: func() json.RawMessage {
						marshaled, _ := json.Marshal(&LogicCreatePhysicalCardContext{
							CardStorage:    &storage.Card{},
							ActivationCode: "",
							ResponseState:  nil,
						})
						return marshaled
					}(),
				},
				params: nil,
			},
			want: &charge.ExecutionData{
				State:         charge.StInvokeFnFailed,
				ChargeDto:     nil,
				StreamMessage: nil,
				AdditionalData: func() json.RawMessage {
					marshaled, _ := json.Marshal(&LogicCreatePhysicalCardContext{
						CardStorage:    &storage.Card{},
						ActivationCode: "",
						ResponseState:  nil,
					})
					return marshaled
				}(),
			},
			wantErr: nil,
		},
		{
			name: "Card issuance error",
			fields: fields{
				ChargeWorkflow: func() charge.IWorkflow {
					mockChargeWorkflow := &charge.MockIWorkflow{}
					mockChargeWorkflow.On("Charge", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
					return mockChargeWorkflow
				}(),
				PhysicalCardOrderFee: config.PhysicalCardOrderFee{
					Amount: 100,
				},
				EuronetAdapterClient: func() euronetAdapterAPI.EuronetAdapter {
					mc := &euronetAdapterMock.EuronetAdapter{}
					mc.On("CardIssuance", mock.Anything, mock.Anything).Return(nil, errors.New("card issuance error"))
					return mc
				}(),
				CustomerMasterClient: func() customerMasterDBMYAPI.CustomerMaster {
					mc := &customerMasterDBMYMock.CustomerMaster{}
					mc.On("GetCustomer", mock.Anything, mock.Anything).Return(getCustomerMasterResponse(), nil)
					return mc
				}(),
			},
			args: args{
				ctx:          context.Background(),
				transitionID: "test-transition-id",
				execData: &charge.ExecutionData{
					State:         0,
					ChargeDto:     nil,
					StreamMessage: nil,
					AdditionalData: func() json.RawMessage {
						marshaled, _ := json.Marshal(&LogicCreatePhysicalCardContext{
							CardStorage:    &storage.Card{},
							ActivationCode: "",
							ResponseState:  nil,
						})
						return marshaled
					}(),
				},
				params: nil,
			},
			want: &charge.ExecutionData{
				State:         charge.StInvokeFnFailed,
				ChargeDto:     nil,
				StreamMessage: nil,
				AdditionalData: func() json.RawMessage {
					marshaled, _ := json.Marshal(&LogicCreatePhysicalCardContext{
						CardStorage:    &storage.Card{},
						ActivationCode: "",
						ResponseState:  nil,
					})
					return marshaled
				}(),
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &PhysicalCardOrderClientImpl{
				ChargeWorkflow:       tt.fields.ChargeWorkflow,
				PhysicalCardOrderFee: tt.fields.PhysicalCardOrderFee,
				EuronetAdapterClient: tt.fields.EuronetAdapterClient,
				CustomerMasterClient: tt.fields.CustomerMasterClient,
			}
			got, err := c.issuePhysicalCardWorkflowFn(tt.args.ctx, tt.args.transitionID, tt.args.execData, tt.args.params)
			if tt.wantErr != nil {
				tt.wantErr(t, err, fmt.Sprintf("issuePhysicalCardWorkflowFn(%v, %v, %v, %v)", tt.args.ctx, tt.args.transitionID, tt.args.execData, tt.args.params))
			}

			if tt.want != nil {
				assert.Equal(t, tt.want.(*charge.ExecutionData), got.(*charge.ExecutionData), "ExecutionData should match")
			}
		})
	}
}

func TestPhysicalCardOrderClientImpl_handlePhysicalCardIssuance(t *testing.T) {
	type fields struct {
		ChargeWorkflow       charge.IWorkflow
		PhysicalCardOrderFee config.PhysicalCardOrderFee
		EuronetAdapterClient euronetAdapterAPI.EuronetAdapter
		CustomerMasterClient customerMasterDBMYAPI.CustomerMaster
	}
	type args struct {
		ctx            context.Context
		cardStorage    *storage.Card
		activationCode string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *responseState
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "Test 1 - happy path",
			fields: fields{
				ChargeWorkflow: func() charge.IWorkflow {
					mockChargeWorkflow := &charge.MockIWorkflow{}
					mockChargeWorkflow.On("Start", mock.Anything, mock.Anything, mock.Anything).Return(&charge.ChargeResponse{
						Status:             "",
						GroupID:            "",
						ReferenceID:        "",
						Amount:             1200,
						Currency:           "",
						SourceAccountID:    "",
						GroupTag:           "",
						TransactionDetails: nil,
						AdditionalData: func() json.RawMessage {
							marshaled, _ := json.Marshal(&LogicCreatePhysicalCardContext{
								ActivationCode: "",
								CardStorage:    &storage.Card{},
								ResponseState: &responseState{
									Status: constant.Success,
								},
							})
							return marshaled
						}(),
					}, nil)
					return mockChargeWorkflow
				}(),
				PhysicalCardOrderFee: config.PhysicalCardOrderFee{
					Amount: 100,
				},
			},
			args: args{
				ctx:         context.Background(),
				cardStorage: &storage.Card{},
			},
			want: &responseState{
				Status: constant.Success,
			},
			wantErr: assert.NoError,
		},
		{
			name: "Test 2 - happy path failed status",
			fields: fields{
				ChargeWorkflow: func() charge.IWorkflow {
					mockChargeWorkflow := &charge.MockIWorkflow{}
					mockChargeWorkflow.On("Start", mock.Anything, mock.Anything, mock.Anything).Return(&charge.ChargeResponse{
						Status:             "",
						GroupID:            "",
						ReferenceID:        "",
						Amount:             1200,
						Currency:           "",
						SourceAccountID:    "",
						GroupTag:           "",
						TransactionDetails: nil,
						AdditionalData: func() json.RawMessage {
							marshaled, _ := json.Marshal(&LogicCreatePhysicalCardContext{
								ActivationCode: "",
								CardStorage:    &storage.Card{},
								ResponseState: &responseState{
									Status: constant.Failed,
								},
							})
							return marshaled
						}(),
					}, nil)
					return mockChargeWorkflow
				}(),
				PhysicalCardOrderFee: config.PhysicalCardOrderFee{
					Amount: 100,
				},
			},
			args: args{
				ctx:         context.Background(),
				cardStorage: &storage.Card{},
			},
			want: &responseState{
				Status: constant.Failed,
			},
			wantErr: assert.NoError,
		},
		{
			name: "Test 3 - error path",
			fields: fields{
				ChargeWorkflow: func() charge.IWorkflow {
					mockChargeWorkflow := &charge.MockIWorkflow{}
					mockChargeWorkflow.On("Start", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("some err"))
					return mockChargeWorkflow
				}(),
				PhysicalCardOrderFee: config.PhysicalCardOrderFee{
					Amount: 100,
				},
			},
			args: args{
				ctx:         context.Background(),
				cardStorage: &storage.Card{},
			},
			want:    nil,
			wantErr: assert.Error,
		},
		{
			name: "Test 4 - amount <= 0",
			fields: fields{
				PhysicalCardOrderFee: config.PhysicalCardOrderFee{
					Amount: 0,
				},
				EuronetAdapterClient: func() euronetAdapterAPI.EuronetAdapter {
					mc := &euronetAdapterMock.EuronetAdapter{}
					mc.On("CardIssuance", mock.Anything, mock.Anything).Return(getENASuccessResponse(), nil)
					return mc
				}(),
				CustomerMasterClient: func() customerMasterDBMYAPI.CustomerMaster {
					mc := &customerMasterDBMYMock.CustomerMaster{}
					mc.On("GetCustomer", mock.Anything, mock.Anything).Return(getCustomerMasterResponse(), nil)
					return mc
				}(),
			},
			args: args{
				ctx:         context.Background(),
				cardStorage: &storage.Card{},
			},
			want:    &responseState{Status: constant.Success},
			wantErr: assert.NoError,
		},
		{
			name: "Test 5 - error path insufficient balance",
			fields: fields{
				ChargeWorkflow: func() charge.IWorkflow {
					mockChargeWorkflow := &charge.MockIWorkflow{}
					mockChargeWorkflow.On("Start", mock.Anything, mock.Anything, mock.Anything).Return(&charge.ChargeResponse{
						Status:             "FAILED",
						StatusReason:       "INSUFFICIENT_BALANCE",
						GroupID:            "",
						ReferenceID:        "",
						Amount:             1200,
						Currency:           "",
						SourceAccountID:    "",
						GroupTag:           "",
						TransactionDetails: nil,
						AdditionalData: func() json.RawMessage {
							marshaled, _ := json.Marshal(&LogicCreatePhysicalCardContext{
								ActivationCode: "",
								CardStorage:    &storage.Card{},
							})
							return marshaled
						}(),
					}, nil)
					return mockChargeWorkflow
				}(),
				PhysicalCardOrderFee: config.PhysicalCardOrderFee{
					Amount: 1200,
				},
			},
			args: args{
				ctx:         context.Background(),
				cardStorage: &storage.Card{},
			},
			want: &responseState{
				Status:       constant.Failed,
				StatusReason: "INSUFFICIENT_BALANCE",
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &PhysicalCardOrderClientImpl{
				ChargeWorkflow:       tt.fields.ChargeWorkflow,
				PhysicalCardOrderFee: tt.fields.PhysicalCardOrderFee,
				EuronetAdapterClient: tt.fields.EuronetAdapterClient,
				CustomerMasterClient: tt.fields.CustomerMasterClient,
			}
			got, err := c.handlePhysicalCardIssuance(tt.args.ctx, tt.args.cardStorage, tt.args.activationCode)
			if !tt.wantErr(t, err, fmt.Sprintf("handlePhysicalCardIssuance(%v, %v, %v)", tt.args.ctx, tt.args.cardStorage, tt.args.activationCode)) {
				return
			}
			assert.Equalf(t, tt.want, got, "handlePhysicalCardIssuance(%v, %v, %v)", tt.args.ctx, tt.args.cardStorage, tt.args.activationCode)
		})
	}
}
