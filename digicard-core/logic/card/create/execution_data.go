package createcard

import (
	"encoding/json"

	"gitlab.myteksi.net/dakota/workflowengine"
)

// GetState gets current state of the machine.
func (e *ExecutionData) GetState() workflowengine.State {
	return e.State
}

// SetState sets the state of the machine.
func (e *ExecutionData) SetState(state workflowengine.State) {
	e.State = state
}

// Marshal marshals the state context.
func (e *ExecutionData) Marshal() ([]byte, error) {
	return json.Marshal(e)
}

// Unmarshal unmarshals the state context.
func (e *ExecutionData) Unmarshal(byteData []byte) error {
	return json.Unmarshal(byteData, e)
}

// Clone clones a context.
func (e *ExecutionData) Clone() *ExecutionData {
	newCtx := *e
	return &newCtx
}
