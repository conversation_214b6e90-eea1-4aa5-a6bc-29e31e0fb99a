package mailingaddress

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	customerMasterAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"
	mockCustomerMaster "gitlab.myteksi.net/dbmy/customer-master/api/v2/mock"
)

func TestClientImpl_UpdateMailingAddress(t *testing.T) {
	mockCustomerMasterClient := mockCustomerMaster.CustomerMaster{}
	mockCardDao := &storage.MockICardDAO{}
	storage.CardDao = mockCardDao

	happyCustomerMasterResp := &customerMasterAPI.UpdateCustomerResponse{Customer: nil}
	var happyCardDaoResp []*storage.Card

	tests := []struct {
		name     string
		userID   string
		req      *api.PatchUpdateUserMailingAddressRequest
		mockFunc func()
		wantErr  error
	}{
		{
			name:   "happy path",
			userID: "testID",
			req: &api.PatchUpdateUserMailingAddressRequest{
				Street:     "test",
				Block:      "test",
				Unit:       "test",
				PostalCode: "654321",
				Country:    "SG",
			},
			mockFunc: func() {
				mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(happyCardDaoResp, nil).Once()
				mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(happyCustomerMasterResp, nil).Once()
			},
			wantErr: nil,
		},
		{
			name:   "no virtual card created",
			userID: "testID",
			req: &api.PatchUpdateUserMailingAddressRequest{
				Street:     "test",
				Block:      "test",
				Unit:       "test",
				PostalCode: "654321",
				Country:    "SG",
			},
			mockFunc: func() {
				mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
			},
			wantErr: logic.ErrNoVirtualCardFound,
		},
		{
			name:   "load DAO failed",
			userID: "testID",
			req: &api.PatchUpdateUserMailingAddressRequest{
				Street:     "test",
				Block:      "test",
				Unit:       "test",
				PostalCode: "654321",
				Country:    "SG",
			},
			mockFunc: func() {
				mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, errors.New("some error")).Once()
			},
			wantErr: api.DefaultInternalServerError,
		},
		{
			name:   "call to customer master failed",
			userID: "testID",
			req: &api.PatchUpdateUserMailingAddressRequest{
				Street:     "test",
				Block:      "test",
				Unit:       "test",
				PostalCode: "654321",
				Country:    "SG",
			},
			mockFunc: func() {
				mockCardDao.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(happyCardDaoResp, nil).Once()
				mockCustomerMasterClient.On("UpdateCustomer", mock.Anything, mock.Anything).Return(nil, errors.New("some error")).Once()
			},
			wantErr: api.DefaultInternalServerError,
		},
	}
	for _, tt := range tests {
		tt := tt
		c := ClientImpl{
			StatsD:               statsd.NewNoop(),
			CustomerMasterClient: &mockCustomerMasterClient,
		}
		tt.mockFunc()
		t.Run(tt.name, func(t *testing.T) {
			err := c.UpdateMailingAddress(context.Background(), tt.req, tt.userID)
			if tt.wantErr != nil {
				assert.Equal(t, tt.wantErr, err)
			} else {
				assert.Nil(t, err)
			}
		})
	}
}
