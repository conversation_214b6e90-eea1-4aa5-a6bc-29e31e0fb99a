// Code generated by mockery v2.14.0. DO NOT EDIT.

package transaction

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"

	mock "github.com/stretchr/testify/mock"
)

// MockClient is an autogenerated mock type for the Client type
type MockClient struct {
	mock.Mock
}

// DisputeCheck provides a mock function with given fields: ctx, req
func (_m *MockClient) DisputeCheck(ctx context.Context, req *api.TransactionDisputeCheckRequest) (*api.TransactionDisputeCheckResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.TransactionDisputeCheckResponse
	if rf, ok := ret.Get(0).(func(context.Context, *api.TransactionDisputeCheckRequest) *api.TransactionDisputeCheckResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.TransactionDisputeCheckResponse)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, *api.TransactionDisputeCheckRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMockClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockClient creates a new instance of MockClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockClient(t mockConstructorTestingTNewMockClient) *MockClient {
	mock := &MockClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
