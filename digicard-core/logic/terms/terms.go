// Package terms ...
package terms

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	customerMasterDBMYAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"
)

const (
	logTag      = "CardsTerms"
	digiCardTag = "digicard_core"
)

// Client ...
type Client interface {
	PersistCardsTermsAcceptance(ctx context.Context) error
	SetCurrentProductConfig(productConfig *constant.ProductConfig)
}

//go:generate mockery --name Client --inpackage --case=underscore

// NewClient ...
func NewClient() Client {
	return &ClientImpl{}
}

// ClientImpl ...
type ClientImpl struct {
	StatsD               statsd.Client                        `inject:"statsD"`
	CustomerMasterClient customerMasterDBMYAPI.CustomerMaster `inject:"client.customerMasterDBMY"`
	currentProductConfig *constant.ProductConfig              // Current product config for the request
}

// SetCurrentProductConfig sets the current product config for the request
func (c *ClientImpl) SetCurrentProductConfig(productConfig *constant.ProductConfig) {
	c.currentProductConfig = productConfig
}

// PersistCardsTermsAcceptance saves the user's card acceptance when they create a virtual card
func (c *ClientImpl) PersistCardsTermsAcceptance(ctx context.Context) error {
	// Use the current product config to determine which variant to process
	if c.currentProductConfig != nil {
		switch c.currentProductConfig.ProductVariant {
		case constant.MYCreditCard:
			return c.persistCreditCardTermsAcceptance(ctx)
		default:
			// Fallback to default terms acceptance for unknown variants
			return c.persistDebitTermsAcceptance(ctx)
		}
	}

	// If no current product config is set, use default (for accept terms flow)
	return c.persistDebitTermsAcceptance(ctx)
}

// persistCreditCardTermsAcceptance handles terms acceptance for credit cards
func (c *ClientImpl) persistCreditCardTermsAcceptance(ctx context.Context) error {
	// For MYCreditCard, we don't use ProductConfig for now
	// TODO: nil for now, we dont have agreement ID for credit card yet
	return nil
}

// persistDebitTermsAcceptance handles terms acceptance with default agreement ID
func (c *ClientImpl) persistDebitTermsAcceptance(ctx context.Context) error {
	userID := logic.GetUserID(ctx)

	// Create customer object with product-specific agreement ID
	customer := new(customerMasterDBMYAPI.Customer)

	// Use product variant to determine agreement ID
	var agreementID string
	agreementID = string(constant.CardsTermsAgreementID)
	slog.FromContext(ctx).Warn(logTag, "Using default agreement ID due to missing product config")
	agreementType := customerMasterDBMYAPI.AgreementType_Cards
	statusType := customerMasterDBMYAPI.AgreementStatus_Accepted
	customer.TnCAgreements = []*customerMasterDBMYAPI.TnCAgreement{
		{
			AgreementID:   &agreementID,
			AgreementType: &agreementType,
			Status:        &statusType,
		},
	}

	// Persist card terms acceptance
	_, err := c.invokeCustomerMaster(ctx, customer, userID)
	if err != nil {
		c.Stats(constant.Failed, err.Error())
		slog.FromContext(ctx).Error(logTag, "invokeCustomerMaster failed")
		return err
	}
	c.Stats(constant.Success, "")
	return nil
}

// Stats ..
func (c *ClientImpl) Stats(status string, statusReason string) {
	c.StatsD.Count1(digiCardTag, logTag,
		fmt.Sprintf("status:%s", status),
		fmt.Sprintf("status_reason:%s", statusReason))
}

func (c *ClientImpl) invokeCustomerMaster(ctx context.Context, customer *customerMasterDBMYAPI.Customer, userID string) (*customerMasterDBMYAPI.UpdateCustomerResponse, error) {
	// call customer master update customer
	customerMasterReq := constructCustomerMasterRequest(customer, userID)
	resp, err := c.CustomerMasterClient.UpdateCustomer(ctx, customerMasterReq)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "CustomerMasterClient.UpdateCustomer failed", slog.Error(err))
		return nil, api.DefaultInternalServerError
	}

	return resp, nil
}

// TODO: Move to helper function next time
func constructCustomerMasterRequest(customer *customerMasterDBMYAPI.Customer, userID string) *customerMasterDBMYAPI.UpdateCustomerRequest {
	customerBytes, _ := json.Marshal(customer)
	customerData := &customerMasterDBMYAPI.CustomerData{Data: customerBytes}
	customerMasterReq := &customerMasterDBMYAPI.UpdateCustomerRequest{
		ID:       userID,
		Target:   &customerMasterDBMYAPI.TargetGroup{ServiceID: constant.ServiceID},
		Customer: customerData,
	}
	return customerMasterReq
}
