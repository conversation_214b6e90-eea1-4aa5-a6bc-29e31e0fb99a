package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var cardDao = "card_dao"

// GetID implements the GetID function for Entity Interface
func (impl *Card) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *Card) SetID(ID string) {
	// replace the logic to populate unique ID for Card
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *Card) NewEntity() data.Entity {
	return &Card{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *Card) GetTableName() string {
	return "card"
}

// ICardDAO is the dao interface for Card
//
//go:generate mockery --name ICardDAO --inpackage --case=underscore
type ICardDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*Card, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*Card, error)

	// Save ...
	Save(ctx context.Context, newData *Card) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*Card) error

	// Update ...
	Update(ctx context.Context, newData *Card) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *Card, newData *Card) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*Card, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*Card, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *Card) error
}

// CardDAO ...
type CardDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewCardDAO creates a data access object for Card
func NewCardDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *CardDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &CardDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &Card{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *CardDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*Card, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(cardDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(cardDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*Card), err
}

// LoadByIDOnSlave ...
func (dao *CardDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*Card, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(cardDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(cardDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*Card), err
}

// Save ...
func (dao *CardDAO) Save(ctx context.Context, entity *Card) error {
	methodName := "save"
	defer dao.StatsD.Duration(cardDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(cardDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *CardDAO) SaveBatch(ctx context.Context, entities []*Card) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(cardDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(cardDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *CardDAO) Update(ctx context.Context, data *Card) error {
	methodName := "update"
	defer dao.StatsD.Duration(cardDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(cardDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *CardDAO) UpdateEntity(ctx context.Context, preData *Card, newData *Card) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(cardDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(cardDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *CardDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*Card, error) {
	methodName := "find"
	defer dao.StatsD.Duration(cardDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(cardDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*Card, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*Card))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *CardDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*Card, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(cardDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(cardDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*Card, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*Card))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *CardDAO) Upsert(ctx context.Context, data *Card) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(cardDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(cardDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}
