package handlers

import (
	context "context"
)

const (
	// AcceptCardsTermsLogTag ...
	AcceptCardsTermsLogTag = "AcceptCardsTerms"
)

// AcceptCardsTerms saves the user's acceptance of Cards TnC
func (d *DigicardCoreService) AcceptCardsTerms(ctx context.Context) error {
	if err := validateUserIDHeaderInfo(ctx, AcceptCardsTermsLogTag); err != nil {
		return err
	}

	// Use nil productConfig for standalone terms acceptance (uses default agreement ID)
	return d.CardsTermsClient.PersistCardsTermsAcceptance(ctx)
}
