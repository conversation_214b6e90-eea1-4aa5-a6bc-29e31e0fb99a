package handlers

import (
	"context"
	"testing"
	"time"

	customerjournalapi "gitlab.myteksi.net/dbmy/customer-journal/api"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/server/config"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/audit"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/block"
)

func TestBankBlock(t *testing.T) {
	req := &api.BankBlockRequest{CardID: cardID, CustomerID: userID, ReasonDescription: "Sample reason"}
	mockBankBlockClient := &block.MockBankBlockClient{}
	cardLogService := &audit.MockCardActivityLogService{}
	service := DigicardCoreService{
		BankBlockClient: mockBankBlockClient,
		CardActivityLog: cardLogService,
	}
	t.Run("Valid flow", func(t *testing.T) {
		cardLogService.On("SendActivityLog", mock.Anything, createCardBlockSuccessEvent()).Once()
		mockBankBlockClient.On("BankBlock", mock.Anything, mock.Anything).Return(nil).Once()

		err := service.BankBlock(context.Background(), req)
		assert.Nil(t, err)
	})
	t.Run("Invalid Flow", func(t *testing.T) {
		expectedErr := api.DefaultInternalServerError
		cardLogService.On("SendActivityLog", mock.Anything, createCardBlockFailedEvent(expectedErr)).Once()
		mockBankBlockClient.On("BankBlock", mock.Anything, mock.Anything).Return(expectedErr).Once()
		err := service.BankBlock(context.Background(), req)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("Timeout error with new context enabled", func(t *testing.T) {
		service = DigicardCoreService{
			BankBlockClient: mockBankBlockClient,
			CardActivityLog: cardLogService,
			TimeoutConfig: config.TimeoutConfig{
				Timeout: 1 * time.Millisecond,
			},
		}
		expectedErr := context.DeadlineExceeded
		cardLogService.On("SendActivityLog", mock.Anything, createCardBlockFailedEvent(expectedErr)).Once()
		mockBankBlockClient.On("BankBlock", mock.Anything, mock.Anything).Return(
			func(ctx context.Context, req *api.BankBlockRequest) error {
				time.Sleep(100 * time.Millisecond)
				return ctx.Err()
			}).Once()
		err := service.BankBlock(context.Background(), req)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func createCardBlockSuccessEvent() audit.CardActivityAuditRequest {
	return audit.CardActivityAuditRequest{
		Event:       customerjournalapi.CardFreezeUpdateEvent,
		Status:      customerjournalapi.AuditEventSuccess,
		Source:      customerjournalapi.TriggeredByCrmSource,
		BeforeValue: map[string]interface{}{audit.CardStatusDesc: audit.UnfreezeStatus},
		AfterValue:  map[string]interface{}{audit.CardStatusDesc: audit.FreezeStatus},
	}
}

func createCardBlockFailedEvent(activityErr error) audit.CardActivityAuditRequest {
	return audit.CardActivityAuditRequest{
		Event:        customerjournalapi.CardFreezeUpdateEvent,
		Status:       customerjournalapi.AuditEventFailed,
		Source:       customerjournalapi.TriggeredByCrmSource,
		BeforeValue:  map[string]interface{}{audit.CardStatusDesc: audit.UnfreezeStatus},
		AfterValue:   map[string]interface{}{audit.CardStatusDesc: audit.UnfreezeStatus},
		FailedReason: activityErr.Error(),
	}
}
