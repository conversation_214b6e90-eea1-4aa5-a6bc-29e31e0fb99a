package handlers

import (
	context "context"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/common"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/audit"

	"gitlab.com/gx-regional/dbmy/digicard/common/bgcontext"
	"gitlab.com/gx-regional/dbmy/digicard/common/recorder"

	customerjournalapi "gitlab.myteksi.net/dbmy/customer-journal/api"

	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	api "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
)

// CancelCard is to cancel user card
func (d *DigicardCoreService) CancelCard(ctx context.Context, req *api.CancelCardRequest) (*api.CardResponse, error) {
	// This function is used to prevent context canceled from front end in short amount of time,
	// and cause the data in our side and 3rd party card provider is not sync
	ctx = bgcontext.BackgroundWithValue(ctx)
	var cancel context.CancelFunc
	ctx, cancel = context.WithTimeout(ctx, d.TimeoutConfig.Timeout)
	defer cancel()

	recorder.RecordApiFuncName(ctx, common.CancelCard)
	updateCardRequest := &api.UpdateCardRequest{
		CardID:     req.CardID,
		Status:     string(constant.CardStatusRetired),
		ReasonCode: req.ReasonCode,
	}

	if err := validateUserIDHeaderInfo(ctx, UpdateCardStatusTag); err != nil {
		slog.FromContext(ctx).Warn(UpdateCardStatusTag, "invalid header(s)")
		recorder.RecordResponseCode(ctx, err.Error())
		return nil, err
	}

	ctx = context.WithValue(ctx, audit.CardActivitySourceKey, customerjournalapi.TriggeredByGXBankAppSource)
	res, err := d.UpdateCardStatusClient.UpdateCardStatus(ctx, updateCardRequest, true, true)

	if err != nil {
		recorder.RecordResponseCode(ctx, err.Error())
	} else if res != nil {
		recorder.RecordResponseCode(ctx, res.Status)
	}
	return res, err
}
