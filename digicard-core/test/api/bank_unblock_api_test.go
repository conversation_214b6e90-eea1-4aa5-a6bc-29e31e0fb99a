package api_test

import (
	"encoding/json"
	"fmt"

	"gitlab.myteksi.net/dakota/servus/v2"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
)

var _ = Describe("BankUnblock", func() {
	var (
		client *hcl.Client
		//errSetup error
	)

	var (
		cardID = uuid.NewString()
		userID = uuid.NewString()
	)
	var (
		apiURL = fmt.Sprintf("/v1/internal/card/%s/bank-unblock", cardID)
	)
	BeforeEach(func() {
		client, _ = hcl.NewClient(server.URL())
		cardAuditLog.On("SendActivityLog", mock.Anything, mock.Anything).Once()
	})

	Context("Invalid request Header parameters", func() {
		When("Request is empty", func() {
			defaultBody := &api.BankUnblockRequest{}
			requestModifier := []hcl.RequestModifier{
				hcl.JSON(defaultBody),
			}
			It("Returns 400 Bad Request", func() {
				resp, err := client.Put(apiURL, requestModifier...)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))
			})
		})
	})
	Context("Bank unblock request with all parameters present", func() {
		When("valid flow", func() {
			defaultBody := &api.BankUnblockRequest{
				CardID:     cardID,
				CustomerID: userID,
			}
			requestModifier := []hcl.RequestModifier{
				hcl.JSON(defaultBody),
			}
			It("Returns 204", func() {
				mockBankUnblockClient.On("BankUnblock", mock.Anything, mock.Anything).Return(nil).Once()
				resp, err := client.Put(apiURL, requestModifier...)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(204))
			})
		})
		When("card is not bank blocked", func() {
			defaultBody := &api.BankUnblockRequest{
				CardID:     cardID,
				CustomerID: userID,
			}
			requestModifier := []hcl.RequestModifier{
				hcl.JSON(defaultBody),
			}
			expectedErr := servus.ServiceError{
				Code:     "CARD_CANNOT_BE_UNBLOCKED",
				Message:  "card cannot be unblocked",
				HTTPCode: api.ResourceConflict.HTTPStatusCode(),
			}
			It("Returns 409", func() {
				mockBankUnblockClient.On("BankUnblock", mock.Anything, mock.Anything).Return(expectedErr).Once()
				resp, err := client.Put(apiURL, requestModifier...)
				errorResp := &servus.ServiceError{}
				_ = json.Unmarshal(resp.Body.Bytes, &errorResp)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(409))
				Expect(errorResp.Code).Should(Equal(expectedErr.Code))
			})
		})
		When("card not found", func() {
			defaultBody := &api.BankUnblockRequest{
				CardID:     cardID,
				CustomerID: userID,
			}
			requestModifier := []hcl.RequestModifier{
				hcl.JSON(defaultBody),
			}
			expectedErr := servus.ServiceError{
				Code:     string(api.CardNotFound),
				Message:  "Card not found",
				HTTPCode: api.BadRequest.HTTPStatusCode(),
			}
			It("Returns 400", func() {
				mockBankUnblockClient.On("BankUnblock", mock.Anything, mock.Anything).Return(expectedErr).Once()
				resp, err := client.Put(apiURL, requestModifier...)
				errorResp := &servus.ServiceError{}
				_ = json.Unmarshal(resp.Body.Bytes, &errorResp)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(400))
				Expect(errorResp.Code).Should(Equal(expectedErr.Code))
			})
		})
		When("invalid response status", func() {
			defaultBody := &api.BankUnblockRequest{
				CardID:     cardID,
				CustomerID: userID,
			}
			requestModifier := []hcl.RequestModifier{
				hcl.JSON(defaultBody),
			}
			expectedErr := logic.ErrInvalidResponseStatus
			It("Returns 500", func() {
				mockBankUnblockClient.On("BankUnblock", mock.Anything, mock.Anything).Return(expectedErr).Once()
				resp, err := client.Put(apiURL, requestModifier...)
				errorResp := &servus.ServiceError{}
				_ = json.Unmarshal(resp.Body.Bytes, &errorResp)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
			})
		})
		When("generic server error", func() {
			defaultBody := &api.BankUnblockRequest{
				CardID:     cardID,
				CustomerID: userID,
			}
			requestModifier := []hcl.RequestModifier{
				hcl.JSON(defaultBody),
			}
			expectedErr := api.DefaultInternalServerError
			It("Returns 500", func() {
				mockBankUnblockClient.On("BankUnblock", mock.Anything, mock.Anything).Return(expectedErr).Once()
				resp, err := client.Put(apiURL, requestModifier...)
				errorResp := &servus.ServiceError{}
				_ = json.Unmarshal(resp.Body.Bytes, &errorResp)
				Expect(err).ShouldNot(HaveOccurred())
				Expect(resp.StatusCode).Should(Equal(500))
				Expect(errorResp.Code).Should(Equal(expectedErr.Code))
			})
		})
	})
})
