// Package config ...
package config

import (
	"time"

	"gitlab.myteksi.net/dakota/schemas/streams"

	"github.com/myteksi/hystrix-go/hystrix"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2"
	sqsconfig "gitlab.myteksi.net/dbmy/common/retryable-stream/sqs/config"
	"gitlab.myteksi.net/gophers/go/commons/util/resilience/circuitbreaker"
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"
)

// AppConfig ...
type AppConfig struct {
	servus.DefaultAppConfig
	PaymentCore                *ServiceConfig             `json:"paymentCore"`
	CustomerMaster             *ServiceConfig             `json:"customerMaster"`
	AccountService             *ServiceConfig             `json:"accountService"`
	EuronetAdapter             *ServiceConfig             `json:"euronetAdapter"`
	Pigeon                     *ServiceConfig             `json:"pigeon"`
	HedwigConfig               *HedwigConfig              `json:"hedwigConfig"`
	FeatureFlags               *FeatureFlags              `json:"featureFlags"`
	RedisConf                  *redis.Config              `json:"redisConfig"`
	LimitConfigs               []LimitConfig              `json:"limitConfigs"`
	CardIssuanceConfig         CardIssuanceConfig         `json:"cardIssuanceConfig"`
	TransactionLimit           *ServiceConfig             `json:"transactionLimit"`
	PhysicalCardDeliveryConfig PhysicalCardDeliveryConfig `json:"physicalCardDeliveryConfig"`
	RedisTTLConfig             *RedisTTLConfig            `json:"redisTTLConfig"`
	DigicardTransaction        *ServiceConfig             `json:"digicardTxn"`
	RiskService                *ServiceConfig             `json:"riskService"`
	WhitelistService           *ServiceConfig             `json:"whitelistService"`
	Aegis                      *ServiceConfig             `json:"aegis"`
	DigicardActivityKafka      *KafkaConfig               `json:"digicardActivityStream"`
	DigicardAuditKafka         *KafkaConfig               `json:"digicardAuditStream"`
	PaymentCoreRetryStream     *sqsconfig.SQSConfig       `json:"pcRetryStream"`
	PaymentCoreKafka           *KafkaConfig               `json:"paymentCoreStream"`
	WorkflowRetryConfig        *WorkflowRetryConfig       `json:"workflowRetryConfig"`
	CountryConfig              CountryConfig              `json:"countryConfig"`
	WorkerConfig               *WorkerConfig              `json:"workerConfig"`
	TimeZone                   string                     `json:"timeZone"`
	CardPinEncryptionPublicKey string                     `json:"cardPinEncryptionPublicKey"`
	DepositsAccountDetailKafka *KafkaConfig               `json:"depositsAccountDetailStream"`
	S3BucketName               string                     `json:"s3BucketName"`
	S3ClientDevMode            bool                       `json:"s3ClientDevMode"`
	PhysicalCardOrderFee       PhysicalCardOrderFee       `json:"physicalCardOrderFee"`
}

// ServiceConfig ...
type ServiceConfig struct {
	BaseURL        string     `json:"baseURL"`
	ServiceName    string     `json:"serviceName"`
	CircuitBreaker *CBSetting `json:"circuitBreaker"`
}

// HedwigConfig defines the configuration for hedwig client connection.
type HedwigConfig struct {
	HostAddress      string                 `json:"hostAddress"`
	CircuitConfig    circuitbreaker.Setting `json:"circuitConfig"`
	ServiceName      string                 `json:"serviceName"`
	ServiceKey       string                 `json:"serviceKey"`
	TemplateMappings map[string]string      `json:"templateMappings"`
}

// FeatureFlags ..
type FeatureFlags struct {
	EnableAccountStatusEnquiryCheck                   bool `json:"enableAccountStatusEnquiryCheck"`
	EnableCardCreationPermissionAndAccountStatusCheck bool `json:"enableCardCreationPermissionAndAccountStatusCheck"`
	EnableActivationCodeCheck                         bool `json:"enableActivationCodeCheck"`
	EnablePhysicalCardCreationRiskCheck               bool `json:"enablePhysicalCardCreationRiskCheck"`
	EnableVirtualCardCreationRiskCheck                bool `json:"enableVirtualCardCreationRiskCheck"`
	EnableNewCardUpdateStatusFlow                     bool `json:"enableNewCardUpdateStatusFlow"`
	EnablePushNotificationFlow                        bool `json:"enablePushNotificationFlow"`
	EnableWorker                                      bool `json:"enableWorker"`
	EnableWhitelistCheck                              bool `json:"enableWhitelistCheck"`
	EnableHelpCenter                                  bool `json:"enableHelpCenter"`
	EnableCardReplacement                             bool `json:"enableCardReplacement"`
	EnableNameCheck                                   bool `json:"enableNameCheck"`
	EnableDepositsAccountDetailStream                 bool `json:"enableDepositsAccountDetailStream"`
}

// WorkflowRetryConfig defines the different retry options
type WorkflowRetryConfig struct {
	VirtualCardCreation          *RetryPolicy `json:"VirtualCardCreation"`
	VirtualCardNotificationRetry *RetryPolicy `json:"virtualCardNotificationRetry"`
}

// StreamConfig ...
type StreamConfig struct {
	StreamID              streams.StreamID `json:"streamID"`
	Topic                 string           `json:"topic"`
	DTOName               string           `json:"dtoName"`
	IsConsumptionDisabled bool             `json:"isConsumptionDisabled"`
}

// RetryPolicy defines the configs for each retry policy
type RetryPolicy struct {
	IntervalInSeconds int `json:"intervalInSeconds"`
	MaxAttempt        int `json:"maxAttempt"`
}

// LimitConfig ..
type LimitConfig struct {
	Name                string  `json:"name"`
	Title               string  `json:"title"`
	SubTitle            string  `json:"subTitle"`
	PreferredCardLimits []int64 `json:"preferredCardLimits"`
}

// PhysicalCardDeliveryConfig ...
type PhysicalCardDeliveryConfig struct {
	OrderConfirmed                  int64             `json:"orderConfirmed"`
	Posted                          int64             `json:"posted"`
	NotReceivedYet                  int64             `json:"notReceivedYet"`
	CanActivateCard                 int64             `json:"canActivateCard"`
	CanViewHelpCenter               int64             `json:"canViewHelpCenter"`
	EstimatedArrivalDays            int64             `json:"estimatedArrivalDays"`
	EstimatedArrivalDaysBuffer      int64             `json:"estimatedArrivalDaysBuffer"`
	PhysicalCardDeliveredDispatched int64             `json:"physicalCardDeliveredDispatched"`
	PhysicalCardActivationReminder1 int64             `json:"physicalCardActivationReminder1"`
	PhysicalCardActivationReminder2 int64             `json:"physicalCardActivationReminder2"`
	PhysicalCardActivationReminder3 int64             `json:"physicalCardActivationReminder3"`
	PhysicalCardActivationReminder4 int64             `json:"physicalCardActivationReminder4"`
	CourierList                     map[string]string `json:"courierList"`
}

// KafkaConfig defines the config of Kafka.
type KafkaConfig struct {
	Brokers     []string             `json:"brokers"`
	TopicName   string               `json:"topicName"`
	ClientID    string               `json:"clientID"`
	InitOffset  sndconfig.OffsetType `json:"initOffset"`
	ClusterType string               `json:"clusterType"`
	EnableTLS   bool                 `json:"enableTLS"`
	DTOName     string               `json:"dtoName"`
	StreamID    streams.StreamID     `json:"streamID"`
}

// RedisTTLConfig ...
type RedisTTLConfig struct {
	CardDesignLimit time.Duration `json:"cardDesignLimit"`
}

// CardIssuanceConfig ...
type CardIssuanceConfig struct {
	MaxAllowedCards       int `json:"maxAllowedCards"`
	MaxAllowedActiveCards int `json:"maxAllowedActiveCards"`
}

// CountryConfig ...
type CountryConfig struct {
	CurrencyCode      string `json:"currencyCode"`
	CurrencyNum       string `json:"currencyNum"`
	CountryCode       string `json:"countryCode"`
	CountryCodeAlpha3 string `json:"countryCodeAlpha3"`
	CountryName       string `json:"countryName"`
}

// PhysicalCardOrderFee ...
type PhysicalCardOrderFee struct {
	Amount int64 `json:"amount"`
}

// WorkerCfg defines the config for worker
type WorkerCfg struct {
	CronExpression []string `json:"cronExpression"`
}

// WorkerConfig ...
type WorkerConfig struct {
	CardArrivalNotification                  WorkerCfg `json:"cardArrivalNotification"`
	PhysicalCardDispatcherNotificationWorker WorkerCfg `json:"physicalCardDispatcherNotificationWorker"`
	PhysicalCardDeliveredNotificationWorker  WorkerCfg `json:"physicalCardDeliveredNotificationWorker"`
	CardConsignmentUpdateWorker              WorkerCfg `json:"cardConsignmentUpdateWorker"`
	CardCancellationUpdateWorker             WorkerCfg `json:"cardCancellationUpdateWorker"`
}

// TimeoutConfig is used to set the timeout
type TimeoutConfig struct {
	Timeout time.Duration `json:"timeout"`
}

// Name ...
func (a *AppConfig) Name() string {
	return a.ServiceName
}

// GetOwnerInfo ...
func (a *AppConfig) GetOwnerInfo() servus.OwnerInfo {
	return a.OwnerInfo
}

// LogConfig ...
func (a *AppConfig) LogConfig() *servus.LogConfig {
	return a.Logger
}

// StatsDConfig ...
func (a *AppConfig) StatsDConfig() *servus.StatsDConfig {
	return a.StatsD
}

// GetHost ...
func (a *AppConfig) GetHost() string {
	return a.Host
}

// GetPort ...
func (a *AppConfig) GetPort() int {
	return a.Port
}

// GetDescription ...
func (a *AppConfig) GetDescription() string {
	return a.Description
}

const (
	// CardCreationSuccessPushID ...
	CardCreationSuccessPushID = "card_creation_success_push_notification"
	// CardCreationFailedPushID ...
	CardCreationFailedPushID = "card_creation_failed_push_notification"
)

// PushTemplateNameMap ...
var PushTemplateNameMap = map[string]string{
	"VIRTUAL_ISSUED": "virtual_issued_push_notification",
}

// EmailTemplateNameMap ...
var EmailTemplateNameMap = map[string]string{
	"VIRTUAL_ISSUED": "virtual_issued_email_notification",
}

// CBSetting is the circuit breaker configurations for storage
// It adds ErrorHandler to ignore context.Canceled errors.
type CBSetting struct {
	hystrix.CommandConfig
}
