package hedwig

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"

	_go "github.com/json-iterator/go"
	"gitlab.myteksi.net/dakota/klient"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/tags"
)

// Push implements push.HedwigClient
func (hc *Client) Push(ctx context.Context, req *PushRequest) (*PushResponse, error) {
	slog.FromContext(ctx).Info(logTag, "calling hedwig push")

	hedwigID := hc.getHedwigTemplateID(req.Template.ID)
	if hedwigID == "" {
		slog.FromContext(ctx).Warn(logTag, "failed to find hedwig template ID")
		return nil, fmt.Errorf("wrong template ID for hedwig: [%s]", req.Template.ID)
	}
	reqBody := &PushRequest{
		RecipientID:    req.RecipientID,
		RecipientType:  req.RecipientType,
		Template:       req.Template,
		Category:       req.Category,
		TrackingParams: req.TrackingParams,
	}
	reqBody.Template.ID = hedwigID
	reqShell := &pushRequestShell{
		ctx:         ctx,
		req:         req,
		requestBody: reqBody,
	}
	resShell := &pushResponseShell{}
	clientCtx := klient.MakeContext(ctx, &PushDescriptor)

	err := hc.machinery.RoundTrip(clientCtx, reqShell, resShell)
	if err != nil {
		slog.FromContext(ctx).Info(logTag, "failed to call hedwig push",
			slog.Error(err))
	}
	return (*PushResponse)(resShell), err
}

type pushRequestShell struct {
	ctx                context.Context
	req                *PushRequest
	requestBody        interface{}
	requestHeaders     http.Header
	requestQueryParams map[string]string
}

// EncodeHTTPRequest implements klient.Request
func (req *pushRequestShell) EncodeHTTPRequest(baseURL string) (*http.Request, error) {
	fullPath := fmt.Sprintf("%s%s", baseURL, PushDescriptor.Path)

	if req.requestQueryParams != nil {
		fullURL, err := url.Parse(fullPath)
		if err != nil {
			slog.FromContext(req.ctx).Warn(logTag, "fail to parse url",
				slog.Error(err), tags.T("desc", PushDescriptor.Name), tags.T("reqQueryParams", req.requestQueryParams))
			return nil, err
		}
		params := url.Values{}
		for k, v := range req.requestQueryParams {
			params.Add(k, v)
		}
		fullURL.RawQuery = params.Encode()
		fullPath = fullURL.String()
	}

	var body io.Reader
	if req.requestBody != nil {
		jsonBytes, err := _go.Marshal(req.requestBody)
		if err != nil {
			slog.FromContext(req.ctx).Warn(logTag, "fail to marshal request body",
				slog.Error(err), tags.T("desc", PushDescriptor.Name), tags.T("reqBody", req.requestBody))
			return nil, err
		}
		body = bytes.NewBuffer(jsonBytes)
	}
	httpReq, err := http.NewRequest(PushDescriptor.Method, fullPath, body)
	if err != nil {
		slog.FromContext(req.ctx).Warn(logTag, "fail to create new HTTP request",
			slog.Error(err), tags.T("desc", PushDescriptor.Name))
		return nil, err
	}

	if req.requestHeaders != nil {
		httpReq.Header = req.requestHeaders
	}

	if body != nil {
		httpReq.Header.Add(contentType, applicationJSON)
	}
	slog.FromContext(req.ctx).Debug(logTag, fmt.Sprintf("fullPath: %v, method: %v, body: %v, header: %v", fullPath, PushDescriptor.Method, body, httpReq.Header))
	return httpReq, nil
}

type pushResponseShell PushResponse

// DecodeHTTPResponse implements klient.Response
func (c *pushResponseShell) DecodeHTTPResponse(res *http.Response) error {
	if res.StatusCode >= http.StatusInternalServerError {
		return errorhandling.UnmarshalError(res)
	}

	if res.StatusCode >= http.StatusBadRequest {
		return errorhandling.UnmarshalError(res)
	}

	if res.StatusCode == http.StatusNoContent {
		return nil
	}

	return _go.NewDecoder(res.Body).Decode(c)
}

func (hc *Client) getHedwigTemplateID(templateName string) string {
	val, ok := hc.templateMapping[templateName]
	if !ok {
		return ""
	}
	return val
}
