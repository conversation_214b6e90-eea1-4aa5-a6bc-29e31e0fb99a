# Implementation Plan: Enhanced Card Creation Workflow for CCC

## Overview
Instead of creating a new API endpoint, we will enhance the existing `POST /v1/card` endpoint and `CreateCard` handler to support CCC virtual card creation through conditional logic based on the `product_variant` field.

## Strategy
1. **Reuse Existing API**: Use current `POST /v1/card` endpoint
2. **Product Variant Detection**: Leverage existing `GetProductConfig()` function
3. **Conditional Logic**: Add if-else/switch statements throughout workflow
4. **Backward Compatibility**: Ensure existing debit card flow remains unchanged

## Prerequisites

### Environment Variables (CCC-specific)
```bash
# Customer Master Service (for RLOC account fetching)
CUSTOMER_MASTER_RLOC_ENDPOINT=/v2/customers/{customerID}/rloc-accounts

# CCC Default Transaction Limits
CCC_DEFAULT_ONLINE_LIMIT=100000
CCC_DEFAULT_CONTACTLESS_LIMIT=50000
CCC_DEFAULT_ATM_LIMIT=30000
CCC_DEFAULT_PINPAY_LIMIT=50000
```

## Conditional Logic Implementation Points

### Point 1: Handler Level - Account Verification Enhancement

**File**: `digicard-core/handlers/digicardcore_create_card.handler.go`
**Location**: Line 72 (existing TODO comment)

**Current Code**:
```go
// TODO: Fetch RLOC account information for CCC customers
if err := d.AccountVerifyClient.VerifyAccount(ctx, req.AccountID); err != nil {
    recorder.RecordResponseCode(ctx, err.Error())
    return nil, err
}
```

**Enhanced Code**:
```go
// Determine product variant from account service
productVariant, err := d.getProductVariantFromAccount(ctx, req.AccountID)
if err != nil {
    recorder.RecordResponseCode(ctx, err.Error())
    return nil, err
}

// Conditional account verification based on product variant
if productVariant == constant.MYCreditCard {
    // For CCC: Fetch RLOC account information
    if err := d.AccountVerifyClient.VerifyRlocAccount(ctx, req.AccountID); err != nil {
        recorder.RecordResponseCode(ctx, err.Error())
        return nil, err
    }
} else {
    // For regular debit cards: Standard account verification
    if err := d.AccountVerifyClient.VerifyAccount(ctx, req.AccountID); err != nil {
        recorder.RecordResponseCode(ctx, err.Error())
        return nil, err
    }
}
```

### Point 2: Account Verification Logic Enhancement

**File**: `digicard-core/logic/card/create/account_verify.go`
**Location**: Line 54 (existing TODO comment)

**Current Code**:
```go
// TODO: switch case for product variant
err = enquiry.CheckCasaAccount(ctx, accountID, c.AccountServiceClientV2)
if err != nil {
    return err
}
```

**Enhanced Code**:
```go
// Get product variant from account
accountDetails, err := c.AccountServiceClientV2.GetAccountDetailsByAccountID(ctx, &accountAPIV2.GetAccountRequest{AccountID: accountID})
if err != nil {
    return err
}

productVariant := constant.ProductVariant(accountDetails.Account.ProductVariantID)
productConfig := constant.GetProductConfig(productVariant)

switch productVariant {
case constant.MYCreditCard:
    // For CCC: Verify RLOC account instead of CASA
    err = c.verifyRlocAccount(ctx, accountID)
    if err != nil {
        return err
    }
case constant.MYDebitCard:
    // For debit cards: Standard CASA account check
    err = enquiry.CheckCasaAccount(ctx, accountID, c.AccountServiceClientV2)
    if err != nil {
        return err
    }
default:
    return fmt.Errorf("unsupported product variant: %s", productVariant)
}
```

### Point 3: Workflow Execution Data Enhancement

**File**: `digicard-core/logic/card/create/workflow.go`
**Location**: Line 62 (existing TODO comment)

**Current Code**:
```go
type ExecutionData struct {
    Card                    storage.Card
    State                   we.State
    IdempotencyKey          string
    CustomerID              string
    Request                 *api.CreateCardRequest
    EuronetAdapterResp      *euronetAdapterAPI.CardIssuanceResponse
    Status                  string
    StatusReason            string
    StatusReasonDescription string
    CardDesign              storage.CardDesign
    // TODO: ProductVariant          string
}
```

**Enhanced Code**:
```go
type ExecutionData struct {
    Card                    storage.Card
    State                   we.State
    IdempotencyKey          string
    CustomerID              string
    Request                 *api.CreateCardRequest
    EuronetAdapterResp      *euronetAdapterAPI.CardIssuanceResponse
    Status                  string
    StatusReason            string
    StatusReasonDescription string
    CardDesign              storage.CardDesign
    ProductVariant          string                    // Added: Product variant for conditional logic
    ProductConfig           *constant.ProductConfig  // Added: Product configuration
    RlocAccountInfo         *RlocAccountInfo         // Added: RLOC account info for CCC
}

// RlocAccountInfo holds RLOC account information for CCC cards
type RlocAccountInfo struct {
    AccountID       string `json:"accountId"`
    CreditLimit     int64  `json:"creditLimit"`
    AvailableBalance int64 `json:"availableBalance"`
    Status          string `json:"status"`
}
```

### Point 4: Workflow Initialization Enhancement

**File**: `digicard-core/logic/card/create/create_card.go`
**Location**: Line 28 (existing TODO comment)

**Current Code**:
```go
data := &ExecutionData{
    State:          stInit,
    Request:        req,
    CustomerID:     userID,
    IdempotencyKey: req.IdempotencyKey,
    // TODO : GET Product variant from account service
}
```

**Enhanced Code**:
```go
// Get product variant from account service
productVariant, productConfig, err := w.getProductVariantAndConfig(ctx, req.AccountID)
if err != nil {
    return nil, err
}

data := &ExecutionData{
    State:          stInit,
    Request:        req,
    CustomerID:     userID,
    IdempotencyKey: req.IdempotencyKey,
    ProductVariant: string(productVariant),
    ProductConfig:  productConfig,
}

// For CCC cards, fetch RLOC account information
if productVariant == constant.MYCreditCard {
    rlocInfo, err := w.fetchRlocAccountInfo(ctx, userID)
    if err != nil {
        return nil, err
    }
    data.RlocAccountInfo = rlocInfo
}
```

### Point 5: Card Limits Validation Enhancement

**File**: `digicard-core/logic/card/create/create_card.go`
**Location**: Line 46 (checkCardsLimit function)

**Current Code**:
```go
func executeVirtualCardCreation(ctx context.Context, data *ExecutionData, config config.CardIssuanceConfig) (*api.CardResponse, error) {
    if err := checkCardsLimit(ctx, data.CustomerID, config); err != nil {
        return nil, err
    }
    // ... rest of function
}
```

**Enhanced Code**:
```go
func executeVirtualCardCreation(ctx context.Context, data *ExecutionData, config config.CardIssuanceConfig) (*api.CardResponse, error) {
    // Use product-specific configuration for card limits
    productConfig := data.ProductConfig
    if productConfig != nil {
        // Use product-specific card issuance config
        if err := checkCardsLimitByProduct(ctx, data.CustomerID, productConfig.CardIssuanceConfig, data.ProductVariant); err != nil {
            return nil, err
        }
    } else {
        // Fallback to default config
        if err := checkCardsLimit(ctx, data.CustomerID, config); err != nil {
            return nil, err
        }
    }
    // ... rest of function
}

// New function for product-specific card limit checking
func checkCardsLimitByProduct(ctx context.Context, userID string, config constant.CardIssuance, productVariant string) error {
    cards, err := logic.FetchAllCards(ctx, userID)
    if err != nil {
        return err
    }

    // Filter cards by product variant for accurate counting
    productCards := filterCardsByProductVariant(cards, productVariant)

    activeCardsCount := 0
    for _, card := range productCards {
        if card.Status == string(constant.CardStatusProcessing) || card.Status == string(constant.CardStatusLocked) {
            return logic.ErrExceededMaxAllowedActiveCards
        }
        if card.Status == string(constant.CardStatusActive) {
            activeCardsCount++
        }
    }

    // Product-specific limits (CCC: 2 cards, Debit: existing limits)
    maxAllowed := 2 // Default for CCC
    if productVariant == string(constant.MYDebitCard) {
        maxAllowed = 2 // Keep existing debit card limit
    }

    if activeCardsCount >= maxAllowed {
        return logic.ErrExceededMaxAllowedActiveCards
    }

    return nil
}
```

### Point 6: Risk Check Enhancement

**File**: `digicard-core/logic/card/create/risk_check.go`
**Location**: Line 38 (convertCheckpointRequest function call)

**Current Code**:
```go
riskReq := convertCheckpointRequest(ctx, &nextCtx.Card, nextCtx.GetState())
```

**Enhanced Code**:
```go
// Use product variant for risk check parameters
productVariant := constant.ProductVariant(nextCtx.Card.ProductVariant)
riskReq := convertCheckpointRequestByProduct(ctx, &nextCtx.Card, nextCtx.GetState(), productVariant)
```

**New Function**:
```go
func convertCheckpointRequestByProduct(ctx context.Context, card *storage.Card, state we.State, productVariant constant.ProductVariant) *riskAPI.ValidatePartnerTxnRequest {
    productConfig := constant.GetProductConfig(productVariant)

    // Base request
    req := &riskAPI.ValidatePartnerTxnRequest{
        CustomerID: card.UserID,
        AccountID:  card.AccountID,
        CardID:     card.CardID,
    }

    // Product-specific risk parameters
    switch productVariant {
    case constant.MYCreditCard:
        req.TransactionDomain = productConfig.CardIssuanceConfig.TransactionDomain // "CREDIT_CARD"
        req.TransactionType = productConfig.CardIssuanceConfig.TransactionType     // "NEW_CREDIT_CARD_ISSUANCE_FEE"
        req.ProductType = "CREDIT_CARD"
    case constant.MYDebitCard:
        req.TransactionDomain = productConfig.CardIssuanceConfig.TransactionDomain // "DEBIT_CARD"
        req.TransactionType = productConfig.CardIssuanceConfig.TransactionType     // "NEW_CARD_ISSUANCE_FEE"
        req.ProductType = "DEBIT_CARD"
    }

    return req
}
```

### Point 7: Card Issuance Enhancement

**File**: `digicard-core/logic/card/create/card_issuance.go`
**Location**: Line 37 (existing TODO comments)

**Current Code**:
```go
resp, err := w.EuronetAdapterClient.CardIssuance(ctx, &euronetAdapterAPI.CardIssuanceRequest{ // TODO: Check if need to pass product variant
    CardID:           nextCtx.Card.CardID,
    CardIssuanceType: string(constant.VirtualCard),
    IdempotencyKey:   currCtx.IdempotencyKey,
    DisplayName:      nextCtx.Request.DisplayName,
    // TODO: to pass VendorIdentifier?
})
```

**Enhanced Code**:
```go
// Get product configuration for vendor identifier
productVariant := constant.ProductVariant(nextCtx.Card.ProductVariant)
productConfig := constant.GetProductConfig(productVariant)

// Build request with product-specific parameters
cardIssuanceReq := &euronetAdapterAPI.CardIssuanceRequest{
    CardID:           nextCtx.Card.CardID,
    CardIssuanceType: string(constant.VirtualCard),
    IdempotencyKey:   currCtx.IdempotencyKey,
    DisplayName:      nextCtx.Request.DisplayName,
    ProductVariant:   string(productVariant),        // Pass product variant
    VendorIdentifier: productConfig.VendorIdentifier, // "CCM" for CCC, "MDP" for debit
}

// For CCC cards, include RLOC account information
if productVariant == constant.MYCreditCard {
    if currCtx.RlocAccountInfo != nil {
        cardIssuanceReq.AccountID = currCtx.RlocAccountInfo.AccountID
        cardIssuanceReq.CreditLimit = currCtx.RlocAccountInfo.CreditLimit
    }
}

resp, err := w.EuronetAdapterClient.CardIssuance(ctx, cardIssuanceReq)
```

### Point 8: Euronet Adapter Enhancement

**File**: `euronet-adapter/logic/card/issuance/persist_request.go`
**Location**: Line 118-125 (existing product variant logic)

**Current Code**:
```go
// Get cardType
pVar := execData.ProductVariant
if pVar == "" {
    pVar = string(constant.MYDebitCard) //default to debit card
}
productConfig := constant.GetProductConfig(constant.ProductVariant(pVar))
if productConfig == nil {
    return nil, fmt.Errorf("invalid productConfig for productVariant %s", execData.ProductVariant)
}
cardType := productConfig.VendorIdentifier
```

**Enhanced Code**:
```go
// Get cardType with enhanced error handling
pVar := execData.ProductVariant
if pVar == "" {
    pVar = string(constant.MYDebitCard) //default to debit card
}

productVariant := constant.ProductVariant(pVar)
productConfig := constant.GetProductConfig(productVariant)
if productConfig == nil {
    return nil, fmt.Errorf("invalid productConfig for productVariant %s", execData.ProductVariant)
}

cardType := productConfig.VendorIdentifier
slog.FromContext(ctx).Info(logTag, "Using product-specific vendor identifier",
    slog.CustomTag("productVariant", pVar),
    slog.CustomTag("vendorIdentifier", cardType))

// Product-specific EN request parameters
enRequest := buildEnRequestByProduct(execData, productVariant, productConfig)
```

**New Function**:
```go
func buildEnRequestByProduct(execData *ExecutionData, productVariant constant.ProductVariant, config *constant.ProductConfig) *dto.IssuanceRequest {
    baseRequest := buildBaseEnRequest(execData) // Existing logic

    // Product-specific customizations
    switch productVariant {
    case constant.MYCreditCard:
        baseRequest.CardType = config.VendorIdentifier // "CCM"
        baseRequest.ProductType = "CREDIT"
        baseRequest.AccountType = "CREDIT_LINE"
        // Add CCC-specific fields if needed
    case constant.MYDebitCard:
        baseRequest.CardType = config.VendorIdentifier // "MDP"
        baseRequest.ProductType = "DEBIT"
        baseRequest.AccountType = "SAVINGS"
        // Keep existing debit card logic
    }

    return baseRequest
}
```

### Point 9: Notification Enhancement

**File**: `digicard-core/logic/card/create/notify.go`
**Location**: Line 44 and 83 (notification request building)

**Current Code**:
```go
notificationRequest = w.buildPushNotificationRequest(notificationType, currCtx.Card.TailCardNumber, currCtx.Card.UserID)
// ...
notificationRequest := w.buildEmailNotificationRequest(notificationType, currCtx.Card.TailCardNumber, currCtx.Card.UserID, username)
```

**Enhanced Code**:
```go
// Get product configuration for notification customization
productVariant := constant.ProductVariant(currCtx.Card.ProductVariant)
productConfig := constant.GetProductConfig(productVariant)

notificationRequest = w.buildPushNotificationRequestByProduct(notificationType, currCtx.Card.TailCardNumber, currCtx.Card.UserID, productConfig)
// ...
notificationRequest := w.buildEmailNotificationRequestByProduct(notificationType, currCtx.Card.TailCardNumber, currCtx.Card.UserID, username, productConfig)
```

**New Functions**:
```go
func (w *WorkflowImpl) buildPushNotificationRequestByProduct(notificationType notification.Type, tailCardNumber string, customerID string, productConfig *constant.ProductConfig) *notification.Request {
    params := make(map[string]string)
    params["tail_number"] = tailCardNumber

    // Product-specific notification parameters
    if productConfig != nil {
        params["product_name"] = productConfig.NotificationProductName // "GXB Credit Card" vs "GXB Debit Card"
    }

    // Product-specific template selection
    templateID := config.PushTemplateNameMap[string(notificationType)]
    if productConfig != nil && productConfig.ProductVariant == constant.MYCreditCard {
        templateID = config.CreditCardPushTemplateNameMap[string(notificationType)] // New template map for CCC
    }

    return &notification.Request{
        RecipientID: customerID,
        TemplateID:  templateID,
        Params:      params,
    }
}

func (w *WorkflowImpl) buildEmailNotificationRequestByProduct(notificationType notification.Type, tailCardNumber string, customerID string, username string, productConfig *constant.ProductConfig) *notification.Request {
    params := make(map[string]string)
    params["tail_number"] = tailCardNumber
    params["username"] = username

    // Product-specific notification parameters
    if productConfig != nil {
        params["product_name"] = productConfig.NotificationProductName
    }

    // Product-specific template selection
    templateID := config.EmailTemplateNameMap[string(notificationType)]
    if productConfig != nil && productConfig.ProductVariant == constant.MYCreditCard {
        templateID = config.CreditCardEmailTemplateNameMap[string(notificationType)] // New template map for CCC
    }

    return &notification.Request{
        RecipientID: customerID,
        TemplateID:  templateID,
        Params:      params,
    }
}
```

### Point 10: Transaction Limits Setting

**File**: `digicard-core/logic/card/create/card_issuance.go`
**Location**: After successful card issuance (new addition)

**Enhanced Code**:
```go
// After successful card issuance, set default transaction limits
if resp.Data.Status == constant.Success {
    // Set product-specific default transaction limits
    productVariant := constant.ProductVariant(nextCtx.Card.ProductVariant)
    if err := w.setDefaultTransactionLimits(ctx, &nextCtx.Card, productVariant); err != nil {
        slog.FromContext(ctx).Warn(logTag, "Failed to set default transaction limits", slog.Error(err))
        // Don't fail the entire operation for limit setting failure
    }

    // Existing publishing logic...
    bgCtx := bgcontext.BackgroundWithValue(ctx)
    gconcurrent.Go(bgCtx, logTag, func(ctx context.Context) error {
        if err = w.publishActivity(bgCtx, &nextCtx.Card); err != nil {
            slog.FromContext(bgCtx).Warn(logTag, "publishCardActivity: publishing card activity to kafka failed", slog.Error(err))
            return err
        }
        return nil
    })
}
```

**New Function**:
```go
func (w *WorkflowImpl) setDefaultTransactionLimits(ctx context.Context, card *storage.Card, productVariant constant.ProductVariant) error {
    productConfig := constant.GetProductConfig(productVariant)
    if productConfig == nil {
        return fmt.Errorf("no product config found for variant: %s", productVariant)
    }

    limitNames := productConfig.TxnLimitNames

    // Default limits based on product variant
    var defaultLimits map[string]int64
    switch productVariant {
    case constant.MYCreditCard:
        defaultLimits = map[string]int64{
            limitNames.Contactless: 50000,  // 500 MYR
            limitNames.Online:      100000, // 1000 MYR
            limitNames.PinAndPay:   50000,  // 500 MYR
            limitNames.ATM:         30000,  // 300 MYR
        }
    case constant.MYDebitCard:
        // Keep existing debit card limits
        defaultLimits = map[string]int64{
            limitNames.Contactless: 25000,  // 250 MYR
            limitNames.Online:      50000,  // 500 MYR
            limitNames.PinAndPay:   25000,  // 250 MYR
            limitNames.ATM:         150000, // 1500 MYR
        }
    }

    // Set each limit via transaction limit service
    for limitName, amount := range defaultLimits {
        if err := w.setTransactionLimit(ctx, card.CardID, limitName, amount); err != nil {
            return fmt.Errorf("failed to set %s limit: %w", limitName, err)
        }
    }

    return nil
}
```

## Helper Functions Implementation

### New Helper Functions Required

**File**: `digicard-core/logic/card/create/product_variant_helper.go`
```go
package createcard

import (
    "context"
    "fmt"

    "gitlab.com/gx-regional/dbmy/digicard/common/constant"
    accountAPIV2 "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
    customerMasterDBMYAPI "gitlab.myteksi.net/dbmy/customer-master/api/v2"
    "gitlab.myteksi.net/dakota/servus/v2/slog"
)

// getProductVariantFromAccount fetches product variant from account service
func (w *WorkflowImpl) getProductVariantFromAccount(ctx context.Context, accountID string) (constant.ProductVariant, error) {
    accountReq := &accountAPIV2.GetAccountRequest{AccountID: accountID}
    accountResp, err := w.AccountServiceClient.GetAccountDetailsByAccountID(ctx, accountReq)
    if err != nil {
        return "", fmt.Errorf("failed to get account details: %w", err)
    }

    productVariant := constant.ProductVariant(accountResp.Account.ProductVariantID)
    slog.FromContext(ctx).Info("product_variant_helper", "Retrieved product variant",
        slog.CustomTag("accountID", accountID),
        slog.CustomTag("productVariant", string(productVariant)))

    return productVariant, nil
}

// getProductVariantAndConfig fetches both product variant and its configuration
func (w *WorkflowImpl) getProductVariantAndConfig(ctx context.Context, accountID string) (constant.ProductVariant, *constant.ProductConfig, error) {
    productVariant, err := w.getProductVariantFromAccount(ctx, accountID)
    if err != nil {
        return "", nil, err
    }

    productConfig := constant.GetProductConfig(productVariant)
    if productConfig == nil {
        return "", nil, fmt.Errorf("no configuration found for product variant: %s", productVariant)
    }

    return productVariant, productConfig, nil
}

// fetchRlocAccountInfo fetches RLOC account information for CCC customers
func (w *WorkflowImpl) fetchRlocAccountInfo(ctx context.Context, customerID string) (*RlocAccountInfo, error) {
    // Get customer information from customer-master
    getCustomerReq := &customerMasterDBMYAPI.GetCustomerRequest{
        ID: customerID,
        Target: &customerMasterDBMYAPI.TargetGroup{
            ServiceID: constant.ServiceID,
        },
    }

    customerObj, err := w.CustomerMasterClient.GetCustomer(ctx, getCustomerReq)
    if err != nil {
        slog.FromContext(ctx).Error("rloc_helper", "failed to get customer from customer-master", slog.Error(err))
        return nil, fmt.Errorf("failed to fetch customer data: %w", err)
    }

    // Extract RLOC account information from customer data
    // This implementation depends on the actual customer-master schema
    rlocInfo := &RlocAccountInfo{
        AccountID:       extractRlocAccountID(customerObj),
        CreditLimit:     extractCreditLimit(customerObj),
        AvailableBalance: extractAvailableBalance(customerObj),
        Status:          extractAccountStatus(customerObj),
    }

    if rlocInfo.AccountID == "" {
        return nil, fmt.Errorf("no RLOC account found for customer %s", customerID)
    }

    return rlocInfo, nil
}

// filterCardsByProductVariant filters cards by product variant
func filterCardsByProductVariant(cards []*storage.Card, productVariant string) []*storage.Card {
    var filtered []*storage.Card
    for _, card := range cards {
        if card.ProductVariant == productVariant {
            filtered = append(filtered, card)
        }
    }
    return filtered
}
```

### Account Verification Enhancement

**File**: `digicard-core/logic/card/create/account_verify.go`
**New Method Addition**:
```go
// VerifyRlocAccount verifies RLOC account for CCC customers
func (c *AccountVerifyClientImpl) VerifyRlocAccount(ctx context.Context, accountID string) error {
    if c.FeatureFlags.EnableCardCreationPermissionAndAccountStatusCheck {
        err := c.checkPermission(ctx, accountID)
        if err != nil {
            errorCode := api.Forbidden
            return servus.ServiceError{
                Code:     string(errorCode),
                Message:  err.Error(),
                HTTPCode: errorCode.HTTPStatusCode(),
            }
        }

        // For CCC: Check RLOC account instead of CASA
        err = c.checkRlocAccountStatus(ctx, accountID)
        if err != nil {
            return err
        }
    }
    return nil
}

// checkRlocAccountStatus checks RLOC account status and availability
func (c *AccountVerifyClientImpl) checkRlocAccountStatus(ctx context.Context, accountID string) error {
    // Get customer information to verify RLOC account
    userID := commonCtx.GetUserID(ctx)
    getCustomerReq := &customerMasterDBMYAPI.GetCustomerRequest{
        ID: userID,
        Target: &customerMasterDBMYAPI.TargetGroup{
            ServiceID: constant.ServiceID,
        },
    }

    customerObj, err := c.CustomerMasterClient.GetCustomer(ctx, getCustomerReq)
    if err != nil {
        slog.FromContext(ctx).Warn(accountVerifyTag, "error getting customer for RLOC verification", slog.Error(err))
        return fmt.Errorf("failed to verify RLOC account: %w", err)
    }

    // Verify RLOC account exists and is active
    // Implementation depends on customer-master schema
    if !hasActiveRlocAccount(customerObj) {
        return fmt.Errorf("no active RLOC account found for customer")
    }

    return nil
}

// hasActiveRlocAccount checks if customer has an active RLOC account
func hasActiveRlocAccount(customerObj *customerMasterDBMYAPI.GetCustomerResponse) bool {
    // Implementation depends on customer-master schema
    // This is a placeholder - actual implementation would check customer data
    return true // Simplified for now
}
```

## Configuration Updates Required

### Template Configuration

**File**: `digicard-core/server/config/config.go`
**Add CCC-specific notification templates**:
```go
// CreditCardPushTemplateNameMap maps notification types to CCC push templates
var CreditCardPushTemplateNameMap = map[string]string{
    string(notification.VirtualIssued): "credit_card_virtual_issued_push_notification",
}

// CreditCardEmailTemplateNameMap maps notification types to CCC email templates
var CreditCardEmailTemplateNameMap = map[string]string{
    string(notification.VirtualIssued): "credit_card_virtual_issued_email_notification",
}
```

### Service Dependencies

**File**: `digicard-core/handlers/digicardcore.service.go`
**Add method to service**:
```go
// getProductVariantFromAccount helper method for handlers
func (d *DigicardCoreService) getProductVariantFromAccount(ctx context.Context, accountID string) (constant.ProductVariant, error) {
    accountReq := &accountAPIV2.GetAccountRequest{AccountID: accountID}
    accountResp, err := d.AccountServiceClient.GetAccountDetailsByAccountID(ctx, accountReq)
    if err != nil {
        return "", fmt.Errorf("failed to get account details: %w", err)
    }

    return constant.ProductVariant(accountResp.Account.ProductVariantID), nil
}
```

## Testing Strategy

### Unit Tests Required

1. **Product Variant Detection Tests**
   - Test `getProductVariantFromAccount()` with different account types
   - Test `getProductVariantAndConfig()` with valid/invalid variants

2. **Conditional Logic Tests**
   - Test account verification for CCC vs debit cards
   - Test risk check parameters for different product variants
   - Test notification template selection by product variant

3. **Card Limits Tests**
   - Test `checkCardsLimitByProduct()` with different product variants
   - Test default transaction limit setting for CCC vs debit cards

4. **Workflow Integration Tests**
   - Test complete CCC card creation flow
   - Test existing debit card flow remains unchanged
   - Test error handling for unsupported product variants

### Integration Tests Required

1. **End-to-End Flow Tests**
   - CCC customer creates virtual card successfully
   - Debit customer creates virtual card (existing flow)
   - Error scenarios for each product variant

2. **External Service Integration Tests**
   - Customer-master RLOC account fetching
   - Euronet adapter with product-specific parameters
   - Notification service with product-specific templates

### Test Commands
```bash
# Run unit tests for enhanced workflow
go test ./digicard-core/logic/card/create/... -v

# Run integration tests
go test ./digicard-core/test/api/... -v -tags=integration

# Run specific product variant tests
go test ./digicard-core/logic/card/create/... -run TestProductVariant -v
```

## Summary

This implementation enhances the existing card creation workflow with conditional logic based on `product_variant` without breaking existing functionality. Key benefits:

1. **✅ Reuses Existing API**: No new endpoints required
2. **✅ Backward Compatible**: Existing debit card flow unchanged
3. **✅ Product-Driven Logic**: Uses existing `GetProductConfig()` pattern
4. **✅ Minimal Code Changes**: Adds conditional logic at key decision points
5. **✅ Leverages Existing Infrastructure**: Uses current table structure and constants

The implementation adds conditional logic at 10 key points in the workflow, ensuring CCC cards get appropriate handling while maintaining the existing debit card behavior.

func (w *WorkflowImpl) validateCCCCardLimits(ctx context.Context, userID string) error {
    cards, err := logic.FetchAllCards(ctx, userID)
    if err != nil {
        return err
    }

    activeCount := 0
    for _, card := range cards {
        if card.Status == string(constant.CardStatusActive) || card.Status == string(constant.CardStatusProcessing) {
            activeCount++
        }
    }

    if activeCount >= 2 { // CCC limit
        return errors.New("maximum active cards limit reached for CCC customer")
    }

    return nil
}

func (w *WorkflowImpl) isRetryableError(err error) bool {
    // Define retryable error patterns
    retryablePatterns := []string{
        "timeout",
        "connection refused",
        "service unavailable",
        "internal server error",
    }

    errStr := err.Error()
    for _, pattern := range retryablePatterns {
        if contains(errStr, pattern) {
            return true
        }
    }
    return false
}

func (w *WorkflowImpl) updateCreationStatus(ctx context.Context, status *storage.CardCreationStatus, newStatus, reason string) {
    status.Status = newStatus
    if reason != "" {
        errorDetails := map[string]string{"reason": reason}
        status.ErrorDetails, _ = json.Marshal(errorDetails)
    }
    storage.CardCreationStatusDao.Update(ctx, status)
}

func (w *WorkflowImpl) handleExistingCCCCard(ctx context.Context, card *storage.Card) (*api.CreateVirtualCardCCCResponse, error) {
    switch card.Status {
    case string(constant.CardStatusActive):
        // Return existing successful card
        return &api.CreateVirtualCardCCCResponse{
            Card:          convertToAPICardDetail(card),
            Status:        "SUCCESS",
            StatusReason:  "CARD_ALREADY_EXISTS",
            RlocAccountId: card.AccountID,
        }, nil
    case string(constant.CardStatusProcessing):
        return &api.CreateVirtualCardCCCResponse{
            Status:       "PROCESSING",
            StatusReason: "CARD_CREATION_IN_PROGRESS",
        }, nil
    case string(constant.CardStatusFailed):
        return &api.CreateVirtualCardCCCResponse{
            Status:       "FAILED",
            StatusReason: "PREVIOUS_ATTEMPT_FAILED",
        }, nil
    default:
        return &api.CreateVirtualCardCCCResponse{
            Status:       "PROCESSING",
            StatusReason: "REQUEST_BEING_PROCESSED",
        }, nil
    }
}

func convertToAPICardDetail(card *storage.Card) *api.CardDetail {
    return &api.CardDetail{
        CardID:         card.CardID,
        TailCardNumber: card.TailCardNumber,
        HeadCardNumber: card.HeadCardNumber,
        DisplayName:    card.DisplayName,
        Status:         card.Status,
        AccountID:      card.AccountID,
        VendorCardID:   card.VendorCardID,
        OrderStatus:    card.OrderStatus,
        CardType:       "VIRTUAL",
    }
}

func contains(s, substr string) bool {
    return len(s) >= len(substr) && (s == substr || (len(s) > len(substr) &&
        (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
         (len(s) > len(substr) && s[1:len(substr)+1] == substr))))
}
```

### Step 5: Create Limit Setter

**File**: `digicard-core/logic/card/create/limit_setter.go`
```go
package createcard

import (
    "context"
    "fmt"

    "gitlab.com/gx-regional/dbmy/digicard/common/constant"
    transactionLimitAPI "gitlab.myteksi.net/dakota/transaction-limit/api"
    "gitlab.myteksi.net/dakota/servus/v2/slog"
)

// LimitSetter handles setting default transaction limits for CCC cards
type LimitSetter interface {
    SetDefaultCCCLimits(ctx context.Context, cardID string, enableOnline bool) error
}

// LimitSetterImpl implements LimitSetter
type LimitSetterImpl struct {
    TransactionLimitClient transactionLimitAPI.TransactionLimit `inject:"client.transactionLimit"`
}

// NewLimitSetter creates a new limit setter
func NewLimitSetter() LimitSetter {
    return &LimitSetterImpl{}
}

// SetDefaultCCCLimits sets default transaction limits for CCC virtual cards
func (l *LimitSetterImpl) SetDefaultCCCLimits(ctx context.Context, cardID string, enableOnline bool) error {
    slog.FromContext(ctx).Info("limit_setter", "Setting default CCC limits", slog.CustomTag("cardID", cardID))

    // Default limits for CCC customers (in cents)
    limits := map[string]int64{
        constant.TxnLimitContactless:   50000,  // 500 MYR
        constant.TxnLimitPinAndPay:     50000,  // 500 MYR
        constant.TxnLimitAtmWithdrawal: 30000,  // 300 MYR
    }

    // Set online limit based on user preference
    if enableOnline {
        limits[constant.TxnLimitOnline] = 100000 // 1000 MYR
    } else {
        limits[constant.TxnLimitOnline] = 0 // Disabled
    }

    // Set each limit
    for limitName, amount := range limits {
        if err := l.setTransactionLimit(ctx, cardID, limitName, amount); err != nil {
            slog.FromContext(ctx).Error("limit_setter", "Failed to set limit",
                slog.CustomTag("limitName", limitName), slog.Error(err))
            return err
        }
    }

    slog.FromContext(ctx).Info("limit_setter", "Successfully set all default CCC limits")
    return nil
}

func (l *LimitSetterImpl) setTransactionLimit(ctx context.Context, cardID, limitName string, amount int64) error {
    req := &transactionLimitAPI.SetTransactionLimitRequest{
        LimitName: limitName,
        Amount:    amount,
        Currency:  "MYR",
        Conditions: []transactionLimitAPI.Condition{
            {
                Key:   constant.TxnLimitConditionKey,
                Value: cardID,
            },
        },
    }

    _, err := l.TransactionLimitClient.SetTransactionLimit(ctx, req)
    if err != nil {
        return fmt.Errorf("failed to set %s limit: %w", limitName, err)
    }

    return nil
}
```

### Step 6: Create Handler

**File**: `digicard-core/handlers/digicardcore_create_virtual_card_ccc.handler.go`
```go
package handlers

import (
    "context"

    "gitlab.com/gx-regional/dbmy/digicard/common/recorder"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/common"
    "gitlab.com/gx-regional/dbmy/digicard/digicard-core/logic/card/create"
    commonCtx "gitlab.myteksi.net/dakota/common/context"
    "gitlab.myteksi.net/dakota/servus/v2"
    "gitlab.myteksi.net/dakota/servus/v2/slog"
)

const (
    createVirtualCardCCCLogTag = "create_virtual_card_ccc_handler"
)

// CreateVirtualCardCCC creates a virtual card for CCC customers
func (d *DigicardCoreService) CreateVirtualCardCCC(ctx context.Context, req *api.CreateVirtualCardCCCRequest) (*api.CreateVirtualCardCCCResponse, error) {
    recorder.RecordApiFuncName(ctx, common.CreateVirtualCardCCC)

    // Validate user ID
    if commonCtx.GetUserID(ctx) == "" {
        errorCode := api.Forbidden
        recorder.RecordResponseCode(ctx, string(errorCode))
        return nil, servus.ServiceError{
            Code:     string(errorCode),
            Message:  "'X-Grab-Id-Userid' is missing.",
            HTTPCode: errorCode.HTTPStatusCode(),
        }
    }

    // Validate request parameters
    if errs := validateCreateVirtualCardCCCRequest(req); len(errs) != 0 {
        errorCode := api.BadRequest
        recorder.RecordResponseCode(ctx, string(errorCode))
        return nil, servus.ServiceError{
            Code:     string(errorCode),
            Message:  "request has invalid parameter(s) or header(s).",
            HTTPCode: errorCode.HTTPStatusCode(),
            Errors:   errs,
        }
    }

    // Check whitelist
    if !d.WhitelistClient.IsUserWhitelisted(ctx, commonCtx.GetUserID(ctx)) {
        errorCode := api.Forbidden
        recorder.RecordResponseCode(ctx, string(errorCode))
        return nil, servus.ServiceError{
            Code:     string(errorCode),
            Message:  "user is not whitelisted to create CCC card",
            HTTPCode: errorCode.HTTPStatusCode(),
        }
    }

    // Execute CCC card creation workflow
    resp, err := d.CCCCardCreationWorkflow.ExecuteCreateVirtualCardCCCWorkflow(ctx, req)
    if err != nil {
        slog.FromContext(ctx).Error(createVirtualCardCCCLogTag, "CCC card creation failed", slog.Error(err))
        recorder.RecordResponseCode(ctx, err.Error())
        return nil, err
    }

    recorder.RecordResponseCode(ctx, resp.StatusReason)
    return resp, nil
}

// GetCardCreationStatus returns the status of a card creation request
func (d *DigicardCoreService) GetCardCreationStatus(ctx context.Context, req *api.GetCardCreationStatusRequest) (*api.GetCardCreationStatusResponse, error) {
    card, err := createcard.FindCCCCardByIdempotencyKey(ctx, req.IdempotencyKey)
    if err != nil {
        return nil, err
    }

    retryMeta, err := createcard.GetCCCRetryMetadata(card)
    if err != nil {
        return nil, err
    }

    return &api.GetCardCreationStatusResponse{
        Status:        card.Status,
        StatusReason:  retryMeta.ErrorDetails,
        AttemptCount:  int32(retryMeta.AttemptCount),
        LastAttemptAt: retryMeta.LastAttemptAt.Format("2006-01-02T15:04:05Z"),
    }, nil
}

func validateCreateVirtualCardCCCRequest(req *api.CreateVirtualCardCCCRequest) []servus.ErrorDetail {
    var errs []servus.ErrorDetail

    emptyFieldValidator(req.IdempotencyKey, "idempotencyKey", &errs)
    emptyFieldValidator(req.PreferredName, "preferredName", &errs)

    if req.PreferredName != "" && !isValidDisplayName(req.PreferredName) {
        errs = append(errs, servus.ErrorDetail{
            ErrorCode: string(api.FieldInvalid),
            Message:   "invalid preferred name",
            Path:      "preferredName",
        })
    }

    return errs
}
```

## Testing

### Unit Tests
1. **RLOC Client Tests**: Mock customer-master responses
2. **CCC Workflow Tests**: Test retry logic and error handling
3. **Limit Setter Tests**: Verify default limits are set correctly
4. **Handler Tests**: Validate request/response handling

### Integration Tests
1. **End-to-End Flow**: Complete CCC card creation process
2. **Retry Scenarios**: Test timeout and retry mechanisms
3. **Error Handling**: Test various failure scenarios
4. **Idempotency**: Verify duplicate request handling

### Test Commands
```bash
# Run unit tests
go test ./digicard-core/logic/card/create/... -v

# Run integration tests
go test ./digicard-core/test/api/... -v -tags=integration

# Run specific CCC tests
go test ./digicard-core/handlers/... -run TestCreateVirtualCardCCC -v
```
```
