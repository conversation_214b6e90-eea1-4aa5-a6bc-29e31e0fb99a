package notification

import (
	"context"
)

// Notifier defines the function for notification service.
type Notifier interface {
	// Notify sends different types of notification(email, push etc)
	Notify(context.Context, *Request) error
}

//go:generate mockery --name Notifier --inpackage --case=underscore

// Request ...
type Request struct {
	RecipientID string
	TemplateID  string
	Params      map[string]string
}
