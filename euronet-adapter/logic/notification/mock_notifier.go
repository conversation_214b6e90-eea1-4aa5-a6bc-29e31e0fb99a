// Code generated by mockery v2.20.0. DO NOT EDIT.

package notification

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// MockNotifier is an autogenerated mock type for the Notifier type
type MockNotifier struct {
	mock.Mock
}

// Notify provides a mock function with given fields: _a0, _a1
func (_m *MockNotifier) Notify(_a0 context.Context, _a1 *Request) error {
	ret := _m.Called(_a0, _a1)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *Request) error); ok {
		r0 = rf(_a0, _a1)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewMockNotifier interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockNotifier creates a new instance of MockNotifier. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockNotifier(t mockConstructorTestingTNewMockNotifier) *MockNotifier {
	mock := &MockNotifier{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
