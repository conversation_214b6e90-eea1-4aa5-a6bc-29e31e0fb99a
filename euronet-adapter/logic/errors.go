// Package logic ...
package logic

import (
	"errors"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"net/http"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/servus/v2"
)

var (
	// ErrInvalidContext defines error when wrong context is passed into workflow function.
	ErrInvalidContext = errors.New("WRONG_CONTEXT_PASSED")

	// ErrInvalidRequestStatus defines when the request status is invalid
	ErrInvalidRequestStatus = errors.New("INVALID_REQUEST_STATUS")

	// ErrInvalidResponseStatus defines when the request status is invalid
	ErrInvalidResponseStatus = errors.New("INVALID_RESPONSE_STATUS")

	// ErrInvalidEuronetErrorCode defines when the error code from euronet is invalid
	ErrInvalidEuronetErrorCode = errors.New("INVALID_ERROR_CODE")

	// ErrProxyNumberNotFound defines when proxy number is not found
	ErrProxyNumberNotFound = errors.New("PROXY_NUMBER_NOT_FOUND")

	// ErrInvalidCardID defines when the interal cardID already exists in db
	ErrInvalidCardID = errors.New("CARD_ID_ALREADY_EXISTS")

	// ErrWrongParamsPassed defines the error when the params passed into the transition function does not contain the correct struct.
	ErrWrongParamsPassed = errors.New("wrong params passed")

	// ErrPartialCaptureFailed defines the error when partial capture failed and the txn is blocked from subsequent captures.
	ErrPartialCaptureFailed = errors.New("PARTIAL_CAPTURE_FAILED")

	// ErrJVRecordNotFound defines error when JV posting record is not found.
	ErrJVRecordNotFound = errors.New("JV_POSTING_RECORD_NOT_FOUND")

	// ErrInvalidCardAcceptorTermName defines when the card acceptor term name is invalid
	ErrInvalidCardAcceptorTermName = errors.New("INVALID_ACCEPTOR_TERM_NAME")

	// ErrJVZeroOriginalAmount defines the error when original amount is 0 in JV entries
	ErrJVZeroOriginalAmount = errors.New("JV_ZERO_ORIGINAL_AMOUNT")

	// ErrInvalidParameters defines error when parameters are invalid.
	ErrInvalidParameters = servus.ServiceError{
		HTTPCode: http.StatusBadRequest,
		Code:     "INVALID_PARAMETERS",
		Message:  "request has invalid parameter(s) or header(s).",
	}
	// ErrInvalidEuronetResponse defines error when euronet return bad response code or invalid format
	ErrInvalidEuronetResponse = servus.ServiceError{
		HTTPCode: http.StatusBadRequest,
		Code:     "INVALID_RESPONSE",
		Message:  "euronet response has error code or invalid format.",
	}

	// ErrUnknownEuronetAPIError defines error when euronet return http error code
	ErrUnknownEuronetAPIError = servus.ServiceError{
		HTTPCode: http.StatusInternalServerError,
		Code:     "INTERNAL_SERVER_ERROR",
		Message:  "euronet request return http error.",
	}

	// ErrForbidden defines error when the request is forbidden.
	ErrForbidden = servus.ServiceError{
		HTTPCode: http.StatusForbidden,
		Code:     "FORBIDDEN",
	}

	// ErrInvalidUserHeader defines error when user credential in header is invalid.
	ErrInvalidUserHeader = servus.ServiceError{
		HTTPCode: http.StatusForbidden,
		Code:     "INVALID_PARAMETERS",
		Message:  "request has invalid parameter(s) or header(s).",
	}

	// ErrGenericServer defines generic server error.
	ErrGenericServer = servus.ServiceError{
		Code:     "INTERNAL_SERVER_ERROR",
		Message:  "Something went wrong, please try again later.",
		HTTPCode: http.StatusInternalServerError,
	}

	//ErrAuthTimeOut ...
	ErrAuthTimeOut = servus.ServiceError{
		HTTPCode: http.StatusRequestTimeout,
		Code:     ErrAuthTimeout.Code,
		Message:  ErrAuthTimeout.Message,
	}

	// ErrPaymentCoreFailure defines error when payment core transfer leads to error or response had failure as status
	ErrPaymentCoreFailure = servus.ServiceError{
		HTTPCode: http.StatusInternalServerError,
		Code:     "INVALID_RESPONSE",
		Message:  "Payment core transfer response has error code or failure during transfer.",
	}

	//ErrDuplicateRRN ...
	ErrDuplicateRRN = servus.ServiceError{
		HTTPCode: http.StatusInternalServerError,
		Code:     "INVALID_PARAMETERS_DUPLICATE_RRN",
		Message:  "dupplicate RRN",
	}

	ErrStatusConflict = servus.ServiceError{
		HTTPCode: http.StatusConflict,
		Code:     constant.StatusConflict,
		Message:  "request conflicts with current state",
	}
)

// CustomError defines errors with appropriate messages
func CustomError(httpCode int, errs *[]servus.ErrorDetail) error {
	return servus.ServiceError{
		Code:     "INVALID_PARAMETERS",
		Message:  "request has invalid parameter(s) or header(s).",
		HTTPCode: httpCode,
		Errors:   *errs,
	}
}

// BuildErrorResponse creates servus error
func BuildErrorResponse(code api.ErrorCode, message string) servus.ServiceError {
	return servus.ServiceError{
		Code:     string(code),
		Message:  message,
		HTTPCode: code.HTTPStatusCode(),
	}
}

func IsServiceError(err error, code string) bool {
	//if err is not a pointer
	if serviceErr1, ok := err.(servus.ServiceError); ok && serviceErr1.Code == code {
		return true
	}

	//if err is a pointer
	var serviceErr2 *servus.ServiceError
	return errors.As(err, &serviceErr2) && serviceErr2.Code == code
}
