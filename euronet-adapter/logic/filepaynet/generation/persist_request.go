package generation

import (
	"context"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/filepaynet"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func (w *WorkflowImpl) persistRequest(ctx context.Context, transitionID string, exec workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := exec.(*filepaynet.ExecutionData)
	if !ok {
		return nil, logic.ErrInvalidContext
	}

	nextCtx := currCtx.Clone()

	storageFile, err := persistFileGeneration(ctx, nextCtx)

	if err != nil {
		slog.FromContext(ctx).Error(logTag, "File.save error", slog.Error(err))
		return nil, err
	}

	nextCtx.File = *storageFile
	nextCtx.State = stRequestPersisted

	return nextCtx, nil
}

func persistFileGeneration(ctx context.Context, execData *filepaynet.ExecutionData) (*storage.File, error) {
	newEntry := &storage.File{
		Type:        string(execData.Req.Type),
		FileName:    execData.Req.FileName,
		ReferenceID: execData.Req.IdempotencyKey,
		Status:      string(constant.FileInProcessing),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := storage.FileD.Save(ctx, newEntry); err != nil {
		return nil, err
	}
	return newEntry, nil
}
