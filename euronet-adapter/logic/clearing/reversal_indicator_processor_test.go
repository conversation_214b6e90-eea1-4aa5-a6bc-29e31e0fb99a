package clearing

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

func Test_reversalIndicatorImpl_Process(t *testing.T) {
	type fields struct {
		availableQueryConditionFns []queryConditionFn
		StatsD                     statsd.Client
		CardTransactionDBHelper    storage.ICardTransactionDBHelper
	}
	type args struct {
		ctx    context.Context
		record *dto.ClearingRecord
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "happy path - indicator is R",
			args: args{
				ctx: context.Background(),
				record: &dto.ClearingRecord{
					ReferenceID:       "12345678",
					ProcessingCode:    "20000",
					ReversalIndicator: "R",
				},
			},
			wantErr: assert.NoError,
		},
		{
			name: "happy path - skip process, indicator not R",
			args: args{
				ctx: context.Background(),
				record: &dto.ClearingRecord{
					ProcessingCode: "00000",
				},
			},
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return assert.ErrorIs(t, err, errProcessorNotApplicable)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &reversalIndicatorImpl{
				StatsD: statsd.NewNoop(),
			}
			tt.wantErr(t, a.Process(tt.args.ctx, tt.args.record), fmt.Sprintf("Process(%v, %v)", tt.args.ctx, tt.args.record))
		})
	}
}
