package clearing

import (
	"context"
	"errors"
	"io"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
)

const (
	logTag          = "logic.clearing"
	processorLogTag = "logic.clearing_record_processor"
	registryLogTag  = "logic.clearing_record_processor_registry"
)

type AC string

const (
	DFLT      AC = ""
	FORCEPOST AC = "FORCE_POST"
	AC1       AC = "AC1"
	AC2AC5    AC = "AC2_AC5"
	AC3       AC = "AC3"
	AC4       AC = "AC4"
	AC9       AC = "AC9"

	ClearingRecordStatusSkipped    = "SKIPPED"
	ClearingRecordStatusProcessing = "PROCESSING"
	ClearingRecordStatusFailed     = "FAILED"
	ClearingRecordStatusCompleted  = "COMPLETED"
)

// Header ...
const (
	Header                   = "H"
	TransactionDetails       = "D"
	Footer                   = "F"
	ClearingFileHeaderLength = 4
	ClearingFileRecordLength = 26
	ClearingFileFooterLength = 2
)

var (
	errProcessorNotApplicable = errors.New("processor not applicable")

	errInvalidAC = errors.New("invalid ac")

	errNoOriginalTxn = errors.New("no original txn found")

	errorMessageMap = map[string]string{
		string(constant.TxnCompleted): "Transaction is already completed",
		string(constant.TxnCanceled):  "Transaction is canceled",
	}
)

var posEntryModePanEntryModeMap = map[string]string{
	"C": string(dto.Chip),                      // PAN auto-entry via chip (online authorized transaction)
	"B": string(dto.MagneticStripe),            // PAN auto-entry via magnetic stripe: track data provided unaltered within transaction (magnetic stripe entry may also be chip fallback transaction). - dev note: Could be "80" too
	"1": string(dto.ManualEntryMode),           // PAN manual entry; no terminal used
	"M": string(dto.ContactlessChipCard),       // PAN auto-entry via contactless M/Chip
	"S": string(dto.OpticalIdentityCheck),      // PAN entry via electronic commerce
	"7": string(dto.COF),                       // Credential on File (effective June 12, 2018)
	"A": string(dto.ContactlessMagneticStripe), // PAN auto-entry via contactless magnetic stripe: track data provided unaltered within transaction
	"0": string(dto.Unknown),                   // PAN Entry mode unknown
	"2": string(dto.MagneticStripeRead),        // PAN auto-entry via magnetic strip: track data is not required within transaction
	"R": string(dto.ECOM),                      // PAN entry via electronic commerce containing Digital Secure Remote Payment (DSP) cryptogram within DE 55 (Integrated Circuit Card [ICC])
	"6": string(dto.HYBRID),                    // PAN manual entry using a terminal, or through voice transaction after chip card read error or chip fallback transaction failure
	"T": string(dto.Server),                    // PAN auto-entry via server (issuer, acquirer, or third party vendor system)
	"F": string(dto.Chip),                      // PAN auto-entry via chip (offline chip-approved transaction)
}

// File Column Indexes ...
const (
	HeaderIndexCreateDate                           = 1
	HeaderIndexSettlementDate                       = 2
	TransactionDetailsIndexProxyNumber              = 1
	TransactionDetailsIndexProcessingCode           = 2
	TransactionDetailsIndexOriginalAmount           = 3
	TransactionDetailsIndexSettlementAmount         = 5
	TransactionDetailsIndexLocalTxnDateTime         = 6
	TransactionDetailsIndexPosEntryMode             = 7
	TransactionDetailsIndexMCC                      = 9
	TransactionDetailsIndexAcquirerReferenceData    = 10
	TransactionDetailsIndexAcquirerID               = 11
	TransactionDetailsIndexRetrievalReferenceNumber = 12
	TransactionDetailsIndexApprovalCode             = 13
	TransactionDetailsIndexAcceptorTerminalID       = 14
	TransactionDetailsIndexAcceptorIDCode           = 15
	TransactionDetailsIndexAcceptorNameLocation     = 16
	TransactionDetailsIndexOriginalCurrency         = 17
	TransactionDetailsIndexCardHolderCurrency       = 19
	TransactionDetailsIndexTransactionLifeCycleID   = 20
	TransactionDetailsIndexReversalIndicator        = 21
	TransactionDetailsIndexConversionRate           = 23
	TransactionDetailsIndexMRC                      = 25
)

type FileProperties struct {
	FileName       string
	SettlementDate time.Time
}

type RowData struct {
	Arr            []string `json:"arr"`
	OriginalAmount int64    `json:"originalAmount"`
}

type ProcessClearingRecordRequest struct {
	*storage.ClearingRecord
}

//go:generate mockery --name IClearingFileLogic --inpackage --case=underscore
type IClearingFileLogic interface {
	PersistClearingRecordsFromCSV(ctx context.Context, fileProperties *FileProperties, reader io.ReadSeeker) error
	ProcessClearingRecord(ctx context.Context, request *ProcessClearingRecordRequest) error
}

//go:generate mockery --name IClearingRecordProcessorRegistry --inpackage --case=underscore
type IClearingRecordProcessorRegistry interface {
	Register(processor IClearingRecordProcessor)
	RunProcessorByPriority(ctx context.Context, record *dto.ClearingRecord) error
}

//go:generate mockery --name IClearingRecordProcessor --inpackage --case=underscore
type IClearingRecordProcessor interface {
	Name() string
	GetAC() AC
	Priority() int
	Process(ctx context.Context, record *dto.ClearingRecord) error
}

//go:generate mockery --name IClearingRecordCore --inpackage --case=underscore
type IClearingRecordCore interface {
	Capture(ctx context.Context, originalRequestID string, record *dto.ClearingRecord) error
	Transfer(ctx context.Context, record *dto.ClearingRecord) error
	Refund(ctx context.Context, record *dto.ClearingRecord) error
}
