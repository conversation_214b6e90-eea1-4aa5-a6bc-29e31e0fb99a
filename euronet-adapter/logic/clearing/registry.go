package clearing

import (
	"context"
	"errors"
	"fmt"
	"sort"

	digicardCoreAPI "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	v2 "gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

type clearingProcessorRegistryImpl struct {
	processors              []IClearingRecordProcessor
	StatsD                  statsd.Client                    `inject:"statsD"`
	DigicardCoreClient      digicardCoreAPI.DigicardCore     `inject:"client.digicardCore"`
	ClearingRecordDBHelper  storage.IClearingRecordDBHelper  `inject:"storage.clearingRecordDBHelper"`
	UserCardMappingDBHelper storage.IUserCardMappingDBHelper `inject:"storage.userCardMappingDBHelper"`
	CardTransactionDBHelper storage.ICardTransactionDBHelper `inject:"storage.cardTransactionDBHelper"`
}

func (c *clearingProcessorRegistryImpl) Register(processor IClearingRecordProcessor) {
	c.processors = append(c.processors, processor)
	sort.Slice(c.processors, func(i, j int) bool {
		return c.processors[j].Priority() > c.processors[i].Priority()
	})
}

func (c *clearingProcessorRegistryImpl) RunProcessorByPriority(ctx context.Context, record *dto.ClearingRecord) error {
	for _, processor := range c.processors {
		err := processor.Process(ctx, record) // Process the record
		if errors.Is(err, errProcessorNotApplicable) {
			continue
		}
		// no checking needed, caller to check
		// if err == nil, then the record has been processed successfully
		// if err != nil, meaning record is found but the record has not been processed successfully, to be mark as FAILED for clearing record
		return err
	}

	return nil
}

// NewClearingProcessorRegistry ...
func NewClearingProcessorRegistry(app *v2.Application, txn IClearingRecordCore) IClearingRecordProcessorRegistry {
	registry := &clearingProcessorRegistryImpl{}
	list := []IClearingRecordProcessor{
		NewRefundProcessor(txn),
		NewACProcessor(txn),
		NewForcePostProcessor(txn),
		NewReversalIndicatorProcessor(),
	}
	for _, processor := range list {
		injectErr := app.Inject(processor)
		if injectErr != nil {
			panic(injectErr)
		}
		app.GetLogger().Info(registryLogTag, fmt.Sprintf("registering clearing processors [%v]", processor.Name()))
		registry.Register(processor)
	}
	return registry
}
