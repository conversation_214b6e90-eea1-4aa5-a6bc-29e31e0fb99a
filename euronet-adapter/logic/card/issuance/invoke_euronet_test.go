package issuance

import (
	"context"
	"encoding/json"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/mocks"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func TestInvokeEuronetIssuance(t *testing.T) {
	errDummy := errors.New("simulate error")
	jsonBody, _ := json.Marshal(dto.IssuanceRequest{
		DebitCardIssuanceRequest: &dto.DebitCardIssuanceRequest{},
		Signature:                "",
	})
	scenarios := []struct {
		euronetResp      *dto.IssuanceResponse
		errEuronet       error
		expectedErr      error
		expectedState    workflowengine.State
		description      string
		errStorage       error
		cardIssuanceType string
	}{
		{
			euronetResp: nil,
			errEuronet:  errDummy,
			expectedErr: constant.ErrRetryable,
			description: "issuance failed",
			errStorage:  nil,
		},
		{
			euronetResp: nil,
			errEuronet:  nil,
			expectedErr: api.DefaultInternalServerError,
			description: "nil response",
			errStorage:  nil,
		},
		{
			euronetResp: &dto.IssuanceResponse{},
			errEuronet:  nil,
			expectedErr: api.DefaultInternalServerError,
			description: "nil DebitCardIssuanceResponse",
			errStorage:  nil,
		},
		{
			euronetResp: &dto.IssuanceResponse{DebitCardIssuanceResponse: &dto.DebitCardIssuanceResponse{}},
			errEuronet:  nil,
			expectedErr: api.DefaultInternalServerError,
			description: "nil DebitCardIssuanceResponse.Response",
			errStorage:  nil,
		},
		{
			euronetResp: &dto.IssuanceResponse{
				DebitCardIssuanceResponse: &dto.DebitCardIssuanceResponse{
					Response: &dto.Response{
						ResponseCode: "01",
						Description:  "",
					},
				}},
			errEuronet:    nil,
			expectedErr:   nil,
			description:   "error in response",
			errStorage:    nil,
			expectedState: stFailed,
		},
		{
			euronetResp: &dto.IssuanceResponse{
				DebitCardIssuanceResponse: &dto.DebitCardIssuanceResponse{
					Response: &dto.Response{
						ResponseCode: "invalid",
						Description:  "",
					},
				}},
			errEuronet:    nil,
			expectedErr:   nil,
			description:   "invalid response code",
			errStorage:    nil,
			expectedState: stFailed,
		},
		{
			euronetResp: &dto.IssuanceResponse{
				DebitCardIssuanceResponse: &dto.DebitCardIssuanceResponse{
					Response: &dto.Response{
						ResponseCode: "00",
						Description:  "",
					},
				}},
			errEuronet:    nil,
			expectedErr:   nil,
			description:   "success",
			errStorage:    nil,
			expectedState: stVirtualCardIssued,
		},
		{
			euronetResp: &dto.IssuanceResponse{
				DebitCardIssuanceResponse: &dto.DebitCardIssuanceResponse{
					Response: &dto.Response{
						ResponseCode: "99",
						Description:  physicalAlreadyIssued,
					},
				}},
			errEuronet:       nil,
			expectedErr:      nil,
			description:      "success physicalAlreadyIssued 99",
			errStorage:       nil,
			expectedState:    stSuccess,
			cardIssuanceType: string(constant.PhysicalCard),
		},
		{
			euronetResp: &dto.IssuanceResponse{
				DebitCardIssuanceResponse: &dto.DebitCardIssuanceResponse{
					Response: &dto.Response{
						ResponseCode: "34",
						Description:  physicalAlreadyIssued,
					},
				}},
			errEuronet:       nil,
			expectedErr:      nil,
			description:      "success physicalAlreadyIssued 34",
			errStorage:       nil,
			expectedState:    stSuccess,
			cardIssuanceType: string(constant.PhysicalCard),
		},
	}

	for _, scenario := range scenarios {
		description := scenario.description

		mockEuronet := &mocks.EuronetClient{}
		mockEuronet.On("Issuance", mock.Anything, mock.Anything).Return(scenario.euronetResp, scenario.errEuronet)
		wfObj := &WorkflowImpl{
			StatsD:        statsd.NewNoop(),
			EuronetClient: mockEuronet,
		}
		mockCardActivity := storage.MockICardActivityDAO{}
		storage.CardActivityD = &mockCardActivity
		mockCardActivity.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(scenario.errStorage)
		cardIssuanceReq := &api.CardIssuanceRequest{
			CardIssuanceType: string(constant.VirtualCard),
		}
		if scenario.cardIssuanceType != "" {
			cardIssuanceReq.CardIssuanceType = scenario.cardIssuanceType
		}
		nextCtx, err := wfObj.invokeEuronetIssuance(context.Background(), "", &ExecutionData{
			CardActivity: storage.CardActivity{
				RequestBody: jsonBody,
			},
			CardIssuanceRequest: cardIssuanceReq,
		}, nil)
		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, scenario.expectedErr, err, description)
			assert.Nil(t, nextCtx, description)
		} else {
			assert.NoError(t, err, description)
			assert.NotNil(t, nextCtx, description)
			assert.Equal(t, scenario.expectedState, nextCtx.(*ExecutionData).State, description)
		}
	}
}
