package issuance

import (
	"context"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
)

// Workflow defines interface for card issuance
type Workflow interface {
	// ExecuteCardIssuanceWorkflow execute new workflow for card issuance
	ExecuteCardIssuanceWorkflow(ctx context.Context, req *api.CardIssuanceRequest) (*api.CardIssuanceResponse, error)
}

//go:generate mockery --name Workflow --inpackage --case=underscore
