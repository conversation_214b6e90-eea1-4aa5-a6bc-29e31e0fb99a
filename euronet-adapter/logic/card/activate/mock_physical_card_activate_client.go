// Code generated by mockery v2.16.0. DO NOT EDIT.

package activate

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"

	mock "github.com/stretchr/testify/mock"
)

// MockPhysicalCardActivateClient is an autogenerated mock type for the PhysicalCardActivateClient type
type MockPhysicalCardActivateClient struct {
	mock.Mock
}

// PhysicalCardActivate provides a mock function with given fields: ctx, request
func (_m *MockPhysicalCardActivateClient) PhysicalCardActivate(ctx context.Context, request *api.PhysicalCardActivateRequest) (*api.PhysicalCardActivateResponse, error) {
	ret := _m.Called(ctx, request)

	var r0 *api.PhysicalCardActivateResponse
	if rf, ok := ret.Get(0).(func(context.Context, *api.PhysicalCardActivateRequest) *api.PhysicalCardActivateResponse); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.PhysicalCardActivateResponse)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, *api.PhysicalCardActivateRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMockPhysicalCardActivateClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockPhysicalCardActivateClient creates a new instance of MockPhysicalCardActivateClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockPhysicalCardActivateClient(t mockConstructorTestingTNewMockPhysicalCardActivateClient) *MockPhysicalCardActivateClient {
	mock := &MockPhysicalCardActivateClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
