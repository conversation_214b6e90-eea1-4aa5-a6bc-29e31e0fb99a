package statusupdate

import (
	"context"
	"net/http"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

const (
	success       = "SUCCESS"
	failed        = "FAILED"
	invalidCardID = "Invalid Card ID"
	noSuchCard    = "No such card found"
)

func (w *WorkflowImpl) validate(ctx context.Context, transitionID string, exec workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := exec.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Debug("debug", "error in getting current context", slog.CustomTag("error", ok))
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	query := []data.Condition{
		data.EqualTo("InternalCardID", nextCtx.Request.CardID),
	}
	querySearch, err := storage.UserCardMappingD.Find(ctx, query...)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error in finding card ID", slog.Error(err))
		w.Stats(ctx, logic.FailedTag, noSuchCard)
		return nil, logic.CustomError(http.StatusBadRequest, &[]servus.ErrorDetail{
			{
				ErrorCode: string(api.FieldInvalid),
				Message:   invalidCardID,
				Path:      noSuchCard,
			},
		})
	}
	nextCtx.ProxyNumber = querySearch[0].CardProxyNumber
	nextCtx.CardSequenceNumber = int64(querySearch[0].CardSequenceNumber)
	nextCtx.SetState(stValidated)
	return nextCtx, nil
}
