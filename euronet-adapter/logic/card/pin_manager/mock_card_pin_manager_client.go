// Code generated by mockery v2.16.0. DO NOT EDIT.

package pinmanager

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"

	mock "github.com/stretchr/testify/mock"
)

// MockCardPinManagerClient is an autogenerated mock type for the CardPinManagerClient type
type MockCardPinManagerClient struct {
	mock.Mock
}

// SetPin provides a mock function with given fields: ctx, request
func (_m *MockCardPinManagerClient) SetPin(ctx context.Context, request *api.CardSetPinRequest) (*api.CardSetPinResponse, error) {
	ret := _m.Called(ctx, request)

	var r0 *api.CardSetPinResponse
	if rf, ok := ret.Get(0).(func(context.Context, *api.CardSetPinRequest) *api.CardSetPinResponse); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.CardSetPinResponse)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, *api.CardSetPinRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMockCardPinManagerClient interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockCardPinManagerClient creates a new instance of MockCardPinManagerClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockCardPinManagerClient(t mockConstructorTestingTNewMockCardPinManagerClient) *MockCardPinManagerClient {
	mock := &MockCardPinManagerClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
