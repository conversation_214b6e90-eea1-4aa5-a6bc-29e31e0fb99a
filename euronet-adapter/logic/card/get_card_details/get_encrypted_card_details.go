package getcarddetails

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/google/uuid"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common/security"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/card"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var (
	getEncryptedCVV2CardDetailsLogTag = "GetEncryptedCVV2CardDetails"
)

// nolint:funlen
// GetEncryptedCVV2CardDetails get cvv2 info from Euronet and encrypt the data immediately
func (c *clientImpl) GetEncryptedCVV2CardDetails(ctx context.Context, req *api.GetEncryptedCVV2CardDetailsRequest) (*api.GetEncryptedCVV2CardDetailsResponse, error) {
	logger := slog.FromContext(ctx)

	if errs := validateEncryptedGetCardDetailsParams(ctx, req); len(errs) != 0 {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "invalid request parameters")
		c.Stats(logic.FailedTag, invalidReq)
		return nil, logic.CustomError(http.StatusBadRequest, &errs)
	}

	cardMapping, errFindUser := findUserCardMappingByID(ctx, req.CardID)
	if errFindUser != nil {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "unable to find user card mapping", slog.Error(errFindUser), slog.CustomTag("internalCardID", req.CardID))
		c.Stats(logic.FailedTag, "error finding requested internal card ID")
		return nil, logic.ErrInvalidParameters
	}

	logger.Info(getEncryptedCVV2CardDetailsLogTag, "start to decrypt device ephemeral public key")
	//TODO: Remove this after finishing E2E encryption flow
	//https://gxsbank.atlassian.net/browse/CARDS-976
	slog.FromContext(ctx).Debug(logTag, fmt.Sprintf("DEBUG LOGS. dc-core get card details request: %+v", req))

	externalPubKey, errParsePubKey := security.ParseECDSAPublicKey(ctx, []byte(req.PublicKey))
	if errParsePubKey != nil {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "unable to parse pinned private key", slog.Error(errParsePubKey))
		c.Stats(logic.FailedTag, "error parse public key")
		return nil, logic.ErrInvalidParameters
	}

	pinnedPrivateKey, errParsePrivKey := security.ParseECDSAPrivateKey(ctx, c.CardInfoStaticPrivateKey)
	if errParsePrivKey != nil {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "unable to parse pinned private key", slog.Error(errParsePrivKey))
		c.Stats(logic.FailedTag, "error parse private key")
		return nil, logic.ErrGenericServer
	}

	salt, _ := base64.StdEncoding.DecodeString(req.Salt)
	iv, _ := base64.StdEncoding.DecodeString(req.Iv)
	encryptedData, _ := base64.StdEncoding.DecodeString(req.EncryptedData)

	var decryptedData []byte
	var errDecrypt error
	if c.FeatureFlags.EnableStaticECDHDecryptV2 {
		decryptedData, errDecrypt = c.SecurityClient.StaticECDHDecryptV2(ctx, externalPubKey, pinnedPrivateKey, salt, iv, encryptedData)
	} else {
		decryptedData, errDecrypt = c.SecurityClient.StaticECDHDecryptV1(ctx, externalPubKey, pinnedPrivateKey, salt, iv, encryptedData)
	}

	if errDecrypt != nil {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "unable to decrypt public key", slog.Error(errDecrypt))
		c.Stats(logic.FailedTag, "error decrypt public key")
		return nil, logic.ErrGenericServer
	}

	pubKeyForCardEncryption, errParsePubKey := security.ParseECDSAPublicKey(ctx, decryptedData)
	if errParsePubKey != nil {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "unable to parse public key", slog.Error(errParsePubKey))
		c.Stats(logic.FailedTag, "error parse public key")
		return nil, logic.ErrGenericServer
	}
	logger.Info(getEncryptedCVV2CardDetailsLogTag, "decrypt device ephemeral public key success")

	logger.Info(getEncryptedCVV2CardDetailsLogTag, "start to request CVV2 data")
	cardSeqNumber := cardMapping.CardSequenceNumber
	proxyNumber := cardMapping.CardProxyNumber
	euronetReq := &dto.GetCVV2Request{
		GetCVVRequest: &dto.GetCVVRequest{
			AppHeader: card.GetNewAppHeaderWithConfig(uuid.NewString(), logic.ServiceGetCVV2, c.CountryConfig.AppHeaderConfig),
			CardParams: &dto.CardParams{
				ProxyNumber:        proxyNumber,
				CardSeqNumber:      int64(cardSeqNumber),
				CardValueIndicator: logic.CardValueIndicatorClearFormat,
			},
		},
		Signature: ":", // TODO
	}

	enResp, errENResp := c.EuronetClient.GetCVV2(ctx, euronetReq)
	if errENResp != nil {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "error calling euronet getCVV2 API", slog.Error(errENResp))
		c.Stats(logic.FailedTag, "error calling euronet getCVV2 API")
		return nil, logic.ErrUnknownEuronetAPIError
	}

	if enResp == nil || enResp.GetCVVResponse == nil || enResp.GetCVVResponse.Response == nil {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "invalid euronet response")
		c.Stats(logic.FailedTag, "error calling euronet getCVV2 API")
		return nil, logic.ErrInvalidEuronetResponse
	}

	respCode := enResp.GetCVVResponse.Response.ResponseCode
	errObj, ok := card.EuronetErrorCodes[respCode]
	if errObj != nil {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "getcvv2 response status failed", slog.CustomTag(logic.StatusReasonTag, errObj.Error()))
		c.Stats(logic.FailedTag, "get card details failed")
		return nil, logic.ErrInvalidEuronetResponse
	}
	if !ok {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "getcvv2 response status code no match", slog.CustomTag(logic.EuronetResponseCodeTag, respCode))
		c.Stats(logic.FailedTag, "get card details failed")
		return nil, logic.ErrInvalidEuronetResponse
	}
	enResp.GetCVVResponse.CardInfo = getFormattedCardNumberAndExpiry(enResp.GetCVVResponse.CardInfo)
	logger.Info(getEncryptedCVV2CardDetailsLogTag, "request CVV2 data success")

	logger.Info(getEncryptedCVV2CardDetailsLogTag, "start to encrypt card info")
	cardInfo, errMarshal := json.Marshal(enResp.GetCVVResponse.CardInfo)
	if errMarshal != nil {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "unable to marshal card info", slog.Error(errMarshal))
		c.Stats(logic.FailedTag, "get card details failed")
		return nil, logic.ErrGenericServer
	}

	encryptedObj := c.SecurityClient.ECDHEncrypt(ctx, pubKeyForCardEncryption, cardInfo)
	if encryptedObj == nil {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "unable to encrypt card info")
		c.Stats(logic.FailedTag, "encrypt card info failed")
		return nil, logic.ErrGenericServer
	}
	pubKeyPem, errEncodePubKey := security.EncodePublicKeyToPem(ctx, encryptedObj.PublicKey)
	if errEncodePubKey != nil {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "unable to encode public key to pem format", slog.Error(errEncodePubKey))
		c.Stats(logic.FailedTag, "encrypt card info failed")
		return nil, logic.ErrGenericServer
	}
	logger.Info(getEncryptedCVV2CardDetailsLogTag, "encrypt card info success")

	return &api.GetEncryptedCVV2CardDetailsResponse{
		EncryptedCardDetails: base64.StdEncoding.EncodeToString(encryptedObj.EncryptedData),
		Salt:                 base64.StdEncoding.EncodeToString(encryptedObj.Salt),
		Iv:                   base64.StdEncoding.EncodeToString(encryptedObj.Iv),
		PublicKey:            pubKeyPem,
	}, nil
}

func getFormattedCardNumberAndExpiry(info *dto.CVV2CardInfo) *dto.CVV2CardInfo {
	// insert spaces every 4 digits
	cardNumber := info.CardNumber
	if len(info.CardNumber) == 16 {
		cardNumber = strings.Join([]string{info.CardNumber[0:4], info.CardNumber[4:8], info.CardNumber[8:12], info.CardNumber[12:16]}, " ")
	}
	info.CardNumber = cardNumber

	// convert from YYMM to MM/YY
	expiry := info.ExpiryDate
	if len(expiry) == 4 {
		expiry = strings.Join([]string{info.ExpiryDate[2:4], info.ExpiryDate[0:2]}, "/")
	}

	info.ExpiryDate = expiry
	return info
}

// TODO: find by internal card id and customer id
func findUserCardMappingByID(ctx context.Context, internalCardID string) (*storage.UserCardMapping, error) {
	logger := slog.FromContext(ctx)
	query := []data.Condition{
		data.EqualTo("InternalCardID", internalCardID),
	}
	cards, err := storage.UserCardMappingD.Find(ctx, query...)
	if err != nil {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "error in finding cardID", slog.Error(err), slog.CustomTag("InternalCardID", internalCardID))
		return nil, err
	}
	if len(cards) != 1 {
		logger.Error(getEncryptedCVV2CardDetailsLogTag, "error found duplicated card ID", slog.CustomTag("InternalCardID", internalCardID))
		return nil, errors.New("duplicated card")
	}
	return cards[0], nil
}

func validateEncryptedGetCardDetailsParams(ctx context.Context, req *api.GetEncryptedCVV2CardDetailsRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	customerID := logic.GetUserID(ctx)
	if customerID == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "X-Grab-Id-Userid' is missing.",
			Path:      "X-Grab-Id-Userid",
		})
	}
	if req.CardID == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "CardID is missing",
			Path:      "CardID",
		})
	}
	if req.PublicKey == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "PubKey is missing",
			Path:      "PubKey",
		})
	}
	if req.Iv == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "Iv is missing",
			Path:      "Iv",
		})
	}
	if req.Salt == "" {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "Salt is missing",
			Path:      "Salt",
		})
	}
	return errs
}
