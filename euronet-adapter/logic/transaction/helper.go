// Package transaction ...
package transaction

import (
	"encoding/json"
	"strings"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.myteksi.net/dakota/workflowengine"
)

const (
	// Authorization CardAcceptorTermName follows a strict length for each subfield based on MC Specs
	// Example: "Grab*                  Singapore     SGP"
	cardAcceptorMerchantNameIndex    = 22
	cardAcceptorMerchantCountryIndex = 23
)

// GetMerchantInfoFromAuthAcceptorName gets the merchant name, city from Authorization acceptor name
func GetMerchantInfoFromAuthAcceptorName(cardAcceptorTermName string) *logic.MerchantAcceptorTermInfo {
	return &logic.MerchantAcceptorTermInfo{
		MerchantName:                    strings.TrimSpace(cardAcceptorTermName[:cardAcceptorMerchantNameIndex+1]),
		MerchantCity:                    strings.TrimSpace(cardAcceptorTermName[cardAcceptorMerchantCountryIndex : len(cardAcceptorTermName)-3]),
		MerchantCardAcceptorCountryCode: cardAcceptorTermName[len(cardAcceptorTermName)-3:],
	}
}

// StateToStatus ... //TODO: consolidate all stateToStatus
func StateToStatus(state workflowengine.State) string {
	state /= 100.0
	switch state {
	case 0, 1, 2:
		return string(constant.TxnProcessing)
	case 3:
		return string(constant.TxnAuthorised)
	case 4, 5:
		return string(constant.TxnFailed)
	case 6:
		return string(constant.TxnCanceled)
	case 7:
		return string(constant.TxnReverted)
	case 9:
		return string(constant.TxnCompleted)
	}

	return string(constant.TxnProcessing)
}

// MarshalEmptyPropertiesJSON ...
func MarshalEmptyPropertiesJSON() json.RawMessage {
	resp := &api.TransactionProperties{}
	respStr, _ := json.Marshal(resp)
	return respStr
}
