package authorize

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	digicardTxnAPI "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	digicardTxnMock "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/workflowengine"
)

const (
	internalCardID = "1234"
)

func TestForcedCancel(t *testing.T) {
	errDummy := errors.New("some error")
	mockDigicardTxn := &digicardTxnMock.DigicardTransaction{}
	mockStorage := &storage.MockICardTransactionDAO{}
	mockUserCardMapping := &storage.MockIUserCardMappingDAO{}
	storage.UserCardMappingD = mockUserCardMapping
	storage.CardTransactionD = mockStorage
	wfObj := &WorkflowImpl{
		StatsD:            statsd.NewNoop(),
		DigicardTxnClient: mockDigicardTxn,
	}
	scenarios := []struct {
		desc            string
		currState       workflowengine.State
		respDigicardTxn *digicardTxnAPI.ChargeResponse
		errDigicardTxn  error
		errDBUpdate     error
		expectedErr     error
		expectedState   workflowengine.State
		mockFunc        func()
	}{
		{
			desc:           "stInit",
			currState:      stInit,
			errDigicardTxn: nil,
			errDBUpdate:    nil,
			expectedErr:    nil,
			expectedState:  stForcedCancelCompleted,
			mockFunc: func() {
				mockStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
			},
		},
		{
			desc:           "stRequestPersisted - AUTHORISED",
			currState:      stRequestPersisted,
			errDigicardTxn: nil,
			errDBUpdate:    nil,
			expectedErr:    nil,
			expectedState:  stCancelPrepared,
			mockFunc: func() {
				mockStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockUserCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{
					InternalCardID: internalCardID,
				}}, nil).Once()
				mockDigicardTxn.On("FetchChargeStatus", mock.Anything, mock.Anything).Return(&digicardTxnAPI.ChargeResponse{
					Data: &digicardTxnAPI.ChargeResource{
						Status: string(constant.TxnAuthorised),
						Id:     "some ID",
					},
				}, nil).Once()
			},
		},
		{
			desc:           "stRequestPersisted - CANCELED",
			currState:      stRequestPersisted,
			errDigicardTxn: nil,
			errDBUpdate:    nil,
			expectedErr:    nil,
			expectedState:  stForcedCancelCompleted,
			mockFunc: func() {
				mockStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockUserCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{
					InternalCardID: internalCardID,
				}}, nil).Once()
				mockDigicardTxn.On("FetchChargeStatus", mock.Anything, mock.Anything).Return(&digicardTxnAPI.ChargeResponse{
					Data: &digicardTxnAPI.ChargeResource{
						Status: string(constant.TxnCanceled),
						Id:     "some ID",
					},
				}, nil).Once()
			},
		},
		{
			desc:           "stRequestPersisted - FAILED",
			currState:      stRequestPersisted,
			errDigicardTxn: nil,
			errDBUpdate:    nil,
			expectedErr:    nil,
			expectedState:  stForcedCancelCompleted,
			mockFunc: func() {
				mockStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockUserCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{
					InternalCardID: internalCardID,
				}}, nil).Once()
				mockDigicardTxn.On("FetchChargeStatus", mock.Anything, mock.Anything).Return(&digicardTxnAPI.ChargeResponse{
					Data: &digicardTxnAPI.ChargeResource{
						Status: string(constant.TxnFailed),
						Id:     "some ID",
					},
				}, nil).Once()
			},
		},
		{
			desc:           "stRequestPersisted - PROCESSING",
			currState:      stRequestPersisted,
			errDigicardTxn: nil,
			errDBUpdate:    nil,
			expectedErr:    nil,
			expectedState:  stForcedCancelPrepared,
			mockFunc: func() {
				mockStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockUserCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{
					InternalCardID: internalCardID,
				}}, nil).Once()
				mockDigicardTxn.On("FetchChargeStatus", mock.Anything, mock.Anything).Return(&digicardTxnAPI.ChargeResponse{
					Data: &digicardTxnAPI.ChargeResource{
						Status: string(constant.TxnProcessing),
						Id:     "some ID",
					},
				}, nil).Once()
			},
		},
		{
			desc:           "stForcedCancelPrepared - AUTHORISED",
			currState:      stForcedCancelPrepared,
			errDigicardTxn: nil,
			errDBUpdate:    nil,
			expectedErr:    nil,
			expectedState:  stCancelPrepared,
			mockFunc: func() {
				mockStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockUserCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{
					InternalCardID: internalCardID,
				}}, nil).Once()
				mockDigicardTxn.On("FetchChargeStatus", mock.Anything, mock.Anything).Return(&digicardTxnAPI.ChargeResponse{
					Data: &digicardTxnAPI.ChargeResource{
						Status: string(constant.TxnAuthorised),
						Id:     "some ID",
					},
				}, nil).Once()
			},
		},
		{
			desc:           "stForcedCancelPrepared - CANCELED",
			currState:      stForcedCancelPrepared,
			errDigicardTxn: nil,
			errDBUpdate:    nil,
			expectedErr:    nil,
			expectedState:  stForcedCancelCompleted,
			mockFunc: func() {
				mockStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockUserCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{
					InternalCardID: internalCardID,
				}}, nil).Once()
				mockDigicardTxn.On("FetchChargeStatus", mock.Anything, mock.Anything).Return(&digicardTxnAPI.ChargeResponse{
					Data: &digicardTxnAPI.ChargeResource{
						Status: string(constant.TxnCanceled),
						Id:     "some ID",
					},
				}, nil).Once()
			},
		},
		{
			desc:           "stForcedCancelPrepared - FAILED",
			currState:      stForcedCancelPrepared,
			errDigicardTxn: nil,
			errDBUpdate:    nil,
			expectedErr:    nil,
			expectedState:  stForcedCancelCompleted,
			mockFunc: func() {
				mockStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockUserCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{
					InternalCardID: internalCardID,
				}}, nil).Once()
				mockDigicardTxn.On("FetchChargeStatus", mock.Anything, mock.Anything).Return(&digicardTxnAPI.ChargeResponse{
					Data: &digicardTxnAPI.ChargeResource{
						Status: string(constant.TxnFailed),
						Id:     "some ID",
					},
				}, nil).Once()
			},
		},
		{
			desc:           "stForcedCancelPrepared - PROCESSING",
			currState:      stForcedCancelPrepared,
			errDigicardTxn: nil,
			errDBUpdate:    nil,
			expectedErr:    errors.New("auth not yet completed, waiting for next attempt"),
			expectedState:  stForcedCancelPrepared,
			mockFunc: func() {
				mockUserCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{
					InternalCardID: internalCardID,
				}}, nil).Once()
				mockDigicardTxn.On("FetchChargeStatus", mock.Anything, mock.Anything).Return(&digicardTxnAPI.ChargeResponse{
					Data: &digicardTxnAPI.ChargeResource{
						Status: string(constant.TxnProcessing),
						Id:     "some ID",
					},
				}, nil).Once()
			},
		},
		{
			desc:           "fetch txn status failed",
			currState:      stForcedCancelPrepared,
			errDigicardTxn: errDummy,
			errDBUpdate:    nil,
			expectedErr:    errDummy,
			expectedState:  stCancelPrepared,
			mockFunc: func() {
				mockUserCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{
					InternalCardID: internalCardID,
				}}, nil).Once()
				mockDigicardTxn.On("FetchChargeStatus", mock.Anything, mock.Anything).Return(nil, errDummy).Once()
			},
		},
		{
			desc:      "db error",
			currState: stForcedCancelPrepared,
			respDigicardTxn: &digicardTxnAPI.ChargeResponse{
				Data: &digicardTxnAPI.ChargeResource{
					Status: string(constant.TxnAuthorised),
					Id:     "some ID",
				},
			},
			errDigicardTxn: nil,
			errDBUpdate:    errDummy,
			expectedErr:    errDummy,
			expectedState:  stCancelPrepared,
			mockFunc: func() {
				mockStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(errDummy).Once()
				mockUserCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.UserCardMapping{{
					InternalCardID: internalCardID,
				}}, nil).Once()
				mockDigicardTxn.On("FetchChargeStatus", mock.Anything, mock.Anything).Return(&digicardTxnAPI.ChargeResponse{
					Data: &digicardTxnAPI.ChargeResource{
						Status: string(constant.TxnFailed),
						Id:     "some ID",
					},
				}, nil).Once()
			},
		},
	}

	for _, scenario := range scenarios {
		description := scenario.desc
		scenario.mockFunc()
		nextCtx, err := wfObj.forcedCancel(context.Background(), "", &ExecutionData{
			CardTransaction: storage.CardTransaction{},
			State:           scenario.currState,
		}, nil)
		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, scenario.expectedErr, err, description)
			assert.Nil(t, nextCtx, description)
		} else {
			assert.NoError(t, err, description)
			assert.NotNil(t, nextCtx, description)
			assert.Equal(t, scenario.expectedState, nextCtx.GetState(), description)
		}
	}
}
