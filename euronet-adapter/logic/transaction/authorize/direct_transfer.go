package authorize

import (
	"context"
	"errors"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
)

// ExecuteDirectTransferWorkflow starts a forced posting from clearing worker
func (w *WorkflowImpl) ExecuteDirectTransferWorkflow(ctx context.Context, req *api.TransactionRequest, params *logic.Transaction, opParams dto.OfflineProcessingParams) error {
	data := &ExecutionData{
		AuthorizeRequest: req,
		State:            stInit,
		OpParams:         opParams,
	}
	requestID := req.AppHeader.ReqID
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.RequestIDTag, requestID),
		slog.CustomTag(logic.ServiceNameTag, req.AppHeader.ServiceName),
		slog.CustomTag(logic.TransactionIDTag, req.TransactionInfo.TransactionID),
		slog.CustomTag(logic.RRNTag, req.TransactionInfo.RetrievalReferenceNumber),
		slog.CustomTag(logic.ProxyNumberTag, req.TransactionInfo.ProxyNumber))

	if err := wfInit(ctx, workflowengine.Execution{
		WorkflowID: workflowID,
		RequestID:  requestID,
	}, data); err != nil {
		if err != workflowengine.ErrExecutionAlreadyExists {
			slog.FromContext(ctx).Warn(logAuth, "error when initiating workflow", slog.Error(err))
			return errors.New("error when initiating workflow")
		}
	}

	_, err := WfExecute(ctx, workflowengine.Execution{
		WorkflowID:     workflowID,
		RequestID:      requestID,
		ExecutionEvent: evPersistRequest,
	}, params)
	if err != nil {
		slog.FromContext(ctx).Warn(logAuth, "error when running workflow", slog.Error(err))
		return errors.New("error when running workflow")
	}
	return nil
}
