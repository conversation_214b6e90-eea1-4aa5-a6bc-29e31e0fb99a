package authorize

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/stats"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	digicardTxnAPI "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/file"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/transaction"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/common/currency"
	"gitlab.myteksi.net/dakota/common/transaction-code/deposits"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
)

func (w *WorkflowImpl) prepareCapture(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}
	nextCtx := currCtx.Clone()
	captureParams, ok2 := params.(ClearingCaptureParams)
	if !ok2 {
		return nil, logic.ErrWrongParamsPassed
	}
	nextCtx.CardTransaction.CaptureAmount = captureParams.CaptureAmount
	nextCtx.CardTransaction.CaptureOriginalAmount = captureParams.CaptureOriginalAmount
	if captureParams.ClearingRecord.AcquirerReferenceData != "" {
		nextCtx.CardTransaction.AcquirerReferenceData = captureParams.ClearingRecord.AcquirerReferenceData
	}
	if captureParams.ClearingRecord.AcquirerID != "" {
		nextCtx.CardTransaction.AcquirerID = captureParams.ClearingRecord.AcquirerID
	}
	nextCtx.OpParams.CaptureSource = captureParams.OpParams.CaptureSource
	if captureParams.OpParams.ClearingFailureID != 0 {
		nextCtx.OpParams.ClearingFailureID = captureParams.OpParams.ClearingFailureID
	}
	// Handle next state given capture type (partial/final)
	w.captureTypeHandler(ctx, nextCtx, &captureParams)

	// update auth txn to values in clearing
	err := ProcessOrigTxnWithClearingRecord(ctx, logCapture, &nextCtx.CardTransaction, &captureParams.ClearingRecord)
	if err != nil {
		// error from processing is ignored, will continue capture with original auth txn deteails
		slog.FromContext(ctx).Warn(logCapture, "error processing original txn with clearing record, resuming with auth record", slog.Error(err))
	}

	// Note: MRC (partial/final clearing type) info is not saved
	if err = logic.UpdateDBCardTxn(ctx, &currCtx.CardTransaction, &nextCtx.CardTransaction); err != nil {
		return nil, err
	}
	return nextCtx, nil
}

func (w *WorkflowImpl) capture(ctx context.Context, transitionID string, exec workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := exec.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logAuth, "Invalid context passed in capture state")
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	chargeReq := &digicardTxnAPI.CaptureChargeRequest{
		Id:             nextCtx.CardTransaction.InternalTransactionID,
		IdempotencyKey: nextCtx.CardTransaction.RequestID,
		AmountParams: &digicardTxnAPI.Amount{
			RequestCurrency:  currency.GetByNumericCode(nextCtx.CardTransaction.RequestCurrency).Code,
			RequestAmount:    nextCtx.CardTransaction.CaptureAmount, //TODO: update digicard-txn api to accept captureAmount
			OriginalAmount:   nextCtx.CardTransaction.CaptureOriginalAmount,
			OriginalCurrency: currency.GetByNumericCode(nextCtx.CardTransaction.OriginalCurrency).Code,
		},
		AcquirerReferenceData: nextCtx.CardTransaction.AcquirerReferenceData,
		AcquirerID:            nextCtx.CardTransaction.AcquirerID,
		PostingSource:         deposits.CUSTOMER,
	}
	// detect clearing txn
	if nextCtx.OpParams.CaptureSource == constant.OpsSource || nextCtx.OpParams.CaptureSource == constant.FileSource {
		chargeReq.PostingSource = deposits.BANK
	}

	resp, err := w.DigicardTxnClient.CaptureCharge(ctx, chargeReq)
	if err != nil {
		slog.FromContext(ctx).Warn(logCapture, "error calling digicard txn capture charge API", slog.Error(err))
		return nil, err
	}
	switch logic.RequestStatus(resp.Data.Status) {
	case logic.Success:
		nextCtx.State = stCaptureCompleted
		nextCtx.CardTransaction.CaptureAmountTillDate += nextCtx.CardTransaction.CaptureAmount
		nextCtx.CardTransaction.CaptureOriginalAmountTillDate += nextCtx.CardTransaction.CaptureOriginalAmount
		if nextCtx.OpParams.CaptureSource == constant.OpsSource {
			gconcurrent.Go(context.Background(), logCapture, func(c context.Context) error {
				return transaction.CompleteFailure(c, string(constant.Clearing), nextCtx.OpParams.ClearingFailureID)
			})
		}
	case logic.Processing:
		nextCtx.State = stCaptureProcessing
	case logic.Failed:
		nextCtx.State = stCaptureFailed
		failureParams := &dto.FailureParams{
			CardTransaction:  nextCtx.CardTransaction,
			ClearingType:     constant.Capture,
			IsPartialCapture: nextCtx.IsPartialCapture,
			FailureReason:    fmt.Sprintf("%s:%s", resp.Data.StatusReason, resp.Data.StatusReasonDescription),
			OpParams:         nextCtx.OpParams,
		}
		gconcurrent.Go(context.Background(), logAuth, func(c context.Context) error {
			return transaction.PersistClearingFailure(ctx, failureParams)
		})
	}
	if nextCtx.State == stCaptureCompleted || nextCtx.State == stCaptureFailed {
		nextCtx.CardTransaction.Status = transaction.StateToStatus(nextCtx.State)
		if err = logic.UpdateDBCardTxn(ctx, &currCtx.CardTransaction, &nextCtx.CardTransaction); err != nil {
			return nil, err
		}
	}
	return nextCtx, nil
}

func (w *WorkflowImpl) persistCaptureStream(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}
	nextCtx := currCtx.Clone()

	streamMessage, ok := params.(*dto.StreamMessage)
	if !ok {
		return nil, errors.New("wrong params passed")
	}

	nextCtx.StreamMessage = streamMessage
	nextCtx.State = stCaptureStreamPersisted
	if currCtx.IsPartialCapture {
		nextCtx.State = stPartialCaptureStreamPersisted
	}

	return nextCtx, nil
}

func (w *WorkflowImpl) captureTypeHandler(ctx context.Context, nextCtx *ExecutionData, captureParams *ClearingCaptureParams) {
	if file.CheckPartialCapture(captureParams.ClearingRecord.MRC) {
		slog.FromContext(ctx).Info(logPartialCapture, "partial settlement triggered")
		w.Stats(ctx, logPartialCapture, "")
		nextCtx.State = stPartialCapturePrepared
		nextCtx.IsPartialCapture = true
		return
	}
	nextCtx.State = stCapturePrepared
	nextCtx.IsPartialCapture = false
}

func (w *WorkflowImpl) completeCapture(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}
	nextCtx := currCtx.Clone()
	streamMessage := currCtx.StreamMessage

	if streamMessage.Status == string(constant.TxnCompleted) {
		nextCtx.State = stCaptureCompleted
		nextCtx.CardTransaction.CaptureAmountTillDate += nextCtx.CardTransaction.CaptureAmount
		nextCtx.CardTransaction.CaptureOriginalAmountTillDate += nextCtx.CardTransaction.CaptureOriginalAmount
		if streamMessage.ValueTimestamp != nil {
			nextCtx.CardTransaction.ValuedAt = *streamMessage.ValueTimestamp
		} else {
			nextCtx.CardTransaction.ValuedAt = time.Now()
		}
		if nextCtx.OpParams.CaptureSource == constant.OpsSource {
			gconcurrent.Go(context.Background(), logCapture, func(c context.Context) error {
				return transaction.CompleteFailure(c, string(constant.Clearing), nextCtx.OpParams.ClearingFailureID)
			})
		}
	} else {
		nextCtx.State = stCaptureFailed
		slog.FromContext(ctx).Warn(logCapture, fmt.Sprintf("capture error: %s", streamMessage.ReferenceID))
		failureParams := &dto.FailureParams{
			CardTransaction:  nextCtx.CardTransaction,
			ClearingType:     constant.Capture,
			IsPartialCapture: nextCtx.IsPartialCapture,
			FailureReason:    fmt.Sprintf("%s:%s", streamMessage.ErrorCode, streamMessage.ErrorMessage),
			OpParams:         nextCtx.OpParams,
		}
		gconcurrent.Go(context.Background(), logCapture, func(c context.Context) error {
			return transaction.PersistClearingFailure(ctx, failureParams)
		})
	}

	nextCtx.CardTransaction.Status = transaction.StateToStatus(nextCtx.State)
	if err := logic.UpdateDBCardTxn(ctx, &currCtx.CardTransaction, &nextCtx.CardTransaction); err != nil {
		return nil, err
	}
	return nextCtx, nil
}

// ProcessOrigTxnWithClearingRecord attempts to synchronize the original transaction with the clearing record for recon
func ProcessOrigTxnWithClearingRecord(ctx context.Context, logTag string, originalTxn *storage.CardTransaction, record *dto.ClearingRecord) error {
	merchantInfo := file.GetMerchantInfoFromClearingAcceptorName(record.AcceptorNameLocation)
	if merchantInfo == nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("record acceptorNameLocation is invalid: [%s]", record.AcceptorNameLocation))
		//return logic.ErrInvalidCardAcceptorTermName
	}
	originalTxnDateTime := originalTxn.LocalTransactionDatetime
	clearingLocalTxnDateTime, err := time.Parse(constant.FormatYYMMDDhhmmss, record.LocalTxnDateTime)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "unable to parse clearing file LocalTxnDateTime", slog.Error(err))
		return err
	}

	// log if the time (up to seconds) is not the same between Auth and Clearing
	if originalTxnDateTime.Second() != clearingLocalTxnDateTime.Second() {
		merchantName := record.AcceptorNameLocation
		if merchantInfo != nil {
			merchantName = merchantInfo.MerchantName
		}
		stats.StatsDClient.Count1(logic.EuronetAdapter, logic.ClearingLocalTxnDateTimeDifferenceStatsdTag,
			fmt.Sprintf("merchantName:%s", merchantName))
	}
	// TODO: Possible enhancement on merchant level in the future

	// updating LocalTransactionDatetime to clearing file to ensure EN is able to recon with our CBS Dump file
	originalTxn.LocalTransactionDatetime = clearingLocalTxnDateTime
	return nil
}
