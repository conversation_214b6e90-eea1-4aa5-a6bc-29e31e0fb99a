// Code generated by mockery v2.27.1. DO NOT EDIT.

package authorizepaynet

import (
	context "context"

	api "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"

	logic "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"

	mock "github.com/stretchr/testify/mock"
)

// MockWorkflow is an autogenerated mock type for the Workflow type
type MockWorkflow struct {
	mock.Mock
}

// ExecuteAuthorizeTransactionWorkflow provides a mock function with given fields: ctx, req, params
func (_m *MockWorkflow) ExecuteAuthorizeTransactionWorkflow(ctx context.Context, req *api.PaynetAuthorizeRequest_TransactionAuthorizeRequest, params *logic.Transaction) (*api.PaynetAuthorizeResponse_TransactionAuthorizeResponse, error) {
	ret := _m.Called(ctx, req, params)

	var r0 *api.PaynetAuthorizeResponse_TransactionAuthorizeResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.PaynetAuthorizeRequest_TransactionAuthorizeRequest, *logic.Transaction) (*api.PaynetAuthorizeResponse_TransactionAuthorizeResponse, error)); ok {
		return rf(ctx, req, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.PaynetAuthorizeRequest_TransactionAuthorizeRequest, *logic.Transaction) *api.PaynetAuthorizeResponse_TransactionAuthorizeResponse); ok {
		r0 = rf(ctx, req, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.PaynetAuthorizeResponse_TransactionAuthorizeResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.PaynetAuthorizeRequest_TransactionAuthorizeRequest, *logic.Transaction) error); ok {
		r1 = rf(ctx, req, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ExecuteReversalPreAuthWorkflow provides a mock function with given fields: ctx, req
func (_m *MockWorkflow) ExecuteReversalPreAuthWorkflow(ctx context.Context, req *api.PaynetReversalPreAuthRequest) (*api.PaynetReversalPreAuthResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *api.PaynetReversalPreAuthResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.PaynetReversalPreAuthRequest) (*api.PaynetReversalPreAuthResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.PaynetReversalPreAuthRequest) *api.PaynetReversalPreAuthResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.PaynetReversalPreAuthResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.PaynetReversalPreAuthRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ExecuteReversalSettledWorkflow provides a mock function with given fields: ctx, req, params
func (_m *MockWorkflow) ExecuteReversalSettledWorkflow(ctx context.Context, req *api.PaynetReversalSettledRequest, params *logic.Transaction) (*api.PaynetReversalSettledResponse, error) {
	ret := _m.Called(ctx, req, params)

	var r0 *api.PaynetReversalSettledResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.PaynetReversalSettledRequest, *logic.Transaction) (*api.PaynetReversalSettledResponse, error)); ok {
		return rf(ctx, req, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.PaynetReversalSettledRequest, *logic.Transaction) *api.PaynetReversalSettledResponse); ok {
		r0 = rf(ctx, req, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.PaynetReversalSettledResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.PaynetReversalSettledRequest, *logic.Transaction) error); ok {
		r1 = rf(ctx, req, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ExecuteSaleCompletionTransactionWorkflow provides a mock function with given fields: ctx, request
func (_m *MockWorkflow) ExecuteSaleCompletionTransactionWorkflow(ctx context.Context, request *api.PaynetSaleCompletionRequest_TransactionSaleCompletionRequest) (*api.PaynetSaleCompletionResponse_TransactionSaleCompletionResponse, error) {
	ret := _m.Called(ctx, request)

	var r0 *api.PaynetSaleCompletionResponse_TransactionSaleCompletionResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *api.PaynetSaleCompletionRequest_TransactionSaleCompletionRequest) (*api.PaynetSaleCompletionResponse_TransactionSaleCompletionResponse, error)); ok {
		return rf(ctx, request)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *api.PaynetSaleCompletionRequest_TransactionSaleCompletionRequest) *api.PaynetSaleCompletionResponse_TransactionSaleCompletionResponse); ok {
		r0 = rf(ctx, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*api.PaynetSaleCompletionResponse_TransactionSaleCompletionResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *api.PaynetSaleCompletionRequest_TransactionSaleCompletionRequest) error); ok {
		r1 = rf(ctx, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMockWorkflow interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockWorkflow creates a new instance of MockWorkflow. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockWorkflow(t mockConstructorTestingTNewMockWorkflow) *MockWorkflow {
	mock := &MockWorkflow{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
