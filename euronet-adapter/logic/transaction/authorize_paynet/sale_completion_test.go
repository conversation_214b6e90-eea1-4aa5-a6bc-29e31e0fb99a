package authorizepaynet

import (
	"context"
	"errors"
	"testing"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func TestExecuteSaleCompletionTransactionWorkflow(t *testing.T) {
	testRequestID := uuid.NewString()
	rrn := "123456789012"

	saleCompletionReq := &api.PaynetSaleCompletionRequest_TransactionSaleCompletionRequest{
		AppHeader: &api.AppHeader{
			ReqID: testRequestID,
		},
		TransactionInfo: &api.PaynetSaleCompletionRequest_SaleCompletionReq{
			RetrievalReferenceNumber: rrn,
			TransactionAmount:        1,
		},
	}
	cancelReq := &api.PaynetSaleCompletionRequest_TransactionSaleCompletionRequest{
		AppHeader: &api.AppHeader{
			ReqID: testRequestID,
		},
		TransactionInfo: &api.PaynetSaleCompletionRequest_SaleCompletionReq{
			RetrievalReferenceNumber: rrn,
			TransactionAmount:        0,
		},
	}

	type mockExecutionData struct {
		executionData *ExecutionData
		err           error
	}

	testCases := []struct {
		saleCompletionReq *api.PaynetSaleCompletionRequest_TransactionSaleCompletionRequest
		responseCode      string
		errInit           error
		errGet            error
		errExecute        error
		expectedErr       error
		name              string
		wfGetMock         mockExecutionData
		wfExecuteMock     mockExecutionData
		mti               string
	}{
		{
			saleCompletionReq: saleCompletionReq,
			name:              "sale completion happy case",
			responseCode:      "0000",
			wfGetMock: mockExecutionData{
				executionData: &ExecutionData{State: stAuthorized},
				err:           nil,
			},
			wfExecuteMock: mockExecutionData{
				executionData: &ExecutionData{State: stCaptureCompleted},
				err:           nil,
			},
			mti: logic.SaleCompletionMTI,
		},
		{
			saleCompletionReq: saleCompletionReq,
			name:              "sale completion time out failed",
			responseCode:      "1341",
			wfGetMock: mockExecutionData{
				executionData: &ExecutionData{State: stAuthorized},
				err:           nil,
			},
			wfExecuteMock: mockExecutionData{
				executionData: &ExecutionData{State: stCaptureProcessing},
				err:           nil,
			},
			mti:         logic.SaleCompletionRepeatMTI,
			expectedErr: logic.ErrAuthTimeOut,
		},
		{
			saleCompletionReq: saleCompletionReq,
			name:              "sale completion failed case",
			responseCode:      "1000",
			wfGetMock: mockExecutionData{
				executionData: &ExecutionData{State: stAuthorized},
				err:           nil,
			},
			wfExecuteMock: mockExecutionData{
				executionData: &ExecutionData{State: stFailed},
				err:           nil,
			},
			mti: logic.SaleCompletionRepeatMTI,
		},
		{
			saleCompletionReq: cancelReq,
			name:              "cancel pre-auth happy case",
			responseCode:      "0000",
			wfGetMock: mockExecutionData{
				executionData: &ExecutionData{State: stAuthorized},
				err:           nil,
			},
			wfExecuteMock: mockExecutionData{
				executionData: &ExecutionData{State: stCaptureCompleted},
				err:           nil,
			},
			mti: logic.SaleCompletionRepeatMTI,
		},
		{
			saleCompletionReq: cancelReq,
			name:              "cancel pre-auth time out failed",
			responseCode:      "1341",
			wfGetMock: mockExecutionData{
				executionData: &ExecutionData{State: stAuthorized},
				err:           nil,
			},
			wfExecuteMock: mockExecutionData{
				executionData: &ExecutionData{State: stCaptureProcessing},
				err:           nil,
			},
			mti:         logic.SaleCompletionMTI,
			expectedErr: logic.ErrAuthTimeOut,
		},
		{
			saleCompletionReq: cancelReq,
			name:              "cancel pre-auth failed case",
			responseCode:      "1000",
			wfGetMock: mockExecutionData{
				executionData: &ExecutionData{State: stAuthorized},
				err:           nil,
			},
			wfExecuteMock: mockExecutionData{
				executionData: &ExecutionData{State: stFailed},
				err:           nil,
			},
			mti: logic.SaleCompletionRepeatMTI,
		},
	}

	for _, tc := range testCases {
		test := tc
		t.Run(test.name, func(t *testing.T) {
			ctx := context.Background()
			wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
				return test.wfGetMock.executionData, nil
			}
			wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
				return test.wfExecuteMock.executionData, nil
			}
			mockStorageDAO := &storage.MockICardTransactionDAO{}
			storage.CardTransactionD = mockStorageDAO
			mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardTransaction{{}}, nil).Once()

			w := WorkflowImpl{
				WorkflowRetryConfig: &config.WorkflowRetryConfig{
					SaleCompletionStateRetry: &config.RetryPolicy{
						IntervalInMilliSeconds: 10,
						MaxAttempt:             5,
					},
				},
			}
			test.saleCompletionReq.TransactionInfo.Mti = test.mti
			resp, err := w.ExecuteSaleCompletionTransactionWorkflow(ctx, test.saleCompletionReq)
			if test.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, test.expectedErr, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.responseCode, resp.Response.ResponseCode)
				assert.NotNil(t, resp)
				assert.Equal(t, "0130", resp.TransactionInfo.Mti)
			}
		})
	}
}

func Test_getCardOriginTxn(t *testing.T) {
	tests := []struct {
		name     string
		mockFunc func()
		wantErr  error
	}{
		{
			name: "err ErrNoData",
			mockFunc: func() {
				mockStorageDAO := &storage.MockICardTransactionDAO{}
				storage.CardTransactionD = mockStorageDAO
				mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardTransaction{{}}, data.ErrNoData).Once()
			},
			wantErr: data.ErrNoData,
		},
		{
			name: "err",
			mockFunc: func() {
				mockStorageDAO := &storage.MockICardTransactionDAO{}
				storage.CardTransactionD = mockStorageDAO
				mockStorageDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return([]*storage.CardTransaction{{}}, errors.New("err")).Once()
			},
			wantErr: errors.New("err"),
		},
	}
	for _, tt := range tests {
		tc := tt
		t.Run(tc.name, func(t *testing.T) {
			tc.mockFunc()
			_, err := getCardOriginTxn(context.Background(), &api.PaynetSaleCompletionRequest_SaleCompletionReq{})
			assert.Equal(t, tc.wantErr, err)
		})
	}
}
