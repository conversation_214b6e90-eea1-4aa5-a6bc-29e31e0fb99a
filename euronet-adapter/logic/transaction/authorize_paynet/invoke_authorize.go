package authorizepaynet

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	commoncfg "gitlab.com/gx-regional/dbmy/digicard/common/config"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/cache"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"

	digicardTxnAPI "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/common/currency"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/transaction"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
)

func (w *WorkflowImpl) authorize(
	ctx context.Context,
	transitionID string,
	exec workflowengine.ExecutionData,
	params interface{},
) (workflowengine.ExecutionData, error) {
	currCtx, ok := exec.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logAuth, "Invalid context passed in authorize state")
		return nil, logic.ErrInvalidContext
	}
	nextCtx := currCtx.Clone()
	chargeReq, err := constructChargeRequest(ctx, nextCtx, w.Locale, w.AtmFeesWaiverConfig)
	if err != nil {
		nextCtx.State = stFailed
		nextCtx.CardTransaction.Status = transaction.StateToStatus(stFailed)
		if errors.Is(err, logic.ErrProxyNumberNotFound) {
			nextCtx.CardTransaction.ResponseCode = logic.ErrInvalidProxyNumber.Code
			nextCtx.ResponseCode = logic.ErrInvalidProxyNumber.Code
			nextCtx.Description = logic.ErrInvalidProxyNumber.Message
		} else {
			slog.FromContext(ctx).Warn(logAuth, "error constructing charge request", slog.Error(err))
			return nil, err
		}
	} else {
		var resp *digicardTxnAPI.ChargeResponse
		resp, err = w.DigicardTxnClient.ChargeTransaction(ctx, chargeReq)
		if err != nil {
			slog.FromContext(ctx).Warn(logAuth, "error calling digicard txn charge API", slog.Error(err))
			return nil, err
		}
		if resp == nil || resp.Data == nil {
			return nil, logic.ErrInvalidResponseStatus
		}

		authResp := constructPaynetAuthResponse(nextCtx, nextCtx.CardTransaction.AuthorizationID)
		nextCtx.CardTransaction.ResponseBody = marshallResponseBody(authResp)
		nextCtx.CardTransaction.InternalTransactionID = resp.Data.Id
		nextCtx.ChargeResponse = resp

		//Decide what state should this flow transition to based on charge response status
		if err = w.handleStateTransition(ctx, nextCtx, resp.Data.Status); err != nil {
			return nil, err
		}
	}
	nextCtx.CardTransaction.Status = transaction.StateToStatus(nextCtx.State)
	nextCtx.CardTransaction.UpdatedAt = logic.Now()
	if err = storage.CardTransactionD.UpdateEntity(ctx, &currCtx.CardTransaction, &nextCtx.CardTransaction); err != nil {
		slog.FromContext(ctx).Warn(logAuth, "error updating card transaction response to DB", slog.Error(err))
		return nil, err
	}
	return nextCtx, nil
}

func (w *WorkflowImpl) handleStateTransition(ctx context.Context, nextCtx *ExecutionData, status string) error {
	switch status {
	case string(logic.Completed):
		slog.FromContext(ctx).Info(logAuth, "automatic capture method, updating transaction status to completed")
		w.Stats(ctx, logSuccess, "")
		nextCtx.ResponseCode = logic.ApprovedCode
		nextCtx.Description = logic.Approved
		nextCtx.CardTransaction.CaptureAmount = nextCtx.TransactionAuthorizeRequest.TransactionInfo.TransactionAmount
		nextCtx.CardTransaction.ValuedAt = nextCtx.ChargeResponse.Data.ValueTimestamp
		nextCtx.SetState(stCaptureCompleted)
		_ = storeSuccessfulAtmWithdrawalForFeesWaiver(ctx, nextCtx.CardTransaction, w.Locale, w.AtmFeesWaiverConfig) //TCH-2122 store successful ATM withdrawal in Redis
	case string(logic.Authorized):
		slog.FromContext(ctx).Info(logAuth, "manual capture method, updating transaction status to successful")
		w.Stats(ctx, logSuccess, "")
		nextCtx.ResponseCode = logic.ApprovedCode
		nextCtx.Description = logic.Approved
		nextCtx.SetState(stAuthorized)
	case string(logic.Failed):
		slog.FromContext(ctx).Warn(logAuth, "failed status response from charge transaction call", slog.CustomTag(logic.StatusReasonTag, nextCtx.ChargeResponse.Data.StatusReason))
		w.Stats(ctx, logFailed, "failed status response from charge transaction call")
		nextCtx.ResponseCode, nextCtx.Description = resolveResponseCode(nextCtx.ChargeResponse.Data.StatusReason)
		nextCtx.SetState(stFailed)
	case string(logic.Processing): // still in processing means we have timeout
		slog.FromContext(ctx).Info(logAuth, "timeout has occurred. it will be cancelled")
		w.Stats(ctx, logFailed, "timeout")
		return errors.New(constant.TransactionSLABreach)
	default:
		slog.FromContext(ctx).Warn(logAuth, "invalid transaction response status")
		return logic.ErrInvalidResponseStatus
	}
	return nil
}

func constructChargeRequest(ctx context.Context, nextCtx *ExecutionData, locale *commoncfg.Locale, atmFeesWaiverConfig *config.AtmFeesWaiverConfig) (*digicardTxnAPI.ChargeRequest, error) {
	authReq := nextCtx.TransactionAuthorizeRequest
	cardTransaction := nextCtx.CardTransaction

	customerSafeID, internalCardID, err := logic.FetchCustomerAndCardId(ctx, cardTransaction.ProxyNumber, logAuth)
	if err != nil {
		if errors.Is(err, data.ErrNoData) {
			return nil, logic.ErrProxyNumberNotFound
		}
		return nil, err
	}

	transactionType := cardTransaction.TransactionType
	chargeType := logic.PaynetTransactionTypeToChargeType[constant.TransactionType(transactionType)].ChargeType
	captureMethod := logic.PaynetTransactionTypeToChargeType[constant.TransactionType(transactionType)].CaptureMethod

	idempotencyKey := CreateWorkflowRunID(
		authReq.TransactionInfo.RetrievalReferenceNumber,
		authReq.TransactionInfo.NetworkID,
		authReq.TransactionInfo.ProxyNumber,
	)
	chargeReq := &digicardTxnAPI.ChargeRequest{
		CardID:         internalCardID,
		VendorChargeID: authReq.TransactionInfo.RetrievalReferenceNumber,
		ChargeType:     chargeType,
		AmountParams: &digicardTxnAPI.Amount{
			RequestAmount:    authReq.TransactionInfo.TransactionAmount,
			RequestCurrency:  currency.GetByNumericCode(authReq.TransactionInfo.TransactionCurrencyCode).Code,
			OriginalAmount:   authReq.TransactionInfo.TransactionAmount,
			OriginalCurrency: currency.GetByNumericCode(authReq.TransactionInfo.TransactionCurrencyCode).Code,
		},
		VendorChargeTimestamp: authReq.TransactionInfo.TransmissionDateTime,
		MerchantDescription:   authReq.TransactionInfo.CardAcceptorTermName,
		MerchantID:            authReq.TransactionInfo.CardAcceptorMerchantID,
		MCC:                   authReq.TransactionInfo.MerchantCategoryCode,
		CaptureMethod:         string(captureMethod),
		IdempotencyKey:        idempotencyKey,
		CaptureMode:           cardTransaction.Category,
		MetaData: &digicardTxnAPI.MetaData{
			AcquirerReferenceData:    cardTransaction.AcquirerReferenceData,
			TransactionCategory:      cardTransaction.Category,
			TransactionType:          transactionType,
			RetrievalReferenceNumber: authReq.TransactionInfo.RetrievalReferenceNumber,
			AuthorizationID:          authReq.TransactionInfo.AuthorizationID,
			ThreeDsValidation:        authReq.TransactionInfo.ThreeDsValidation,
			CurrencyConvType:         cardTransaction.CurrencyConvType,
			TransactionSubCategory:   cardTransaction.SubCategory,
			TerminalID:               authReq.TransactionInfo.CardAcceptorTerminalID,
			NetworkID:                authReq.TransactionInfo.NetworkID,
			PinValidation:            authReq.TransactionInfo.PinValidation,
			CardProxyNumber:          authReq.TransactionInfo.ProxyNumber,
			Mcc:                      authReq.TransactionInfo.MerchantCategoryCode,
			AcceptorCountryCode:      cardTransaction.AcceptorCountryCode,
			RetailerID:               cardTransaction.RetailerID,
		},
		AdditionalCharges: createAdditionalCharge(ctx, cardTransaction, customerSafeID, locale, atmFeesWaiverConfig),
	}
	return chargeReq, nil
}

// constructPaynetAuthResponse TODO: should utilize the mapper
func constructPaynetAuthResponse(nextCtx *ExecutionData, authID string) *api.PaynetAuthorizeResponse_TransactionAuthorizeResponse {
	authReq := nextCtx.TransactionAuthorizeRequest
	authResp := &api.PaynetAuthorizeResponse_TransactionAuthorizeResponse{
		AppHeader: authReq.AppHeader,
		TransactionInfo: &api.PaynetAuthorizeResponse_AuthorizeRes{
			Mti:                      authReq.TransactionInfo.Mti,
			ProxyNumber:              authReq.TransactionInfo.ProxyNumber,
			ProcessingCode:           authReq.TransactionInfo.ProcessingCode,
			TransactionAmount:        authReq.TransactionInfo.TransactionAmount,
			TransmissionDateTime:     authReq.TransactionInfo.TransmissionDateTime,
			SystemTraceAuditNumber:   authReq.TransactionInfo.SystemTraceAuditNumber,
			LocalTransactionDateTime: authReq.TransactionInfo.LocalTransactionDateTime,
			MerchantCategoryCode:     authReq.TransactionInfo.MerchantCategoryCode,
			AcquirerCountryCode:      authReq.TransactionInfo.AcquirerCountryCode,
			AcquirerID:               authReq.TransactionInfo.AcquirerID,
			RetrievalReferenceNumber: authReq.TransactionInfo.RetrievalReferenceNumber,
			AuthorizationID:          authReq.TransactionInfo.AuthorizationID,
			CardAcceptorTerminalID:   authReq.TransactionInfo.CardAcceptorTerminalID,
			CardAcceptorTermName:     authReq.TransactionInfo.CardAcceptorTermName,
			TransactionCurrencyCode:  authReq.TransactionInfo.TransactionCurrencyCode,
			CardHolderCurrencyCode:   authReq.TransactionInfo.CardHolderCurrencyCode,
			TransactionID:            authReq.TransactionInfo.TransactionID,
			OrigTransactionID:        authReq.TransactionInfo.OrigTransactionID,
		},
		Response: &api.PaynetAuthorizeResponse_Response{
			ResponseCode: nextCtx.ResponseCode,
			Description:  nextCtx.Description,
		},
	}
	authResp.TransactionInfo.AuthorizationID = authID
	return authResp
}

func marshallResponseBody(respObj *api.PaynetAuthorizeResponse_TransactionAuthorizeResponse) json.RawMessage {
	respStr, _ := json.Marshal(respObj)
	return respStr
}

func resolveResponseCode(statusReason string) (string, string) {
	adapterErr := logic.GetResponseCode(statusReason)
	return adapterErr.Code, adapterErr.Message
}

func createAdditionalCharge(ctx context.Context, cardTxn storage.CardTransaction, customerSafeId string, locale *commoncfg.Locale, config *config.AtmFeesWaiverConfig) []digicardTxnAPI.AdditionalCharge {
	// only charge and then waive processing fee for Paynet ATM withdrawal
	// https://jira.grab.com/browse/DBMY-4757
	if constant.TransactionCategory(cardTxn.Category) != constant.TransactionCategoryATM ||
		cardTxn.FeeInfo == nil || cardTxn.FeeInfo.ProcessingFeeAmount <= 0 {
		return nil
	}

	feeInfo := cardTxn.FeeInfo
	chargingFee := digicardTxnAPI.AdditionalCharge{Amount: feeInfo.ProcessingFeeAmount, FeeChargeType: constant.FeeCharge}
	additionalCharges := []digicardTxnAPI.AdditionalCharge{chargingFee}

	//TCH-2122 waive fee only for first txn for the month
	if shouldWaiveAtmWithdrawalFee(ctx, customerSafeId, locale, config) {
		waivingFee := digicardTxnAPI.AdditionalCharge{Amount: feeInfo.ProcessingFeeAmount, FeeChargeType: constant.FeeWaive}
		additionalCharges = append(additionalCharges, waivingFee)
		slog.FromContext(ctx).Info(logAuth, fmt.Sprintf("first ATM withdrawal of the month, adding fee waiver. chargeType='%s', amount=%v", waivingFee.FeeChargeType, waivingFee.Amount))
	} else {
		slog.FromContext(ctx).Debug(logAuth, fmt.Sprintf("not the first ATM withdrawal of the month, skipping fee waiver."))
	}

	return additionalCharges
}

func shouldWaiveAtmWithdrawalFee(ctx context.Context, userId string, locale *commoncfg.Locale, config *config.AtmFeesWaiverConfig) bool {
	now := logic.Now()
	if locale != nil {
		if loc, err := time.LoadLocation(locale.Timezone); err == nil {
			now = now.In(loc)
		}
	}
	redisKey := constructAtmWithdrawalRedisKey(now, userId, logic.TimeComponent(config.TracksWithdrawalUntilNext))
	count, getErr := cache.RedisClient.GetInt64(ctx, redisKey)
	if getErr != nil {
		slog.FromContext(ctx).Warn(logAuth, fmt.Sprintf("unable to get ATM fee waiver Redis record, just waive it. redisKey='%s'", redisKey), slog.Error(getErr))
		return true
	}

	slog.FromContext(ctx).Debug(logAuth, fmt.Sprintf("successfully get ATM withdrawal count from Redis. redisKey='%s', count=%d", redisKey, count))
	return count == 0
}

func storeSuccessfulAtmWithdrawalForFeesWaiver(ctx context.Context, cardTransaction storage.CardTransaction, locale *commoncfg.Locale, config *config.AtmFeesWaiverConfig) error {
	if constant.TransactionCategory(cardTransaction.Category) != constant.TransactionCategoryATM {
		// not ATM transaction, skip
		return nil
	}

	userId, _, err := logic.FetchCustomerAndCardId(ctx, cardTransaction.ProxyNumber, logAuth)
	if err != nil {
		slog.FromContext(ctx).Warn(logAuth, "error fetching user ID for successful ATM withdrawal", slog.Error(err))
		return err
	}

	now := logic.Now()
	if locale != nil {
		if loc, err := time.LoadLocation(locale.Timezone); err == nil {
			now = now.In(loc)
		}
	}
	trackingTimeComponent := logic.TimeComponent(config.TracksWithdrawalUntilNext)
	redisKey := constructAtmWithdrawalRedisKey(now, userId, trackingTimeComponent)
	expiryTime := logic.GetNextNearestTime(now, trackingTimeComponent).Sub(now) //TODO we might need to add some buffers in case times are not synced between our pods and redis server?
	_, redisErr := cache.RedisClient.Set(ctx, redisKey, 1, expiryTime)
	if redisErr != nil {
		slog.FromContext(ctx).Warn(logAuth, fmt.Sprintf("error storing successful ATM withdrawal to Redis. redisKey='%s', expiryTime='%v'", redisKey, expiryTime), slog.Error(redisErr))
		return redisErr
	}

	slog.FromContext(ctx).Debug(logAuth, fmt.Sprintf("successful ATM withdrawal stored to Redis. redisKey='%s', expiryTime='%v'", redisKey, expiryTime))
	return nil
}

func constructAtmWithdrawalRedisKey(now time.Time, userId string, timeComponent logic.TimeComponent) string {
	// key format: CARD_ATM_<date/time>_<customer_id>
	timeLayout := "2006"
	switch timeComponent {
	case logic.Year:
	case logic.Month:
		timeLayout += "01"
	case logic.Day:
		timeLayout += "0102"
	case logic.Hour:
		timeLayout += "010215"
	case logic.Minute:
		timeLayout += "01021504"
	case logic.Second:
		timeLayout += "0102150405"
	}
	return fmt.Sprintf("CARD_ATM_%s_%s", now.Format(timeLayout), userId)
}
