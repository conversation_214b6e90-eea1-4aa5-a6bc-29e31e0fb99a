package authorizepaynet

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"

	"gitlab.myteksi.net/dakota/servus/v2"

	digicardTxnAPI "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/transaction"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
)

// ExecuteReversalSettledWorkflow ...
func (w *WorkflowImpl) ExecuteReversalSettledWorkflow(ctx context.Context, req *api.PaynetReversalSettledRequest, params *logic.Transaction) (*api.PaynetReversalSettledResponse, error) {
	txnInfo := req.TransactionReversalSettledRequest.TransactionInfo
	execData := &ExecutionData{
		PaynetReversalSettledRequest:     req,
		ReversalSettledTransactionParams: params,
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.IdempotencyKeyTag, txnInfo.RetrievalReferenceNumber),
		slog.CustomTag(logic.ProxyNumberTag, txnInfo.ProxyNumber))
	if err := preparePosting(ctx, txnInfo, execData, params.TransactionType == constant.TransactionTypeDirectCredit); err != nil {
		slog.FromContext(ctx).Error(logReversalSettled, fmt.Sprintf("Failed to prepare posting data for Reversal(%s)", string(params.TransactionType)), slog.Error(err))
		return nil, err
	}
	switch params.TransactionType {
	case constant.TransactionTypeDirectCredit:
		slog.FromContext(ctx).Info(logReversalSettled, fmt.Sprintf("Credit request detected: %s, starting direct credit workflow", params.TransactionCategory))
		return executeDirectCredit(ctx, execData)
	case constant.TransactionTypeDirectDebit:
		slog.FromContext(ctx).Info(logReversalSettled, fmt.Sprintf("Debit request detected: %s, starting direct debit workflow", params.TransactionCategory))
		return executeDirectDebit(ctx, execData, w)
	default:
		return nil, logic.ErrGenericServer
	}
}

// refund ...
//
//nolint:dupl
func (w *WorkflowImpl) refund(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}
	nextCtx := currCtx.Clone()
	refundReq, err := constructRefundRequest(ctx, nextCtx)
	if err != nil {
		slog.FromContext(ctx).Warn(logReversalSettled, "error constructing refund request", slog.Error(err))
		return nil, err
	}
	response, err := w.DigicardTxnClient.RefundTransaction(ctx, refundReq)
	if err != nil {
		slog.FromContext(ctx).Error(logReversalSettled, "DigicardTxnClient.RefundTransaction", slog.CustomTag("InternalResponse", response))
		return nil, err
	}
	switch response.Data.RefundStatus {
	case string(logic.Completed):
		nextCtx.State = stRefundCompleted
		nextCtx.Refund.ValuedAt = time.Now()
		nextCtx.Refund.Status = string(logic.Completed)
		w.Stats(ctx, string(logic.Completed), "")
		w.StatsD.Duration(logic.EuronetAdapter, refundLatencyTag, nextCtx.Refund.CreatedAt, fmt.Sprintf("status:%s", string(logic.Completed)))
	case string(logic.Processing):
		nextCtx.State = stRefundProcessing
	case string(logic.Failed):
		nextCtx.State = stFailed
		nextCtx.Refund.Status = string(logic.Failed)
		nextCtx.Refund.StatusReason = response.Data.StatusReason
		w.Stats(ctx, string(logic.Failed), response.Data.StatusReason)
	default:
		nextCtx.ResponseCode = logic.ErrSystemMalfunction.Code
		nextCtx.Description = logic.ErrSystemMalfunction.Message
		w.Stats(ctx, string(logic.Failed), "unknown error")
		return nil, errors.New("unknown status")
	}
	if response.Data.RefundStatus == string(logic.Completed) || response.Data.RefundStatus == string(logic.Failed) {
		nextCtx.ResponseCode = transaction.StateToStatus(nextCtx.State)
		if err = UpdateDBRefund(ctx, &currCtx.Refund, &nextCtx.Refund); err != nil {
			return nil, err
		}
	}

	return nextCtx, nil
}

// transferCancellation ...
//
//nolint:dupl
func (w *WorkflowImpl) transferCancellation(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}
	nextCtx := currCtx.Clone()
	refundReq, err := constructRefundRequest(ctx, nextCtx)
	if err != nil {
		slog.FromContext(ctx).Warn(logReversalSettled, "error constructing refund request", slog.Error(err))
		return nil, err
	}
	response, err := w.DigicardTxnClient.CancellationTransaction(ctx, refundReq)
	if err != nil {
		slog.FromContext(ctx).Error(logReversalSettled, "DigicardTxnClient.RefundTransaction", slog.CustomTag("InternalResponse", response))
		return nil, err
	}
	switch response.Data.RefundStatus {
	case string(logic.Completed):
		nextCtx.State = stCancellationCompleted
		nextCtx.Refund.ValuedAt = time.Now()
		nextCtx.Refund.Status = string(logic.Completed)
		w.Stats(ctx, string(logic.Completed), "")
		w.StatsD.Duration(logic.EuronetAdapter, refundLatencyTag, nextCtx.Refund.CreatedAt, fmt.Sprintf("status:%s", string(logic.Completed)))
	case string(logic.Processing):
		nextCtx.State = stCancellationProcessing
	case string(logic.Failed):
		nextCtx.State = stFailed
		nextCtx.Refund.Status = string(logic.Failed)
		nextCtx.Refund.StatusReason = response.Data.StatusReason
		w.Stats(ctx, string(logic.Failed), response.Data.StatusReason)
	default:
		nextCtx.ResponseCode = logic.ErrSystemMalfunction.Code
		nextCtx.Description = logic.ErrSystemMalfunction.Message
		w.Stats(ctx, string(logic.Failed), "unknown error")
		return nil, errors.New("unknown status")
	}
	if response.Data.RefundStatus == string(logic.Completed) || response.Data.RefundStatus == string(logic.Failed) {
		nextCtx.ResponseCode = transaction.StateToStatus(nextCtx.State)
		if err = UpdateDBRefund(ctx, &currCtx.Refund, &nextCtx.Refund); err != nil {
			return nil, err
		}
	}

	return nextCtx, nil
}

// UpdateDBRefund ...
func UpdateDBRefund(ctx context.Context, currRefund *storage.Refund, nextRefund *storage.Refund) error {
	nextRefund.UpdatedAt = time.Now()
	if err := storage.RefundD.UpdateEntity(ctx, currRefund, nextRefund); err != nil {
		slog.FromContext(ctx).Warn(logReversalSettled, "RefundD.UpdateEntity error", slog.Error(err))
		return err
	}
	slog.FromContext(ctx).Debug(logReversalSettled, fmt.Sprintf("successfully updated nextState status for %s from %s to %s", nextRefund.InternalTransactionID, currRefund.Status, nextRefund.Status))
	return nil
}

// persistRefundTransferStream ...
func (w *WorkflowImpl) persistRefundTransferStream(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}

	nextCtx := currCtx.Clone()

	streamMessage, ok2 := params.(*dto.StreamMessage)
	if !ok2 {
		return nil, errors.New("wrong params passed")
	}

	nextCtx.StreamMessage = streamMessage
	nextCtx.State = stRefundStreamPersisted

	return nextCtx, nil
}

// persistTransferCancellationStream ...
func (w *WorkflowImpl) persistTransferCancellationStream(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}

	nextCtx := currCtx.Clone()

	streamMessage, ok2 := params.(*dto.StreamMessage)
	if !ok2 {
		return nil, errors.New("wrong params passed")
	}

	nextCtx.StreamMessage = streamMessage
	nextCtx.State = stCancellationStreamPersisted

	return nextCtx, nil
}

// completeRefund ...
//
//nolint:dupl
func (w *WorkflowImpl) completeRefund(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}

	nextCtx := currCtx.Clone()

	streamMessage := currCtx.StreamMessage

	switch streamMessage.Status {
	case string(logic.Success):
		nextCtx.State = stRefundCompleted
		nextCtx.Refund.ValuedAt = time.Now()
		nextCtx.Refund.Status = string(logic.Completed)
		w.Stats(ctx, string(logic.Completed), "")
		w.StatsD.Duration(logic.EuronetAdapter, refundLatencyTag, nextCtx.Refund.CreatedAt, fmt.Sprintf("status:%s", string(logic.Completed)))
	case string(logic.Failed):
		nextCtx.State = stFailed
		nextCtx.Refund.Status = string(logic.Failed)
		nextCtx.Refund.StatusReason = streamMessage.ErrorCode
		w.Stats(ctx, string(logic.Failed), streamMessage.ErrorCode)
	}
	nextCtx.Refund.Status = transaction.StateToStatus(nextCtx.State)
	if err := UpdateDBRefund(ctx, &currCtx.Refund, &nextCtx.Refund); err != nil {
		return nil, err
	}
	return nextCtx, nil
}

// completeCancellation ...
//
//nolint:dupl
func (w *WorkflowImpl) completeCancellation(ctx context.Context, transitionID string, execData workflowengine.ExecutionData, params interface{}) (workflowengine.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		return nil, errors.New("wrong context passed")
	}

	nextCtx := currCtx.Clone()

	streamMessage := currCtx.StreamMessage

	switch streamMessage.Status {
	case string(logic.Success):
		nextCtx.State = stCancellationCompleted
		nextCtx.Refund.ValuedAt = time.Now()
		nextCtx.Refund.Status = string(logic.Completed)
		w.Stats(ctx, string(logic.Completed), "")
		w.StatsD.Duration(logic.EuronetAdapter, refundLatencyTag, nextCtx.Refund.CreatedAt, fmt.Sprintf("status:%s", string(logic.Completed)))
	case string(logic.Failed):
		nextCtx.State = stFailed
		nextCtx.Refund.Status = string(logic.Failed)
		nextCtx.Refund.StatusReason = streamMessage.ErrorCode
		w.Stats(ctx, string(logic.Failed), streamMessage.ErrorCode)
	}
	nextCtx.Refund.Status = transaction.StateToStatus(nextCtx.State)
	if err := UpdateDBRefund(ctx, &currCtx.Refund, &nextCtx.Refund); err != nil {
		return nil, err
	}
	return nextCtx, nil
}

// constructRefundRequest ...
func constructRefundRequest(ctx context.Context, execData *ExecutionData) (*digicardTxnAPI.RefundRequest, error) {
	internalCardID, err := logic.FetchInternalCardID(ctx, execData.Refund.ProxyNumber, logReversalSettled)
	if err != nil {
		if errors.Is(err, data.ErrNoData) {
			return nil, logic.ErrProxyNumberNotFound
		}
		return nil, err
	}
	txnParams := execData.ReversalSettledTransactionParams
	refundTxn := execData.Refund
	txnInfo := execData.PaynetReversalSettledRequest.TransactionReversalSettledRequest.TransactionInfo
	idempotencyKey := CreateWorkflowRunID(
		txnInfo.RetrievalReferenceNumber,
		txnInfo.NetworkID,
		txnInfo.ProxyNumber,
	)
	if txnInfo.RetrievalReferenceNumber == txnInfo.OrigTransactionID && common.ContainStringValue([]string{logic.ReversalMTI, logic.ReversalRepeatMTI}, txnInfo.Mti) {
		// Reversal txn detected, start new workflow
		idempotencyKey += reversalTxnSuffix
	}
	return &digicardTxnAPI.RefundRequest{
		CardID:         internalCardID,
		ChargeID:       execData.Refund.OriginalInternalTransactionID,
		RefundAmount:   execData.Refund.RefundAmount,
		IdempotencyKey: idempotencyKey, // it's used for workflow.ConsumeFromRefundStream
		Metadata: &digicardTxnAPI.MetaData{
			AcquirerReferenceData:  refundTxn.AcquirerReferenceData,
			TransactionCategory:    string(txnParams.TransactionCategory),
			TransactionType:        string(txnParams.TransactionType),
			TransactionSubCategory: string(txnParams.TransactionSubCategory),
			AcceptorCountryCode:    refundTxn.AcceptorCountryCode,
			CardProxyNumber:        refundTxn.ProxyNumber,
			OriginalAmount:         refundTxn.OriginalAmount,
			OriginalCurrency:       refundTxn.OriginalCurrency,
			McConversionRate:       execData.CardTransaction.ConversionRate,
			RetailerID:             refundTxn.RetailerID,
			AuthorizationID:        txnInfo.AuthorizationID,
		},
	}, nil
}

// fetchOriginalTransactionRefund ...
func fetchOriginalTransactionRefund(ctx context.Context, criteria *TransactionInfoCriteria) (*storage.Refund, *logic.AdapterError) {
	query := []data.Condition{
		data.EqualTo("RetrievalReferenceNumber", criteria.OriginalTxID),
		data.EqualTo("ProxyNumber", criteria.ProxyNumber),
	}
	transactions, err := storage.RefundD.Find(ctx, query...)
	if err != nil || len(transactions) == 0 {
		slog.FromContext(ctx).Warn(logCancel, "error in finding original transaction ", slog.Error(err))
		return nil, &logic.ErrInvalidTransaction
	}
	return transactions[0], nil
}

// preparePosting ...
func preparePosting(ctx context.Context, txnInfo *api.PaynetReversalSettledRequest_ReversalSettledReq, execData *ExecutionData, isCredit bool) error {
	// Adjustment credit request detected, start new workflow
	if logic.IsAdjustmentTxn(txnInfo.Mti, txnInfo.ProcessingCode) {
		return nil
	}
	txInfoCriteria := &TransactionInfoCriteria{
		OriginalTxID: txnInfo.OrigTransactionID,
		ProxyNumber:  txnInfo.ProxyNumber,
	}
	var requestCurrency string
	// find original txn in table card_transaction
	if isCredit {
		slog.FromContext(ctx).Info(logReversalSettled, fmt.Sprintf("Fetching original card txn with original rrn: %s", txnInfo.OrigTransactionID))
		authTransaction, err := fetchOriginalTransaction(ctx, txInfoCriteria)
		if err != nil {
			slog.FromContext(ctx).Warn(logReversalSettled, "cannot find original transaction")
			return toServiceError(err)
		}
		execData.CardTransaction = *authTransaction
		requestCurrency = authTransaction.RequestCurrency
		if txnInfo.CardHolderCurrencyCode == requestCurrency &&
			!isValidRefundAmount(txnInfo, execData.CardTransaction) {
			slog.FromContext(ctx).Warn(logReversalSettled, "request refund amount and original txn amount are mismatched")
			return logic.ErrGenericServer
		}
		if authTransaction.Status != string(constant.TxnCompleted) {
			slog.FromContext(ctx).Error(logReversalSettled, fmt.Sprintf("Charge transaction with [proxyNo=%s rrn=%s] is not COMPLETED", txnInfo.ProxyNumber, txnInfo.RetrievalReferenceNumber))
			return logic.ErrGenericServer
		}
	} else { // find original txn in table refund
		slog.FromContext(ctx).Info(logReversalSettled, fmt.Sprintf("Fetching original refund txn with original rrn: %s", txnInfo.OrigTransactionID))
		refundTxn, err := fetchOriginalTransactionRefund(ctx, txInfoCriteria)
		if err != nil {
			slog.FromContext(ctx).Warn(logReversalSettled, "cannot find original transaction")
			return toServiceError(err)
		}
		execData.Refund = *refundTxn
		requestCurrency = refundTxn.RequestCurrency
		if txnInfo.CardHolderCurrencyCode == requestCurrency &&
			!isValidRefundReversal(txnInfo, execData.Refund) {
			slog.FromContext(ctx).Warn(logReversalSettled, "request debit amount and original credit amount are mismatched")
			return logic.ErrGenericServer
		}
		if refundTxn.Status != string(constant.TxnCompleted) {
			slog.FromContext(ctx).Error(logReversalSettled, fmt.Sprintf("Refund transaction with [proxyNo=%s rrn=%s] is not COMPLETED", txnInfo.ProxyNumber, txnInfo.RetrievalReferenceNumber))
			return logic.ErrGenericServer
		}
	}
	return nil
}

func toServiceError(err *logic.AdapterError) *servus.ServiceError {
	return &servus.ServiceError{
		HTTPCode: http.StatusBadRequest,
		Code:     err.Code,
		Message:  err.Message,
	}
}

func executeDirectCredit(ctx context.Context, execData *ExecutionData) (*api.PaynetReversalSettledResponse, error) {
	txnInfo := execData.PaynetReversalSettledRequest.TransactionReversalSettledRequest.TransactionInfo
	workflowRunID := CreateWorkflowRunID(
		txnInfo.RetrievalReferenceNumber,
		txnInfo.NetworkID,
		txnInfo.ProxyNumber,
	)
	if txnInfo.RetrievalReferenceNumber == txnInfo.OrigTransactionID && common.ContainStringValue([]string{logic.ReversalMTI, logic.ReversalRepeatMTI}, txnInfo.Mti) {
		// Reversal txn detected, start new workflow
		workflowRunID += reversalTxnSuffix
	}
	resp := handleReversalSettledRepeat(ctx, workflowRunID, execData)
	if resp != nil {
		return resp, nil
	}
	if err := initWorkflow(ctx, workflowRunID, execData); err != nil {
		return nil, err
	}
	event := evPersistRefundRequest
	if txnInfo.Mti == logic.AuthMTI { // cancel request
		event = evPersistCancellationRequest
	}
	refundResult, err := wfExecute(ctx, workflowengine.Execution{
		WorkflowID:     workflowID,
		RequestID:      workflowRunID,
		ExecutionEvent: event,
	}, nil)
	if err != nil {
		slog.FromContext(ctx).Warn(logReversalSettled, "error when running workflow", slog.Error(err))
		return nil, err
	}
	return toPaynetReversalSettledResponse(refundResult.(*ExecutionData)), nil
}

func executeDirectDebit(ctx context.Context, execData *ExecutionData, w *WorkflowImpl) (*api.PaynetReversalSettledResponse, error) {
	txnInfo := execData.PaynetReversalSettledRequest.TransactionReversalSettledRequest.TransactionInfo
	workflowRunID := CreateWorkflowRunID(
		txnInfo.RetrievalReferenceNumber,
		txnInfo.NetworkID,
		txnInfo.ProxyNumber,
	)
	if txnInfo.RetrievalReferenceNumber == txnInfo.OrigTransactionID && common.ContainStringValue([]string{logic.ReversalMTI, logic.ReversalRepeatMTI}, txnInfo.Mti) {
		// Reversal txn detected, start new workflow
		workflowRunID += reversalTxnSuffix
	}
	resp := handleReversalSettledRepeat(ctx, workflowRunID, execData)
	if resp != nil {
		return resp, nil
	}
	execData.TransactionAuthorizeRequest = toAuthRequest(execData.PaynetReversalSettledRequest)
	if err := initWorkflow(ctx, workflowRunID, execData); err != nil {
		return nil, err
	}
	executedData, err := wfExecute(ctx, workflowengine.Execution{
		WorkflowID:     workflowID,
		RequestID:      workflowRunID,
		ExecutionEvent: evPersistRequest,
	}, execData.ReversalSettledTransactionParams)
	if err != nil {
		slog.FromContext(ctx).Warn(logReversalSettled, "error when running workflow", slog.Error(err))
		return nil, logic.ErrGenericServer
	}
	if err != nil {
		slog.FromContext(ctx).Warn(logReversalSettled, "error when running workflow", slog.Error(err))
		return nil, err
	}
	return toPaynetReversalSettledResp(w.convertResponse(executedData.(*ExecutionData))), nil
}

// validate reversal amount for cancellation txn
func isValidRefundReversal(txnInfo *api.PaynetReversalSettledRequest_ReversalSettledReq, refundTxn storage.Refund) bool {
	return refundTxn.RequestAmount == txnInfo.TransactionAmount
}

func isValidRefundAmount(txnInfo *api.PaynetReversalSettledRequest_ReversalSettledReq, authTxn storage.CardTransaction) bool {
	txnAmt := txnInfo.TransactionAmount
	txnFeeAmt := txnInfo.TransactionFeeAmount
	processFeeAmt := txnInfo.ProcessingFeeAmount
	authAmt := authTxn.RequestAmount
	authFeeInfo := authTxn.FeeInfo

	if txnAmt > 0 && txnAmt != authAmt {
		return false
	}

	if txnFeeAmt > 0 {
		if authFeeInfo != nil && txnFeeAmt != authFeeInfo.TransactionFeeAmount {
			return false
		}
	}

	if processFeeAmt > 0 {
		if authFeeInfo != nil && processFeeAmt != authFeeInfo.ProcessingFeeAmount {
			return false
		}
	}
	return true
}

func handleReversalSettledRepeat(ctx context.Context, workflowRunID string, execData *ExecutionData) *api.PaynetReversalSettledResponse {
	txnInfo := execData.PaynetReversalSettledRequest.TransactionReversalSettledRequest.TransactionInfo
	params := execData.ReversalSettledTransactionParams
	if txnInfo.Mti == logic.ReversalRepeatMTI {
		wfData, wfGetErr := wfGet(ctx, workflowengine.Execution{
			WorkflowID: workflowID,
			RequestID:  workflowRunID,
		})
		if wfGetErr != nil {
			slog.FromContext(ctx).Info(logReversalSettled, fmt.Sprintf("Workflow with run_id=%s does not exist", workflowRunID))
			return nil
		}
		if IsFinalState(wfData.GetState()) {
			slog.FromContext(ctx).Info(logReversalSettled, fmt.Sprintf("Reversal(%s) repeat detected, return latest transaction status", string(params.TransactionType)))
			return toPaynetReversalSettledResponse(wfData.(*ExecutionData))
		}
	}
	return nil
}

//nolint:interfacer
func initWorkflow(ctx context.Context, workflowRunID string, execData *ExecutionData) error {
	if err := wfInit(ctx, workflowengine.Execution{
		WorkflowID: workflowID,
		RequestID:  workflowRunID,
	}, execData); err != nil {
		if errors.Is(err, workflowengine.ErrExecutionAlreadyExists) {
			slog.FromContext(ctx).Warn(logReversalSettled, fmt.Sprintf("workflow %s existed", workflowRunID))
			return err
		}
		slog.FromContext(ctx).Error(logReversalSettled, "failed to initiating workflow", slog.Error(err))
	}
	return nil
}
