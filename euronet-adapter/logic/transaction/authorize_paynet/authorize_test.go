package authorizepaynet

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func TestExecuteAuthorizeTransactionWorkflow(t *testing.T) {
	errDummy := errors.New("simulate error")
	testRequestID := uuid.NewString()
	rrn := "123456789012"
	defaultAuthorizeRequest := &api.PaynetAuthorizeRequest_TransactionAuthorizeRequest{
		AppHeader: &api.AppHeader{
			ReqID: testRequestID,
		},
		TransactionInfo: &api.PaynetAuthorizeRequest_AuthorizeReq{
			RetrievalReferenceNumber: rrn,
		},
	}
	scenarios := []struct {
		authorizeReq *api.PaynetAuthorizeRequest_TransactionAuthorizeRequest
		responseCode string
		errInit      error
		errGet       error
		errExecute   error
		expectedErr  error
	}{
		{
			authorizeReq: defaultAuthorizeRequest,
			errInit:      errDummy,
			expectedErr:  logic.ErrGenericServer,
		},
		{
			authorizeReq: defaultAuthorizeRequest,
			errInit:      workflowengine.ErrExecutionAlreadyExists,
			errGet:       errDummy,
			expectedErr:  logic.ErrGenericServer,
		},
		{
			authorizeReq: defaultAuthorizeRequest,
			errExecute:   errDummy,
			expectedErr:  logic.ErrGenericServer,
		},
		{
			authorizeReq: defaultAuthorizeRequest,
			responseCode: "0000",
		},
	}

	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario
		ctx := context.Background()
		wfInit = func(ctx context.Context, e workflowengine.Execution, executionData workflowengine.ExecutionData) error {
			return testcase.errInit
		}
		wfGet = func(ctx context.Context, e workflowengine.Execution) (workflowengine.ExecutionData, error) {
			return &ExecutionData{
				State: stAuthorized,
			}, testcase.errGet
		}
		wfExecute = func(ctx context.Context, e workflowengine.Execution, params interface{}) (workflowengine.ExecutionData, error) {
			return &ExecutionData{
				State:        stAuthorized,
				ResponseCode: testcase.responseCode,
			}, testcase.errExecute
		}

		w := WorkflowImpl{}
		transactionParams := &logic.Transaction{}
		resp, err := w.ExecuteAuthorizeTransactionWorkflow(ctx, testcase.authorizeReq, transactionParams)
		if testcase.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, testcase.expectedErr, err, description)
			assert.Nil(t, resp, description)
		} else {
			assert.NoError(t, err, description)
			assert.Equal(t, testcase.responseCode, resp.Response.ResponseCode)
			assert.NotNil(t, resp, description)
		}
	}
}
