// Code generated by mockery v2.13.1. DO NOT EDIT.

package refund

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	dto "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
)

// MockWorkflow is an autogenerated mock type for the Workflow type
type MockWorkflow struct {
	mock.Mock
}

// ExecuteRefundWorkflow provides a mock function with given fields: ctx, req
func (_m *MockWorkflow) ExecuteRefundWorkflow(ctx context.Context, req *dto.CreateRefundRequest) (*dto.CreateRefundResponse, error) {
	ret := _m.Called(ctx, req)

	var r0 *dto.CreateRefundResponse
	if rf, ok := ret.Get(0).(func(context.Context, *dto.CreateRefundRequest) *dto.CreateRefundResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*dto.CreateRefundResponse)
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(context.Context, *dto.CreateRefundRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewMockWorkflow interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockWorkflow creates a new instance of MockWorkflow. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockWorkflow(t mockConstructorTestingTNewMockWorkflow) *MockWorkflow {
	mock := &MockWorkflow{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
