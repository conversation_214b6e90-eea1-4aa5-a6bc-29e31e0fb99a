package refund

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	digicardTxnAPI "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	digicardTxnMock "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func TestTransfer(t *testing.T) {
	dummyErr := errors.New("some error")
	mockDigicardTxn := &digicardTxnMock.DigicardTransaction{}
	userCardMapping := []*storage.UserCardMapping{
		{
			ID:                 123,
			InternalCustomerID: uuid.NewString(),
			EuronetCustomerID:  "123456789",
			CardProxyNumber:    "123456789",
			CardSequenceNumber: 1,
			InternalCardID:     uuid.NewString(),
			ExpiryDate:         "",
			CreatedAt:          time.Time{},
			UpdatedAt:          time.Time{},
		},
	}
	wfObj := &WorkflowImpl{
		StatsD:            statsd.NewNoop(),
		DigicardTxnClient: mockDigicardTxn,
	}
	scenarios := []struct {
		desc          string
		expectedErr   error
		expectedState workflowengine.State
		mockFunc      func()
	}{
		{
			desc:          "happy path - completed",
			expectedErr:   nil,
			expectedState: stRefundCompleted,
			mockFunc: func() {
				mockStorageDAO := &storage.MockIRefundDAO{}
				mockStorageCardMapping := &storage.MockIUserCardMappingDAO{}
				storage.RefundD = mockStorageDAO
				storage.UserCardMappingD = mockStorageCardMapping
				mockDigicardTxn.On("RefundTransaction", mock.Anything, mock.Anything).Return(&digicardTxnAPI.RefundResponse{Data: &digicardTxnAPI.RefundResource{
					RefundID:                "123",
					RefundStatus:            string(logic.Completed),
					RefundAmount:            10,
					StatusReason:            "reason",
					StatusReasonDescription: "reason",
					CreationTimestamp:       time.Time{},
					RefundTimestamp:         time.Time{},
				}}, nil).Once()
				mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockStorageCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(userCardMapping, nil).Once()
			},
		},
		{
			desc:        "proxy number not found",
			expectedErr: logic.ErrProxyNumberNotFound,
			mockFunc: func() {
				mockStorageCardMapping := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorageCardMapping
				mockStorageCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
			},
		},
		{
			desc:        "find user card mapping DB error",
			expectedErr: dummyErr,
			mockFunc: func() {
				mockStorageCardMapping := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorageCardMapping
				mockStorageCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, dummyErr).Once()
			},
		},
		{
			desc:          "happy path - processing",
			expectedErr:   nil,
			expectedState: stRefundProcessing,
			mockFunc: func() {
				mockStorageDAO := &storage.MockIRefundDAO{}
				mockStorageCardMapping := &storage.MockIUserCardMappingDAO{}
				storage.RefundD = mockStorageDAO
				storage.UserCardMappingD = mockStorageCardMapping
				mockDigicardTxn.On("RefundTransaction", mock.Anything, mock.Anything).Return(&digicardTxnAPI.RefundResponse{Data: &digicardTxnAPI.RefundResource{
					RefundID:                "123",
					RefundStatus:            string(logic.Processing),
					RefundAmount:            10,
					StatusReason:            "reason",
					StatusReasonDescription: "reason",
					CreationTimestamp:       time.Time{},
					RefundTimestamp:         time.Time{},
				}}, nil).Once()
				mockStorageCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(userCardMapping, nil).Once()
			},
		},
		{
			desc:        "digicard txn failed",
			expectedErr: dummyErr,
			mockFunc: func() {
				mockStorageCardMapping := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorageCardMapping
				mockDigicardTxn.On("RefundTransaction", mock.Anything, mock.Anything).Return(nil, dummyErr).Once()
				mockStorageCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(userCardMapping, nil).Once()
			},
		},
		{
			desc:          "dc-txn return failed",
			expectedErr:   nil,
			expectedState: stFailed,
			mockFunc: func() {
				mockStorageDAO := &storage.MockIRefundDAO{}
				mockStorageCardMapping := &storage.MockIUserCardMappingDAO{}
				storage.RefundD = mockStorageDAO
				storage.UserCardMappingD = mockStorageCardMapping
				mockDigicardTxn.On("RefundTransaction", mock.Anything, mock.Anything).Return(&digicardTxnAPI.RefundResponse{Data: &digicardTxnAPI.RefundResource{
					RefundID:                "123",
					RefundStatus:            string(logic.Failed),
					RefundAmount:            10,
					StatusReason:            "reason",
					StatusReasonDescription: "reason",
					CreationTimestamp:       time.Time{},
					RefundTimestamp:         time.Time{},
				}}, nil).Once()
				mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockStorageCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(userCardMapping, nil).Once()
			},
		},
		{
			desc:          "unprocessable status",
			expectedErr:   errors.New("unknown status"),
			expectedState: stRequestPersisted,
			mockFunc: func() {
				mockStorageCardMapping := &storage.MockIUserCardMappingDAO{}
				storage.UserCardMappingD = mockStorageCardMapping
				mockDigicardTxn.On("RefundTransaction", mock.Anything, mock.Anything).Return(&digicardTxnAPI.RefundResponse{Data: &digicardTxnAPI.RefundResource{
					RefundID:                "123",
					RefundStatus:            "random_status",
					RefundAmount:            10,
					StatusReason:            "reason",
					StatusReasonDescription: "reason",
					CreationTimestamp:       time.Time{},
					RefundTimestamp:         time.Time{},
				}}, nil).Once()
				mockStorageCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(userCardMapping, nil).Once()
			},
		},
		{
			desc:          "refund db update failed",
			expectedErr:   dummyErr,
			expectedState: stFailed,
			mockFunc: func() {
				mockStorageDAO := &storage.MockIRefundDAO{}
				mockStorageCardMapping := &storage.MockIUserCardMappingDAO{}
				storage.RefundD = mockStorageDAO
				storage.UserCardMappingD = mockStorageCardMapping
				mockDigicardTxn.On("RefundTransaction", mock.Anything, mock.Anything).Return(&digicardTxnAPI.RefundResponse{Data: &digicardTxnAPI.RefundResource{
					RefundID:                "123",
					RefundStatus:            string(logic.Failed),
					RefundAmount:            10,
					StatusReason:            "reason",
					StatusReasonDescription: "reason",
					CreationTimestamp:       time.Time{},
					RefundTimestamp:         time.Time{},
				}}, nil).Once()
				mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(dummyErr).Once()
				mockStorageCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(userCardMapping, nil).Once()
			},
		},
	}

	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			scenario.mockFunc()
			nextCtx, err := wfObj.transfer(context.Background(), "", &ExecutionData{
				CreateRefundRequest: &dto.CreateRefundRequest{
					IdempotencyKey:   uuid.NewString(),
					AuthTxnID:        uuid.NewString(),
					ProxyNumber:      uuid.NewString(),
					AuthAmount:       10,
					AuthCurrency:     "SGD",
					OriginalAmount:   0,
					OriginalCurrency: "SGD",
					RefundAmount:     0,
					RRN:              uuid.NewString(),
					CardSchemeTxnID:  uuid.NewString(),
				},
				Refund: storage.Refund{
					ID:                            1234,
					OriginalInternalTransactionID: uuid.NewString(),
					ProxyNumber:                   uuid.NewString(),
					RequestAmount:                 10,
					RequestCurrency:               "SGD",
					OriginalAmount:                10,
					OriginalCurrency:              "SGD",
					RefundAmount:                  10,
					RetrievalReferenceNumber:      uuid.NewString(),
					CardSchemeTransactionID:       uuid.NewString(),
					InternalTransactionID:         uuid.NewString(),
					Status:                        string(logic.Processing),
					StatusReason:                  "",
					CreatedAt:                     time.Time{},
					UpdatedAt:                     time.Time{},
					ValuedAt:                      time.Time{},
				},
			}, nil)
			if scenario.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, scenario.expectedErr, err)
				assert.Nil(t, nextCtx)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, nextCtx)
				assert.Equal(t, scenario.expectedState, nextCtx.GetState())
			}
		})
	}
}

func TestPersistTransferStream(t *testing.T) {
	w := WorkflowImpl{}

	executionData := &ExecutionData{
		Refund:        storage.Refund{},
		StreamMessage: &dto.StreamMessage{},
	}

	streamMessage := &dto.StreamMessage{
		ReferenceID:  "1",
		TxID:         "123456789",
		Status:       string(logic.Success),
		ErrorCode:    "the_error_code",
		ErrorMessage: "the_error_message",
	}

	updatedCtx, err := w.persistTransferStream(context.Background(), uuid.NewString(), executionData, streamMessage)
	assert.NoError(t, err)
	assert.Equal(t, stRefundStreamPersisted, updatedCtx.GetState())
	updatedExecutionData, ok := updatedCtx.(*ExecutionData)
	assert.True(t, ok)
	assert.Equal(t, streamMessage, updatedExecutionData.StreamMessage)
}

func TestCompleteTransfer(t *testing.T) {
	mockStorageDAO := &storage.MockIRefundDAO{}
	storage.RefundD = mockStorageDAO

	w := WorkflowImpl{
		StatsD: statsd.NewNoop(),
	}

	scenarios := []struct {
		desc          string
		expectedErr   error
		expectedState workflowengine.State
		execData      *ExecutionData
		mockFunc      func()
	}{
		{
			desc:          "refund successful",
			expectedErr:   nil,
			expectedState: stRefundCompleted,
			execData: &ExecutionData{
				State: stRefundProcessing,
				Refund: storage.Refund{
					Status: string(logic.Processing),
				},
				CreateRefundRequest: &dto.CreateRefundRequest{},
				StreamMessage: &dto.StreamMessage{
					ReferenceID:  "1",
					TxID:         "2",
					Status:       string(logic.Success),
					ErrorCode:    "the_error_code",
					ErrorMessage: "the_error_message",
				},
			},
			mockFunc: func() {
				mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
			},
		},
		{
			desc:          "refund failed",
			expectedErr:   nil,
			expectedState: stFailed,
			execData: &ExecutionData{
				State: stRefundProcessing,
				Refund: storage.Refund{
					Status: string(logic.Processing),
				},
				CreateRefundRequest: &dto.CreateRefundRequest{},
				StreamMessage: &dto.StreamMessage{
					ReferenceID:  "1",
					TxID:         "2",
					Status:       string(logic.Failed),
					ErrorCode:    "the_error_code",
					ErrorMessage: "the_error_message",
				},
			},
			mockFunc: func() {
				mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
			},
		},
	}
	for _, scenario := range scenarios {
		testcase := scenario
		description := testcase.desc
		testcase.mockFunc()
		resp, err := w.completeTransfer(context.Background(), "", testcase.execData, nil)
		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, testcase.expectedErr, err, description)
			assert.Nil(t, resp, description)
		} else {
			assert.NoError(t, err, description)
			assert.NotNil(t, resp, description)
			assert.Equal(t, testcase.expectedState, resp.GetState(), description)
		}
	}
}

func TestTransferEnrichData(t *testing.T) {
	mockDigicardTxn := &digicardTxnMock.DigicardTransaction{}
	userCardMapping := []*storage.UserCardMapping{
		{
			ID:                 123,
			InternalCustomerID: uuid.NewString(),
			EuronetCustomerID:  "123456789",
			CardProxyNumber:    "123456789",
			CardSequenceNumber: 1,
			InternalCardID:     uuid.NewString(),
			ExpiryDate:         "",
			CreatedAt:          time.Time{},
			UpdatedAt:          time.Time{},
		},
	}
	wfObj := &WorkflowImpl{
		StatsD:            statsd.NewNoop(),
		DigicardTxnClient: mockDigicardTxn,
	}
	scenarios := []struct {
		desc     string
		input    string
		output   string
		mockFunc func()
	}{
		{
			desc:   "Currency Numeric Code Input",
			input:  "458",
			output: "MYR",
			mockFunc: func() {
				mockStorageDAO := &storage.MockIRefundDAO{}
				mockStorageCardMapping := &storage.MockIUserCardMappingDAO{}
				storage.RefundD = mockStorageDAO
				storage.UserCardMappingD = mockStorageCardMapping
				mockDigicardTxn.On("RefundTransaction", mock.Anything, mock.Anything).Return(&digicardTxnAPI.RefundResponse{Data: &digicardTxnAPI.RefundResource{
					RefundID:                "123",
					RefundStatus:            string(logic.Completed),
					RefundAmount:            10,
					StatusReason:            "reason",
					StatusReasonDescription: "reason",
					CreationTimestamp:       time.Time{},
					RefundTimestamp:         time.Time{},
				}}, nil).Once()
				mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockStorageCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(userCardMapping, nil).Once()
			},
		},
		{
			desc:   "Currency Code Input",
			input:  "MYR",
			output: "MYR",
			mockFunc: func() {
				mockStorageDAO := &storage.MockIRefundDAO{}
				mockStorageCardMapping := &storage.MockIUserCardMappingDAO{}
				storage.RefundD = mockStorageDAO
				storage.UserCardMappingD = mockStorageCardMapping
				mockDigicardTxn.On("RefundTransaction", mock.Anything, mock.Anything).Return(&digicardTxnAPI.RefundResponse{Data: &digicardTxnAPI.RefundResource{
					RefundID:                "123",
					RefundStatus:            string(logic.Completed),
					RefundAmount:            10,
					StatusReason:            "reason",
					StatusReasonDescription: "reason",
					CreationTimestamp:       time.Time{},
					RefundTimestamp:         time.Time{},
				}}, nil).Once()
				mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockStorageCardMapping.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(userCardMapping, nil).Once()
			},
		},
	}

	for _, _scenario := range scenarios {
		scenario := _scenario
		t.Run(scenario.desc, func(t *testing.T) {
			scenario.mockFunc()
			nextCtx, _ := wfObj.transfer(context.Background(), "", &ExecutionData{
				CreateRefundRequest: &dto.CreateRefundRequest{
					IdempotencyKey:   uuid.NewString(),
					AuthTxnID:        uuid.NewString(),
					ProxyNumber:      uuid.NewString(),
					AuthAmount:       10,
					AuthCurrency:     "SGD",
					OriginalAmount:   0,
					OriginalCurrency: scenario.input,
					RefundAmount:     0,
					RRN:              uuid.NewString(),
					CardSchemeTxnID:  uuid.NewString(),
				},
				Refund: storage.Refund{
					ID:                            1234,
					OriginalInternalTransactionID: uuid.NewString(),
					ProxyNumber:                   uuid.NewString(),
					RequestAmount:                 10,
					RequestCurrency:               "SGD",
					OriginalAmount:                10,
					OriginalCurrency:              "SGD",
					RefundAmount:                  10,
					RetrievalReferenceNumber:      uuid.NewString(),
					CardSchemeTransactionID:       uuid.NewString(),
					InternalTransactionID:         uuid.NewString(),
					Status:                        string(logic.Processing),
					StatusReason:                  "",
					CreatedAt:                     time.Time{},
					UpdatedAt:                     time.Time{},
					ValuedAt:                      time.Time{},
				},
			}, nil)
			updatedExecutionData, _ := nextCtx.(*ExecutionData)
			assert.NotNil(t, nextCtx)
			assert.Equal(t, scenario.output, updatedExecutionData.CreateRefundRequest.MetaData.OriginalCurrency)
		})
	}
}
