// Code generated by mockery v2.13.1. DO NOT EDIT.

package file

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	dto "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
)

// MockWorkflow is an autogenerated mock type for the Workflow type
type MockWorkflow struct {
	mock.Mock
}

// ExecuteFileWorkflow provides a mock function with given fields: ctx, req
func (_m *MockWorkflow) ExecuteFileWorkflow(ctx context.Context, req *dto.FileRequest) error {
	ret := _m.Called(ctx, req)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *dto.FileRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewMockWorkflow interface {
	mock.TestingT
	Cleanup(func())
}

// NewMockWorkflow creates a new instance of MockWorkflow. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewMockWorkflow(t mockConstructorTestingTNewMockWorkflow) *MockWorkflow {
	mock := &MockWorkflow{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
