package processing

import (
	"context"
	"encoding/csv"
	"fmt"
	processUtil "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/process"
	"io"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/file"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"golang.org/x/sync/semaphore"
)

type clearingStage string

const (
	// PartialCaptureClearing ...
	PartialCaptureClearing = "PARTIAL"
	// NonRefundFinalCaptureClearing ...
	NonRefundFinalCaptureClearing clearingStage = "NON_REFUND_FINAL"
	// RefundClearing ...
	RefundClearing clearingStage = "REFUND"
)

var (
	once                         sync.Once
	sem                          *semaphore.Weighted
	processTxnDetailsInGoRoutine = _processTxnDetailsInGoRoutine
)

func (w *WorkflowImpl) processClearingFileV2(ctx context.Context, reader io.ReadSeeker, fileName string) fileErrors {
	// To skip current clearing process and proceed with new process when threshold is at 100%
	if w.QueueHandlerClearingConfig.AllowedProxyNumberThreshold >= 100 {
		slog.FromContext(ctx).Info(logTag, fmt.Sprintf("Skipping processClearingFileV2 and proceed with new process"))
		return nil
	}

	once.Do(func() {
		sem = semaphore.NewWeighted(w.AdminConfig.ClearingConcurrency)
	})

	//https://gxsbank.atlassian.net/wiki/spaces/Digibank/pages/5178423/TT112+Clearing+File+Specification+from+Euronet
	var lineNumber, countFromFooter, successfulProcessCount int
	var successCountSafe safeCounter
	fileErr := fileErrors(make(map[string]string))
	clearingStages := []clearingStage{PartialCaptureClearing, NonRefundFinalCaptureClearing, RefundClearing}

	for stageIdx, stage := range clearingStages {
		slog.FromContext(ctx).Info(logTag, fmt.Sprintf("start clearing stage [%s]", stage))
		lineNumber = 0
		if _, err := reader.Seek(0, 0); err != nil {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("error resetting position at stage %s", stage), slog.Error(err))
			return fileErr
		}
		csvReader := csv.NewReader(reader)
		csvReader.FieldsPerRecord = -1 // file is not having constant number of columns
	forLoop:
		for {
			lineNumber++
			record, err := csvReader.Read()
			if err != nil {
				if err == io.EOF {
					slog.FromContext(ctx).Info(logTag, fmt.Sprintf("reached the end of the file at line %d", lineNumber), slog.Error(err))
					break
				}
				slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("error reading line %d from csv file", lineNumber), slog.Error(err))
				fileErr[strconv.Itoa(lineNumber)] = err.Error()
				continue
			}
			switch record[0] {
			case file.Header:
				slog.FromContext(ctx).Info(logTag, fmt.Sprintf("start processing records [create date: %s] [settlement date: %s]", record[1], record[2]))
			case file.Footer:
				countFromFooter = w.processClearingRecordFooterRow(ctx, record)
				break forLoop
			case file.TransactionDetails:
				ctx = slog.AddTagsToContext(
					ctx,
					slog.CustomTag("lineNumber", lineNumber),
					slog.CustomTag("fileName", fileName),
					slog.CustomTag("RRN", record[12]),
					slog.CustomTag("phase", stage),
				)
				slog.FromContext(ctx).Info(logTag, fmt.Sprintf("start processing records [phase: %s] [fileName: %s] [line: %d] [RRN: %s]", stage, fileName, lineNumber, record[12]))

				if processUtil.IsProxyInListOrThreshold(strings.TrimSpace(record[1]), w.QueueHandlerClearingConfig.AllowedProxyNumbers, w.QueueHandlerClearingConfig.AllowedProxyNumberThreshold) {
					slog.FromContext(ctx).Info(logTag, fmt.Sprintf("record [proxy number: %s] will be processed by new clearing queue handler", record[1]))
					continue
				}

				if w.FeatureFlags.EnableClearingRateLimiting {
					w.processTxnDetailsWithRateLimiting(ctx, record, lineNumber, stage, fileName, fileErr, &successCountSafe)
				} else if hasRun := w.processTxnDetailsWithDelay(ctx, record, lineNumber, stage, fileName, fileErr); hasRun {
					successfulProcessCount++
				}
			}
		}
		slog.FromContext(ctx).Info(logTag, fmt.Sprintf("finish clearing stage [%s]", stage))
		if stageIdx < len(clearingStages)-1 {
			time.Sleep(time.Duration(w.AdminConfig.ClearingBetweenStagesDelayInMs) * time.Millisecond) // further sleep between stages
		}
	}
	if w.FeatureFlags.EnableClearingRateLimiting {
		successfulProcessCount = successCountSafe.Val()
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("finished processing clearing file: successful count [%d], footer line count [%d]",
		successfulProcessCount, countFromFooter))
	if countFromFooter != successfulProcessCount {
		slog.FromContext(ctx).Warn(logTag, "not all lines are processed successfully")
	}
	return fileErr
}

func (w *WorkflowImpl) processTxnDetailsWithDelay(ctx context.Context, record []string, lineNumber int, stage clearingStage, fileName string, fileErr map[string]string) bool {
	var shouldSleep, hasRun bool
	if hasRun, err := w.processTransactionDetails(ctx, record, lineNumber, stage, fileName); err != nil {
		fileErr[strconv.Itoa(lineNumber)] = err.Error()
	} else if hasRun {
		shouldSleep = true
	}
	if shouldSleep {
		if stage == PartialCaptureClearing {
			time.Sleep(time.Duration(w.AdminConfig.ClearingPartialCaptureDelayInMs) * time.Millisecond) // prevent partial capturing a partial-capture-in-progress txn
		} else {
			time.Sleep(time.Duration(w.AdminConfig.ClearingDefaultDelayInMs) * time.Millisecond)
		}
	}

	return hasRun
}

func (w *WorkflowImpl) processTxnDetailsWithRateLimiting(ctx context.Context, record []string, lineNumber int, stage clearingStage, fileName string, fileErr map[string]string, successCounter *safeCounter) {
	switch stage {
	case PartialCaptureClearing: // partial capture can have data racing issue easily
		if hasRun, err := w.processTransactionDetails(ctx, record, lineNumber, stage, fileName); err != nil {
			fileErr[strconv.Itoa(lineNumber)] = err.Error()
		} else if hasRun {
			time.Sleep(time.Duration(w.AdminConfig.ClearingPartialCaptureDelayInMs) * time.Millisecond) // prevent partial capturing a partial-capture-in-progress txn
		}
	case NonRefundFinalCaptureClearing, RefundClearing:
		if err := sem.Acquire(context.Background(), 1); err != nil {
			slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("error acquiring lock from Semaphore at line %d", lineNumber))
			fileErr[strconv.Itoa(lineNumber)] = err.Error()
		} else { //successfully acquire semaphore
			gconcurrent.Go(context.Background(), logTag, func(c context.Context) error {
				return processTxnDetailsInGoRoutine(ctx, w, record, lineNumber, stage, fileName, fileErr, successCounter)
			})
		}
	}
}

func (w *WorkflowImpl) processTransactionDetails(ctx context.Context, recordArr []string, lineNumber int, stage clearingStage, fileName string) (bool, error) {
	var err error
	if err = basicValidation(recordArr); err != nil {
		slog.FromContext(ctx).Info(logTag, fmt.Sprintf("clearing record at line %d does not pass basic validation", lineNumber), slog.Error(err))
	} else if isCurrentStage(stage, recordArr) {
		if err = w.processClearingRecordDataRow(ctx, recordArr, fileName); err != nil {
			slog.FromContext(ctx).Info(logTag, fmt.Sprintf("could not process clearing record at line %d", lineNumber), slog.Error(err))
		}
		return true, err
	}

	return false, err
}

func _processTxnDetailsInGoRoutine(ctx context.Context, w *WorkflowImpl, recordArr []string, lineNumber int, stage clearingStage, fileName string, fileErr map[string]string, successCounter *safeCounter) error {
	defer sem.Release(1)
	start := time.Now()
	if err := basicValidation(recordArr); err != nil {
		slog.FromContext(ctx).Info(logTag, fmt.Sprintf("clearing record at line %d does not pass basic validation", lineNumber), slog.Error(err))
		fileErr[strconv.Itoa(lineNumber)] = err.Error()
		return err
	}
	if isCurrentStage(stage, recordArr) {
		if err := w.processClearingRecordDataRowV2(ctx, recordArr, fileName); err != nil {
			slog.FromContext(ctx).Info(logTag, fmt.Sprintf("could not process clearing record at line %d", lineNumber), slog.Error(err))
			fileErr[strconv.Itoa(lineNumber)] = err.Error()
			return err
		}
		successCounter.Inc()
		delay := time.Duration(w.AdminConfig.ClearingDefaultDelayInMs) * time.Millisecond
		timeLapse := time.Since(start)
		w.StatsD.Duration(logic.EuronetAdapter, fmt.Sprintf("%s_%s", logic.ProcessClearingStatsdTag, "clearing_goroutine"), start)
		if timeLapse < delay {
			time.Sleep(delay - timeLapse)
		}
	}
	return nil
}

func isCurrentStage(stage clearingStage, recordArr []string) bool {
	first2ProcessingCode := recordArr[2][:2]
	mrc := strings.TrimSpace(recordArr[25])
	switch stage {
	case RefundClearing:
		return first2ProcessingCode == "20"
	case PartialCaptureClearing:
		return first2ProcessingCode != "20" && mrc == file.GetPartialClearingMRC()
	case NonRefundFinalCaptureClearing:
		return first2ProcessingCode != "20" && mrc != file.GetPartialClearingMRC()
	}
	return false
}

//nolint:dupl
func (w *WorkflowImpl) processClearingRecordDataRowV2(ctx context.Context, recordArr []string, fileName string) error {
	clearingRecord, err := w.constructClearingRecord(ctx, recordArr, fileName)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed to construct clearing record",
			slog.CustomTag(string(constant.Clearing), recordArr), slog.Error(err))
		return err
	}
	if err = w.processClearingRecordV2(ctx, clearingRecord); err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed to process clearing record", slog.Error(err))
		return err
	}
	return nil
}

func (w *WorkflowImpl) processClearingRecordV2(ctx context.Context, record *dto.ClearingRecord) error {
	if record.ProcessingCode[:2] == "20" {
		w.StatsD.Count1(logic.EuronetAdapter, logic.ProcessClearingStatsdTag, fmt.Sprintf("clearing_type:%s", constant.Refund))
		if err := doRefund(ctx, w, record); err != nil {
			return err
		}
		return nil
	}
	// by default using enhanced matching logic
	err := w.processEnhancedMatchingLogic(ctx, record, true)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Processing enhanced matching logic failed for RRN: %s and TransactionLifeCycleID: %s and Proxy Number: %s",
			record.RetrievalReferenceNumber, record.TransactionLifeCycleID, record.ProxyNumber),
			slog.CustomTag(string(constant.Clearing), record), slog.Error(err))
		return err
	}
	return nil
}
