package processing

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common"
	"io"
	"math"
	"regexp"
	"strconv"
	"time"

	"github.com/google/uuid"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/common/dto"
	"gitlab.com/gx-regional/dbmy/digicard/common/utils"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/file"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/cache"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/stats"
	"gitlab.myteksi.net/dakota/common/currency"
	"gitlab.myteksi.net/dakota/common/redis"
	paymentCoreAPI "gitlab.myteksi.net/dakota/payment/payment-core/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

// JVPostingRecord is the record stored in cache for JV postings
type JVPostingRecord struct {
	AccountInFile     string
	GLTransactionType string
	Amount            string
	Currency          string
	DCIndicator       string
	Status            string
	PostingDateTime   time.Time
}

// JVFileRecordObject ...
type JVFileRecordObject struct {
	GLNumber        string
	TransactionType string
	Currency        string
	Amount          float64
	DCIndicator     string
	Description     string
}

var defaultCurrency = currency.MYR.NumericCode
var now = time.Now //for unit test injection

//nolint:gocognit
func (w *WorkflowImpl) processJVFile(ctx context.Context, reader io.Reader, fileName string) fileErrors {
	// https://gxsbank.atlassian.net/wiki/spaces/CARDS/pages/*********/JV+Postings
	start := now()
	csvReader := csv.NewReader(reader)
	csvReader.FieldsPerRecord = -1 // file is not having constant number of columns

	var lineNumber, processCount, successfulProcessCount int
	var expectedProcessedTxnType int
	for _, v := range w.JVConfig {
		if v.Enable {
			expectedProcessedTxnType++
		}
	}
	fileErr := fileErrors(make(map[string]string))
	fileDate := getFileDate(fileName)
	fileCtx := slog.AddTagsToContext(ctx,
		slog.CustomTag("JV.enabledTransactionTypes", expectedProcessedTxnType),
		slog.CustomTag("JV.fileName", common.GetFileNameFromPath(fileName)),
		slog.CustomTag("JV.fileDate", fileDate),
		slog.CustomTag("JV.fileDateIsToday", fileDate == now().Format(constant.FormatYYYYMMDD)),
	)
	slog.FromContext(fileCtx).Info(logTag, "start processing JV file")
	defer func() {
		finalCtx := slog.AddTagsToContext(fileCtx,
			slog.CustomTag("JV.lineCount", lineNumber),
			slog.CustomTag("JV.processCount", processCount),
			slog.CustomTag("JV.successCount", successfulProcessCount),
			slog.CustomTag("JV.duration", now().Sub(start).Milliseconds()),
		)
		slog.FromContext(finalCtx).Info(logTag, "finished process JV file")
	}()

	for {
		lineNumber++
		lineCtx := slog.AddTagsToContext(fileCtx, slog.CustomTag("JV.lineNumber", lineNumber))
		record, err := csvReader.Read()
		if err != nil {
			if err == io.EOF {
				break
			}
			slog.FromContext(lineCtx).Warn(logTag, fmt.Sprintf("error reading line %d from csv file", lineNumber), slog.Error(err))
			fileErr[strconv.Itoa(lineNumber)] = err.Error()
			// TODO: add stats
			continue
		}

		csvCtx := slog.AddTagsToContext(lineCtx, slog.CustomTag("JV.csv", record))

		// covert record in file to object
		jvRecordObject, err := w.convertJvRecordToObject(csvCtx, record)
		if err != nil {
			if err == io.EOF {
				break
			}
			if errors.Is(err, logic.ErrJVZeroOriginalAmount) {
				slog.FromContext(csvCtx).Warn(logTag, fmt.Sprintf("Skipping row for zero original amount"),
					slog.CustomTag("JV.skipReason", "zero_original_amount"))
				w.StatsD.Count1(logTag, string(constant.JV), "SKIPPED", "reason:zero_original_amount")
				continue
			}
			slog.FromContext(csvCtx).Warn(logTag, fmt.Sprintf("Skipping row for error parsing csv line"), slog.Error(err),
				slog.CustomTag("JV.skipReason", "error_parse_record"))
			w.StatsD.Count1(logTag, string(constant.JV), "SKIPPED", "reason:error_parse_record")
			continue
		}

		jvRecordCtx := slog.AddTagsToContext(lineCtx, slog.CustomTag("JV.record", jvRecordObject))
		jvConfig, ok := w.JVConfig[jvRecordObject.TransactionType]
		if !ok {
			slog.FromContext(jvRecordCtx).Warn(logTag, fmt.Sprintf("Skipping row for unknown transaction type '%s'", jvRecordObject.TransactionType),
				slog.CustomTag("JV.skipReason", "transaction_type_not_configured"))
			w.StatsD.Count1(logTag, string(constant.JV), "SKIPPED", "reason:transaction_type_not_configured")
			continue
		}
		if !jvConfig.Enable {
			slog.FromContext(jvRecordCtx).Warn(logTag, fmt.Sprintf("Skipping row for disabled transaction type '%s'", jvRecordObject.TransactionType),
				slog.CustomTag("JV.skipReason", "transaction_type_not_enabled"))
			w.StatsD.Count1(logTag, string(constant.JV), "SKIPPED", "reason:transaction_type_not_enabled")
			continue
		}

		processCount++
		if err := w.processJVRecordDataRow(jvRecordCtx, jvRecordObject, jvConfig); err != nil {
			slog.FromContext(jvRecordCtx).Warn(logTag, fmt.Sprintf("Could not process JV record for transaction type '%s'", jvRecordObject.TransactionType), slog.Error(err))
			fileErr[strconv.Itoa(lineNumber)] = err.Error()
		} else {
			successfulProcessCount++
		}
	}
	slog.FromContext(fileCtx).Info(logTag, fmt.Sprintf("Read %d lines from JV file '%s'. Successfully processed %d records",
		lineNumber, fileName, successfulProcessCount))

	if successfulProcessCount != expectedProcessedTxnType {
		slog.FromContext(fileCtx).Warn(logTag, "Not all transaction types are processed successfully")
	}
	return fileErr
}

func (w *WorkflowImpl) processJVRecordDataRow(ctx context.Context, recordObject *JVFileRecordObject, jvConfig config.JVProcessCfg) error {
	jvTransferRequest, err := w.constructTransferRequest(recordObject, jvConfig)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed to construct JV record",
			slog.CustomTag(string(constant.JV), recordObject), slog.Error(err))
		w.StatsD.Count1(logTag, string(constant.JV), constant.Failed, fmt.Sprintf("error:%s", err.Error()))
		return err
	}
	reqCtx := slog.AddTagsToContext(ctx, slog.CustomTag("JV.transferRequest", jvTransferRequest))
	slog.FromContext(reqCtx).Info(logTag, fmt.Sprintf("mastercard jv processing for ReferenceID '%s' src '%s' dst '%s' txnCodeType '%s' txnType '%s'",
		jvTransferRequest.ReferenceID, jvTransferRequest.SrcAcctID, jvTransferRequest.DestAcctID, jvTransferRequest.TransactionCode.Type, recordObject.TransactionType))

	if err = w.initiateTransferForJVRecord(reqCtx, jvTransferRequest); err != nil {
		slog.FromContext(reqCtx).Error(logTag, "failed to process JV record",
			slog.Error(err), slog.CustomTag(constant.ReferenceIDTag, jvTransferRequest.ReferenceID))
		w.StatsD.Count1(logTag, string(constant.JV), constant.Failed, fmt.Sprintf("error:%s", err.Error()))
		// TODO: Notify JV posting transaction failure
		// https://gxsbank.atlassian.net/browse/CARDS-1471
		return err
	}

	jvPostingRecord := JVPostingRecord{
		AccountInFile:     recordObject.GLNumber,
		GLTransactionType: recordObject.TransactionType,
		Currency:          recordObject.Currency,
		Amount:            strconv.FormatFloat(recordObject.Amount, 'f', -1, 64),
		DCIndicator:       recordObject.DCIndicator,
		Status:            constant.Processing,
		PostingDateTime:   now(),
	}
	// saving posting record entry to redis with transfer refID as key
	err = cache.Set(reqCtx, jvTransferRequest.ReferenceID, jvPostingRecord, cache.JVPostingCacheTTL)
	if err != nil {
		// store caching error in file table
		slog.FromContext(reqCtx).Warn(logTag, "failed to cache JV record", slog.Error(err))
		return err
	}
	return nil
}

func (w *WorkflowImpl) constructTransferRequest(record *JVFileRecordObject, jvConfig config.JVProcessCfg) (*paymentCoreAPI.InternalTransferRequest, error) {
	if !jvConfig.Enable {
		return nil, fmt.Errorf("unsupported transaction type in JV. type:[%s]", record.TransactionType)
	}
	originalAmount := math.Abs(math.Round(record.Amount * 100))

	sourceAccountID, destinationAccountID, transactionCodeType := logic.IdentifyAccountsFromDCIndicator(record.DCIndicator, jvConfig)

	if sourceAccountID == "" || destinationAccountID == "" {
		return nil, fmt.Errorf("unhandled/invalid GL number in file. GLNumber:[%s]", record.GLNumber)
	}
	description := record.Description
	return &paymentCoreAPI.InternalTransferRequest{
		ReferenceID: uuid.NewString(),
		Amount:      int64(originalAmount),
		Currency:    currency.MYR.Code,
		SrcAcctID:   sourceAccountID,
		DestAcctID:  destinationAccountID,
		GroupID:     uuid.NewString(),
		Remarks:     description,
		TransactionCode: &paymentCoreAPI.TransactionCode{
			Domain:  jvConfig.TransactionDomain,
			Type:    transactionCodeType,
			Subtype: jvConfig.TransactionSubType,
		},
		GroupTag: string(constant.JVPostingGroupTag),
	}, nil
}

func (w *WorkflowImpl) initiateTransferForJVRecord(ctx context.Context, request *paymentCoreAPI.InternalTransferRequest) error {
	response, err := w.PaymentCoreClient.InternalTransfer(ctx, request)
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "PaymentCoreClient.InternalTransfer returned error", slog.Error(err))
		return logic.ErrPaymentCoreFailure
	}
	if response.Status == constant.Failed {
		slog.FromContext(ctx).Error(logTag, "PaymentCoreClient.InternalTransfer returned failed status",
			slog.CustomTag(constant.ReferenceIDTag, request.ReferenceID))
		return logic.ErrPaymentCoreFailure
	}
	return nil
}

// ConsumeFromJVTransferStream ...
func ConsumeFromJVTransferStream(ctx context.Context, message *dto.PaymentCoreStreamMessage) error {
	slog.FromContext(ctx).Info(logTag, "updating JV posting record")

	jvRecord := &JVPostingRecord{}
	err := cache.Get(ctx, message.ReferenceID, jvRecord)
	if err == redis.ErrNoData {
		slog.FromContext(ctx).Error(logTag, "jv record not found in cache given refID")
		stats.StatsDClient.Count1(logTag, string(constant.JV), constant.Failed, fmt.Sprintf("error:%s", err.Error()))
		return logic.ErrJVRecordNotFound
	}
	if err != nil {
		slog.FromContext(ctx).Error(logTag, "error loading jv record from cache", slog.Error(err))
		stats.StatsDClient.Count1(logTag, string(constant.JV), constant.Failed, fmt.Sprintf("error:%s", err.Error()))
		return err
	}

	// TODO: Notify Ops and oncall that posting is not successful
	// https://gxsbank.atlassian.net/browse/CARDS-1471
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(processingTypeLogTag, jvRecord.GLTransactionType))
	switch message.Status {
	case constant.Success:
		stats.StatsDClient.Count1(logTag, string(constant.JV), constant.Success)
	case constant.Failed:
		stats.StatsDClient.Count1(logTag, string(constant.JV), constant.Failed,
			fmt.Sprintf("error:%s", message.ErrorCode))
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("JV posting failed. error code: [%v] error message: [%v]",
			message.ErrorCode, message.ErrorMessage))
	default:
		stats.StatsDClient.Count1(logTag, string(constant.JV), constant.Failed, "error:wrong message status")
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("unhandled jv posting status. status: [%v]", message.Status))
		return nil
	}

	jvRecord.Status = message.Status
	err = cache.Set(ctx, message.ReferenceID, jvRecord, cache.JVPostingCacheTTL)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "error updating jv record in cache", slog.Error(err))
		return err
	}
	return nil
}
func (w *WorkflowImpl) convertJvRecordToObject(ctx context.Context, record []string) (*JVFileRecordObject, error) {
	// we can accept both MYR and 458 in the jv file currency column
	validCurrency := []string{currency.MYR.Code, currency.MYR.NumericCode}
	if len(record) != file.JVFileRecordLength {
		return nil, errors.New("wrong number of columns")
	}

	originalAmount, err := strconv.ParseFloat(record[3], 64)
	if err != nil {
		return nil, errors.New("failed to parse original amount")
	}
	if originalAmount == 0.0 {
		return nil, logic.ErrJVZeroOriginalAmount
	}

	jvCurrency := record[2]
	if len(jvCurrency) == 0 {
		jvCurrency = defaultCurrency //TCH-1221 default currency to MYR if not provided
		w.StatsD.Count1(logTag, string(constant.JV), "DEFAULT_CURRENCY", fmt.Sprintf("TRANSACTIONTYPE:%s", record[1]))
		slog.FromContext(ctx).Info(logTag, fmt.Sprintf("empty currency replaced with default '%s'", jvCurrency))
	} else {
		if len(jvCurrency) < 3 || !utils.SearchStringArray(validCurrency, jvCurrency) {
			return nil, errors.New("invalid currency code")
		}
	}

	return &JVFileRecordObject{
		GLNumber:        record[0],
		TransactionType: record[1],
		Currency:        jvCurrency,
		Amount:          originalAmount,
		DCIndicator:     record[4],
		Description:     record[5],
	}, nil
}

func getFileDate(filename string) string {
	regex := regexp.MustCompile(`DBMY_\w+_JV_Report_(?P<date>\d{8})_[\d_]+\..+`)
	if matches := regex.FindStringSubmatch(filename); len(matches) > 0 {
		if dateIndex := regex.SubexpIndex("date"); dateIndex >= 0 {
			return matches[dateIndex]
		}
	}
	return ""
}
