package acs

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	we "gitlab.myteksi.net/dakota/workflowengine"
	identityExperience "gitlab.myteksi.net/dbmy/identity-experience/api"
	"gitlab.myteksi.net/gophers/go/commons/util/tags"
)

func (w *WorkflowImpl) pushNotif(ctx context.Context, id string, execData we.ExecutionData, params interface{}) (we.ExecutionData, error) {
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logAuthn, "Invalid context passed in persistRequest state")
		return nil, logic.ErrInvalidContext
	}

	safeID, err := logic.FetchCustomerSafeID(ctx, currCtx.PersistedAuthenticate.ProxyNumber, logAuthn)
	if err != nil {
		slog.FromContext(ctx).Warn(logAuthn, "can't FetchCustomerSafeID", tags.T("error", err))
		return nil, logic.ErrGenericServer
	}

	nextCtx := currCtx.Clone()
	nextCtx.State = stPushedNotifSuccess
	authnReq := nextCtx.AuthenticateRequest
	authTimestampInUTC := parseAuthDateTimeToUTC(ctx, authnReq.AuthDateTime, logAuthn)
	finTrustReq := &identityExperience.EnableAppAuthRequest{
		SafeID:     safeID,
		Type:       identityExperience.AppAuthType_CARD,
		EnablePush: true,
		Transaction: &identityExperience.AppAuthTransaction{
			Recipient:           authnReq.MerchantName,
			Amount:              authnReq.TransactionDisplayAmount,
			TransactionID:       authnReq.ACSTransactionID,
			TransactionDatetime: authTimestampInUTC,
			TransactionDetails:  authnReq.AuthMessage,
			MaskedCardNumber:    authnReq.MaskedCardNumber,
			AccountID:           authnReq.CustID,
		},
	}

	if b, err2 := json.Marshal(finTrustReq); err2 != nil {
		slog.FromContext(ctx).Error(logAuthn, "can't marshall finTrustReq", tags.T("error", err2))
	} else {
		nextCtx.PersistedAuthenticate.RequestToFinTrust = b
	}

	if w.ByPassFinTrustFlag {
		slog.FromContext(ctx).Warn(logAuthn, "BYPASS call to finTrust")
		nextCtx.PersistedAuthenticate.ResponseFromFinTrust = []byte(`{"byPass": true}`)
		nextCtx.PersistedAuthenticate.UpdatedAt = time.Now()
		nextCtx.State = stPushedNotifSuccess
	} else if err = w.IdentityExperienceClient.EnableAppAuth(ctx, finTrustReq); err != nil {
		slog.FromContext(ctx).Error(logAuthn, "call to finTrust got error", tags.T("error", err))
		jsonErr := fmt.Sprintf(`{"error": "%s"}`, err.Error())
		b, _ := json.Marshal(jsonErr)
		nextCtx.PersistedAuthenticate.ResponseFromFinTrust = b
		nextCtx.PersistedAuthenticate.UpdatedAt = time.Now()
		nextCtx.State = stPushedNotifFail
	}

	// persist db
	if err := storage.ACSAuthenticateD.UpdateEntity(ctx, &currCtx.PersistedAuthenticate, &nextCtx.PersistedAuthenticate); err != nil {
		slog.FromContext(ctx).Error(logAuthn, "can't persist data into db", tags.T("error", err))
	}

	return nextCtx, nil
}

func parseAuthDateTimeToUTC(ctx context.Context, authDatetime, logTag string) string {
	timeFormat := "2006-01-02T15:04:05.999-07:00"
	authTimeInOriginalTimezone, err := time.Parse(timeFormat, authDatetime)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "failed to parse auth date time", slog.Error(err))
		return time.Now().UTC().Format("2006-01-02 15:04:05")
	}
	return authTimeInOriginalTimezone.UTC().Format("2006-01-02 15:04:05")
}
