// Package security ...
//
//nolint:dupl
package security

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"fmt"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common/security"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

func (c *clientImpl) EncryptAuthnResponse(ctx context.Context, request *dto.ACSAuthResponseDetails) (*api.ACSAuthenticateResponse, error) {
	logger := slog.FromContext(ctx)
	digitalSignature, err := c.signAuthnResponse(ctx, request)
	if err != nil {
		logger.Error(logAuthnTag, "failed sign authn response details", slog.Error(err))
		return nil, err
	}
	responseDetailsWithSignature := &dto.ACSAuthResponseDetailsWithSignature{
		Response:  request,
		Signature: digitalSignature,
	}
	encryptedString, errEncrypt := encryptForAuthnResp(ctx, responseDetailsWithSignature, c.authnAESKey)
	if errEncrypt != nil {
		logger.Error(logAuthnTag, "failed to encrypt for authn callback", slog.Error(errEncrypt))
		return nil, errEncrypt
	}
	logger.Info(logAuthnTag, fmt.Sprintf("encrypted:\n%s\nsignature:\n%s\n", encryptedString, digitalSignature))
	if err != nil {
		logger.Error(logAuthnTag, "failed encrypt authn response details with signature", slog.Error(err))
		return nil, err
	}
	return &api.ACSAuthenticateResponse{EncryptedRequest: encryptedString}, nil
}

func (c *clientImpl) signAuthnResponse(ctx context.Context, authResponseDetails *dto.ACSAuthResponseDetails) (string, error) {
	logger := slog.FromContext(ctx)
	rawBytes, err := json.Marshal(authResponseDetails)
	logger.Info(logAuthnTag, fmt.Sprintf("plaintext:\n%s\n", string(rawBytes)))
	if err != nil {
		logger.Error(logAuthnTag, "failed to marshal authn response details", slog.Error(err))
		return "", err
	}
	return c.SignatureClient.Sign(string(rawBytes), c.authnRespRSAPrivateKey)
}

func encryptForAuthnResp(ctx context.Context, authResponseWithSignature *dto.ACSAuthResponseDetailsWithSignature, aesKey string) (string, error) {
	logger := slog.FromContext(ctx)
	secretKey := security.GenerateSHA1Key(aesKey)
	block, err := aes.NewCipher(secretKey)
	if err != nil {
		logger.Error(logAuthnTag, "failed to parse authn AES key for response encryption", slog.Error(err))
		return "", err
	}
	rawBytes, err := json.Marshal(authResponseWithSignature)
	if err != nil {
		logger.Error(logAuthnTag, "failed to marshal authn response details", slog.Error(err))
		return "", err
	}
	logger.Info(logAuthnTag, fmt.Sprintf("plaintext:\n%s\n", string(rawBytes)))
	iv := make([]byte, aes.BlockSize)

	// Add padding
	rawBytes = security.PKCS5Padding(rawBytes)

	mode := cipher.NewCBCEncrypter(block, iv)
	cipherText := make([]byte, len(rawBytes))
	mode.CryptBlocks(cipherText, rawBytes)

	return base64.StdEncoding.EncodeToString(cipherText), nil
}
