package logic

// for Paynet request validation, let's start with prefix 26
var (
	ErrInvalidPaynetMti                            = AdapterError{Code: "2601", Message: "Missing/Invalid mti"}
	ErrInvalidPaynetProxyNumber                    = AdapterError{Code: "2602", Message: "Missing/Invalid proxyNumber"}
	ErrInvalidPaynetProcessingCode                 = AdapterError{Code: "2603", Message: "Missing/Invalid processingCode"}
	ErrInvalidPaynetTransactionAmount              = AdapterError{Code: "2604", Message: "Missing/Invalid transactionAmount"}
	ErrInvalidPaynetSettlementAmount               = AdapterError{Code: "2605", Message: "Missing/Invalid settlementAmount"}
	ErrInvalidPaynetTransmissionDateTime           = AdapterError{Code: "2606", Message: "Missing/Invalid transmissionDateTime"}
	ErrInvalidPaynetSystemTraceAuditNumber         = AdapterError{Code: "2607", Message: "Missing/Invalid systemTraceAuditNumber"}
	ErrInvalidPaynetLocalTransactionDateTime       = AdapterError{Code: "2608", Message: "Missing/Invalid localTransactionDateTime"}
	ErrInvalidPaynetSettlementDate                 = AdapterError{Code: "2609", Message: "Missing/Invalid settlementDate"}
	ErrInvalidPaynetCaptureDate                    = AdapterError{Code: "2610", Message: "Missing/Invalid captureDate"}
	ErrInvalidPaynetMerchantCategoryCode           = AdapterError{Code: "2611", Message: "Missing/Invalid merchantCategoryCode"}
	ErrInvalidPaynetAcquirerCountryCode            = AdapterError{Code: "2612", Message: "Missing/Invalid acquirerCountryCode"}
	ErrInvalidPaynetPosEntryMode                   = AdapterError{Code: "2613", Message: "Missing/Invalid posEntryMode"}
	ErrInvalidPaynetPosConditionCode               = AdapterError{Code: "2614", Message: "Missing/Invalid posConditionCode"}
	ErrInvalidPaynetNetworkID                      = AdapterError{Code: "2615", Message: "Missing/Invalid networkID"}
	ErrInvalidPaynetTransactionFeeAmount           = AdapterError{Code: "2616", Message: "Missing/Invalid transactionFeeAmount"}
	ErrInvalidPaynetProcessingFeeAmount            = AdapterError{Code: "2617", Message: "Missing/Invalid processingFeeAmount"}
	ErrInvalidPaynetAcquirerID                     = AdapterError{Code: "2618", Message: "Missing/Invalid acquirerID"}
	ErrInvalidPaynetRetrievalReferenceNumber       = AdapterError{Code: "2619", Message: "Missing/Invalid retrievalReferenceNumber"}
	ErrInvalidPaynetAuthorizationID                = AdapterError{Code: "2620", Message: "Missing/Invalid authorizationID"}
	ErrInvalidPaynetCardAcceptorTerminalID         = AdapterError{Code: "2621", Message: "Missing/Invalid cardAcceptorTerminalID"}
	ErrInvalidPaynetCardAcceptorIDCode             = AdapterError{Code: "2622", Message: "Missing/Invalid cardAcceptorIDCode"}
	ErrInvalidPaynetCardAcceptorTermName           = AdapterError{Code: "2623", Message: "Missing/Invalid cardAcceptorTermName"}
	ErrInvalidPaynetRetailerID                     = AdapterError{Code: "2624", Message: "Missing/Invalid retailerID"}
	ErrInvalidPaynetTransactionCurrencyCode        = AdapterError{Code: "2625", Message: "Missing/Invalid transactionCurrencyCode"}
	ErrInvalidPaynetCardHolderCurrencyCode         = AdapterError{Code: "2626", Message: "Missing/Invalid cardHolderCurrencyCode"}
	ErrInvalidPaynetTransactionID                  = AdapterError{Code: "2627", Message: "Missing/Invalid transactionID"}
	ErrInvalidPaynetOrigTransactionID              = AdapterError{Code: "2628", Message: "Missing/Invalid origTransactionID"}
	ErrInvalidPaynetThreeDsAuthTID                 = AdapterError{Code: "2629", Message: "Missing/Invalid threeDsAuthTID"}
	ErrInvalidPaynetThreeDsAuthResultCode          = AdapterError{Code: "2630", Message: "Missing/Invalid threeDsAuthResultCode"}
	ErrInvalidPaynetSecondFactorAuthCode           = AdapterError{Code: "2631", Message: "Missing/Invalid secondFactorAuthCode"}
	ErrInvalidPaynetCnpPaymentIndicator            = AdapterError{Code: "2632", Message: "Missing/Invalid cnpPaymentIndicator"}
	ErrInvalidPaynetCnpOrigRRN                     = AdapterError{Code: "2633", Message: "Missing/Invalid cnpOrigRRN"}
	ErrInvalidPaynetCnpOrigTransactionDate         = AdapterError{Code: "2634", Message: "Missing/Invalid cnpOrigTransactionDate"}
	ErrInvalidPaynetTransitTransactionTypeID       = AdapterError{Code: "2635", Message: "Missing/Invalid transitTransactionTypeID"}
	ErrInvalidPaynetTransitTransportModeID         = AdapterError{Code: "2636", Message: "Missing/Invalid transitTransportModeID"}
	ErrInvalidPaynetTransitTransactionDetails      = AdapterError{Code: "2637", Message: "Missing/Invalid transitTransactionDetails"}
	ErrInvalidPaynetTransitOrigRRN                 = AdapterError{Code: "2638", Message: "Missing/Invalid transitOrigRRN"}
	ErrInvalidPaynetTransitOrigTransactionDate     = AdapterError{Code: "2639", Message: "Missing/Invalid transitOrigTransactionDate"}
	ErrInvalidPaynetTerminalAddress                = AdapterError{Code: "2640", Message: "Missing/Invalid terminalAddress"}
	ErrInvalidPaynetTerminalOwnerFIID              = AdapterError{Code: "2641", Message: "Missing/Invalid terminalOwnerFIID"}
	ErrInvalidPaynetAcquirerFIID                   = AdapterError{Code: "2642", Message: "Missing/Invalid acquirerFIID"}
	ErrInvalidPaynetThreeDsValidation              = AdapterError{Code: "2643", Message: "Missing/Invalid threeDsValidation"}
	ErrInvalidPaynetPANValidation                  = AdapterError{Code: "2644", Message: "Missing/Invalid PANValidation"}
	ErrInvalidPaynetExpiryDateValidation           = AdapterError{Code: "2645", Message: "Missing/Invalid expiryDateValidation"}
	ErrInvalidPaynetPinValidation                  = AdapterError{Code: "2646", Message: "Missing/Invalid pinValidation"}
	ErrInvalidPaynetCVVValidation                  = AdapterError{Code: "2647", Message: "Missing/Invalid CVVValidation"}
	ErrInvalidPaynetCVV2Validation                 = AdapterError{Code: "2648", Message: "Missing/Invalid CVV2Validation"}
	ErrInvalidPaynetEMVValidation                  = AdapterError{Code: "2649", Message: "Missing/Invalid EMVValidation"}
	ErrInvalidPaynetDeclineCode                    = AdapterError{Code: "2650", Message: "Missing/Invalid DeclineCode"}
	ErrInvalidPaynetDeclineReason                  = AdapterError{Code: "2651", Message: "Missing/Invalid DeclineReason"}
	ErrInvalidPaynetCardAcceptorMerchantID         = AdapterError{Code: "2652", Message: "Missing/Invalid CardAcceptorMerchantID"}
	ErrInvalidPaynetCardHolderBillingAmount        = AdapterError{Code: "2653", Message: "Missing/Invalid CardHolderBillingAmount"}
	ErrInvalidPaynetCardAcceptorTermNameValidation = AdapterError{Code: "2654", Message: "Missing/Invalid PaynetCardAcceptorTermName"}
	ErrInvalidPaynetTransmissionDateValidation     = AdapterError{Code: "2655", Message: "Missing/Invalid TransmissionDate"}

	ErrPaynetDuplicateRRN            = AdapterError{Code: "2660", Message: "Duplicate RRN"}
	ErrPaynetInvalidRefundAmount     = AdapterError{Code: "2698", Message: "Invalid Refund Amount"}
	ErrPaynetStillNotHandle          = AdapterError{Code: "2699", Message: "Internal Error"}
	ErrPaynetUnableDetectTransaction = AdapterError{Code: "2700", Message: "Unable to detect transaction type"}
	// TODO: add more if needed
)
