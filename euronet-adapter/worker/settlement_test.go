package worker

import (
	"context"
	"errors"
	"fmt"
	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/sftputils"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/file"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/cache"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/stats"
	"gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

func TestSettlement(t *testing.T) {
	setDefaultConfig()
	errDummy := errors.New("simulate error")
	mockRedis := &mocks.Client{}
	mockLock := &mocks.Lock{}
	mockWf := &file.MockWorkflow{}
	mockSftp := &sftputils.MockClient{}
	stats.StatsDClient = statsd.NewNoop()
	scenarios := []struct {
		errLock     error
		expectedErr error
		errExecute  error
		errBlackout bool
		mockFunc    func()
		description string
	}{
		{
			mockFunc: func() {
				mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(
					mockLock, errDummy).Once()
				mockLock.On("Unlock", mock.Anything).Return(nil).Once()
			},
			description: "failed to get lock, skipping",
		},
		{
			expectedErr: errDummy,
			mockFunc: func() {
				mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(
					mockLock, nil).Once()
				mockLock.On("Unlock", mock.Anything).Return(nil).Once()
				mockSftp.On("ListFiles", mock.Anything, mock.Anything).Return(nil, errDummy).Once()
			},
			description: "failed to list file from sftp server",
		},
		{
			mockFunc: func() {
				mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(
					mockLock, nil).Once()
				mockLock.On("Unlock", mock.Anything).Return(nil).Once()
				mockSftp.On("ListFiles", mock.Anything, mock.Anything).Return(nil, errors.New("file does not exist")).Once()
			},
			description: "no such folder",
		},
		{
			expectedErr: errDummy,
			mockFunc: func() {
				mockRedis.On("TryLock", mock.Anything, SettlementWorkerRedisLock, mock.Anything).Return(
					mockLock, nil).Once()
				mockRedis.On("TryLock", mock.Anything, fmt.Sprintf("%s_%s", SettlementProcessingRedisLock, "test.csv"), mock.Anything).Return(
					mockLock, nil).Once()
				mockWf.On("ExecuteFileWorkflow", mock.Anything, mock.Anything).Return(errDummy).Once()
				mockLock.On("Unlock", mock.Anything).Return(nil).Twice()
				mockSftp.On("ListFiles", mock.Anything, mock.Anything).Return([]string{"test.csv"}, nil).Once()
			},
			description: "workflow execute failed",
		},
		{
			expectedErr: nil,
			mockFunc: func() {
				mockRedis.On("TryLock", mock.Anything, SettlementWorkerRedisLock, mock.Anything).Return(
					mockLock, nil).Once()
				mockRedis.On("TryLock", mock.Anything, fmt.Sprintf("%s_%s", SettlementProcessingRedisLock, "test.csv"), mock.Anything).Return(
					mockLock, nil).Once()
				mockRedis.On("TryLock", mock.Anything, fmt.Sprintf("%s_%s", SettlementProcessingRedisLock, "test_2.csv"), mock.Anything).Return(
					mockLock, nil).Once()
				mockWf.On("ExecuteFileWorkflow", mock.Anything, mock.Anything).Return(nil).Twice()
				mockSftp.On("ListFiles", mock.Anything, mock.Anything).Return([]string{"test.csv", "test_2.csv"}, nil).Once()
			},
			description: "happy flow",
		},
	}

	for _, scenario := range scenarios {
		testcase := scenario
		description := testcase.description
		cache.RedisClient = mockRedis

		workerConfig := &config.WorkerCfg{
			CronExpression: []string{
				"45 18 * * *",
			},
			BlackoutDay: map[string]string{
				"2022-10-24": "Deepavali",
				"2022-12-26": "Christmas observed",
			},
		}

		w := SettlementWorker{
			SettlementCfg:          workerConfig,
			FileProcessingWorkflow: mockWf,
			SftpClient:             mockSftp,
			FeatureFlags:           &config.FeatureFlags{EnableClearingLongLockDuration: false},
		}
		testcase.mockFunc()

		ctx := context.Background()
		err := w.executor(ctx)
		t.Run(testcase.description, func(t *testing.T) {
			if scenario.expectedErr != nil {
				assert.Error(t, err, description)
				assert.Equal(t, testcase.expectedErr, err, description)
			} else {
				assert.NoError(t, err, description)
			}
		})
	}
}

func setDefaultConfig() {
	configFile, _ := os.CreateTemp("", "config")
	os.Setenv("SERVICE_CONF", configFile.Name())
	configFile.WriteString("{\"featureFlags\":{\"enableWorker\":true,\"enableActivationCodeCheck\":true},\"timeZone\":\"Asia/Singapore\"}")
	secretsDir, _ := os.MkdirTemp("", "secret")
	os.Setenv("SECRET_CONF", secretsDir)
}

func TestLockWorkerProcess(t *testing.T) {
	setDefaultConfig()
	timeZone, _ := time.LoadLocation(constant.TimeZoneMalaysia)
	scenarios := []struct {
		enableLongLockFeature bool
		currentTime           time.Time
		expectedLockDuration  time.Duration
		expectedLockKey       string
		description           string
	}{
		{
			enableLongLockFeature: true,
			currentTime:           time.Date(2024, 1, 1, 0, 0, 0, 0, timeZone),
			expectedLockDuration:  ClearingFileWorkerLockDurationInSeconds * time.Second,
			expectedLockKey:       "SettlementWorkerLock_20240101",
			description:           "enable long lock with 24 hours to midnight should lock file for 8 hours",
		},
		{
			enableLongLockFeature: true,
			currentTime:           time.Date(2024, 1, 1, 23, 0, 0, 0, timeZone),
			expectedLockDuration:  ClearingFileWorkerLockDurationInSeconds * time.Second,
			expectedLockKey:       "SettlementWorkerLock_20240101",
			description:           "enable long lock with 1 hour to midnight should still lock file for 8 hours",
		},
		{
			enableLongLockFeature: false,
			currentTime:           time.Date(2024, 1, 1, 0, 0, 0, 0, timeZone),
			expectedLockDuration:  RedisLockDurationInSeconds * time.Second,
			expectedLockKey:       "SettlementWorkerLock",
			description:           "disable long lock with 24 hours to midnight should only lock file for 60s",
		},
		{
			enableLongLockFeature: false,
			currentTime:           time.Date(2024, 1, 1, 23, 0, 0, 0, timeZone),
			expectedLockDuration:  RedisLockDurationInSeconds * time.Second,
			expectedLockKey:       "SettlementWorkerLock",
			description:           "disable long lock with 1 hour to midnight should still lock file for 60s",
		},
	}

	mockWf := &file.MockWorkflow{}
	mockSftp := &sftputils.MockClient{}
	mockRedis := &mocks.Client{}

	for _, scenario := range scenarios {
		testcase := scenario
		description := testcase.description

		t.Run(description, func(t *testing.T) {
			cache.RedisClient = mockRedis
			stats.StatsDClient = statsd.NewNoop()

			logic.Now = func() time.Time {
				return testcase.currentTime
			}
			mockWorkerLock := &mocks.Lock{}
			mockRedis.On("TryLock", mock.Anything, testcase.expectedLockKey, testcase.expectedLockDuration).Return(mockWorkerLock, nil).Once()
			mockWorkerLock.On("Unlock", mock.Anything).Return(nil).Once()
			mockSftp.On("ListFiles", mock.Anything, mock.Anything).Return(nil, nil).Once()

			workerConfig := &config.WorkerCfg{
				CronExpression: []string{"* * * * *"},
				BlackoutDay:    map[string]string{},
			}
			w := SettlementWorker{
				SettlementCfg:          workerConfig,
				FileProcessingWorkflow: mockWf,
				SftpClient:             mockSftp,
				FeatureFlags:           &config.FeatureFlags{EnableClearingLongLockDuration: testcase.enableLongLockFeature},
			}

			ctx := context.Background()
			err := w.executor(ctx)
			assert.NoError(t, err)
		})
	}
}

func TestLockIndividualFile(t *testing.T) {
	timeZone, _ := time.LoadLocation(constant.TimeZoneMalaysia)
	today := "20240101"
	scenarios := []struct {
		enableLongLockFeature bool
		currentTime           time.Time
		expectedLockDuration  time.Duration
		expectedLockKey       string
		description           string
	}{
		{
			enableLongLockFeature: true,
			currentTime:           time.Date(2024, 1, 1, 0, 0, 0, 0, timeZone),
			expectedLockDuration:  24 * time.Hour,
			expectedLockKey:       "SettlementProcessingRedisLock_clearing_file.csv_20240101",
			description:           "enable long lock with 24 hours to midnight should lock file for 24 hours",
		},
		{
			enableLongLockFeature: true,
			currentTime:           time.Date(2024, 1, 1, 23, 0, 0, 0, timeZone),
			expectedLockDuration:  24 * time.Hour,
			expectedLockKey:       "SettlementProcessingRedisLock_clearing_file.csv_20240101",
			description:           "enable long lock with 1 hour to midnight should still lock file for 24 hours",
		},
		{
			enableLongLockFeature: false,
			currentTime:           time.Date(2024, 1, 1, 0, 0, 0, 0, timeZone),
			expectedLockDuration:  SettlementProcessingRedisLockDurationInSeconds * time.Second,
			expectedLockKey:       "SettlementProcessingRedisLock_clearing_file.csv",
			description:           "disable long lock with 24 hours to midnight should only lock file for 6 hours",
		},
		{
			enableLongLockFeature: false,
			currentTime:           time.Date(2024, 1, 1, 23, 0, 0, 0, timeZone),
			expectedLockDuration:  SettlementProcessingRedisLockDurationInSeconds * time.Second,
			expectedLockKey:       "SettlementProcessingRedisLock_clearing_file.csv",
			description:           "disable long lock with 1 hour to midnight should still lock file for 6 hours",
		},
	}

	mockWf := &file.MockWorkflow{}
	mockSftp := &sftputils.MockClient{}
	mockRedis := &mocks.Client{}
	fileName := "clearing_file.csv"

	for _, scenario := range scenarios {
		testcase := scenario
		description := testcase.description

		t.Run(description, func(t *testing.T) {
			cache.RedisClient = mockRedis

			workerConfig := &config.WorkerCfg{
				CronExpression: []string{"* * * * *"},
				BlackoutDay:    map[string]string{},
			}

			logic.Now = func() time.Time {
				return testcase.currentTime
			}
			mockFileLock := &mocks.Lock{}
			mockRedis.On("TryLock", mock.Anything, testcase.expectedLockKey, testcase.expectedLockDuration).Return(mockFileLock, nil).Once()

			w := SettlementWorker{
				SettlementCfg:          workerConfig,
				FileProcessingWorkflow: mockWf,
				SftpClient:             mockSftp,
				FeatureFlags:           &config.FeatureFlags{EnableClearingLongLockDuration: testcase.enableLongLockFeature},
			}
			ctx := context.Background()
			result, _ := w.canProcessClearingFile(ctx, fileName, today)

			assert.True(t, result, "canProcessClearingFile")
		})
	}
}
