package worker

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/transactiondetails"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/cache"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/util/parallel/gconcurrent"
)

const resumeClearingRefundTag = "RESUME_CLEARING_REFUND"

// ResumeClearingRefundWorker ...
type ResumeClearingRefundWorker struct {
	TxnDetailsClient        transactiondetails.TransactionDetailClient `inject:"client.txnDetails"`
	ResumeClearingRefundCfg *config.WorkerCfg                          `inject:"config.resumeClearingRefundWorker"`
}

func (w *ResumeClearingRefundWorker) schedule(ctx context.Context) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logTag, resumeClearingRefundTag))

	cron := w.ResumeClearingRefundCfg.CronExpression
	for _, cronExp := range cron {
		if _, err := scheduler.Cron(cronExp).Do(resumeClearingRefundWorkerJob, ctx, w); err != nil {
			slog.FromContext(ctx).Fatal(logTag, "failed to schedule clearing resume refund worker")
		}
		slog.FromContext(ctx).Info(logTag, fmt.Sprintf("scheduled clearing resume refund worker, cronExp: %s", cronExp))
	}
}

var resumeClearingRefundWorkerJob = func(ctx context.Context, w *ResumeClearingRefundWorker) error {
	if err := w.executor(ctx); err != nil {
		return err
	}
	return nil
}

func (w *ResumeClearingRefundWorker) executor(ctx context.Context) error { //nolint:dupl
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logTag, resumeClearingRefundTag))

	// obtain redis lock
	lock, err := cache.RedisClient.TryLock(ctx, ResumeClearingRefundWorkerRedisLockPrefix, time.Duration(RedisLockDurationInSeconds)*time.Second)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("not able to obtain lock for worker: %s, skipping", ResumeClearingRefundWorkerRedisLockPrefix), slog.Error(err))
		return nil // not returning error as only one pod is expected to acquire the lock
	}
	defer releaseLock(ctx, lock)

	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("successfully obtain lock for worker: %s", ResumeClearingRefundWorkerRedisLockPrefix))
	result, err := getClearingFailureTransactions(ctx)
	if result == nil {
		// no clearing failure refund found.
		slog.FromContext(ctx).Info(logTag, "resume clearing refund worker done.")
		return nil
	}
	if err != nil {
		return err
	}
	triggerResumeRefundFunc := func(failedRefund *storage.ClearingFailure) func(ctx context.Context) error {
		return func(ctx context.Context) error {
			ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logTag, resumeClearingRefundTag), slog.CustomTag("retrievalReferenceNumber", failedRefund.RetrievalReferenceNumber))
			slog.FromContext(ctx).Info(logTag, fmt.Sprintf("Resume refund: %v\n", failedRefund.ID))
			resumeFailedClearingErr := w.TxnDetailsClient.ResumeFailedClearing(ctx, &api.ResumeFailedClearingRequest{
				Id: failedRefund.ID,
			})
			if resumeFailedClearingErr != nil {
				slog.FromContext(ctx).Error(logTag, "resume refund error", slog.Error(err))
			}
			return resumeFailedClearingErr
		}
	}

	for _, failedRefund := range result {
		gconcurrent.Go(context.Background(), logTag, triggerResumeRefundFunc(failedRefund))
		time.Sleep(time.Millisecond * 500)
	}

	return nil
}

func getClearingFailureTransactions(ctx context.Context) ([]*storage.ClearingFailure, error) {
	var configReader, _ = logic.LoadFeatureConfig()
	timeZoneSetting := constant.TimeZoneMalaysia
	if configReader.TimeZone != "" {
		timeZoneSetting = configReader.TimeZone
	}
	timeZone, _ := time.LoadLocation(timeZoneSetting)
	today := time.Now().In(timeZone)
	year, month, day := today.Date()
	cutOffTime := time.Date(year, month, day, 4, 30, 0, 0, timeZone)
	endOfDay := time.Date(year, month, day+1, 0, 0, 0, 0, timeZone)
	query := []data.Condition{
		data.EqualTo("Type", constant.Refund),
		data.EqualTo("Status", "FAILED"),
		data.GreaterThanOrEqualTo("CreatedAt", cutOffTime),
		data.LessThan("CreatedAt", endOfDay),
	}
	result, err := storage.ClearingFailureD.FindOnSlave(ctx, query...)
	if err != nil {
		if errors.Is(err, data.ErrNoData) {
			slog.FromContext(ctx).Info(logTag, "no clearing refund failure that need to be resumed.")
			return nil, nil
		}
		slog.FromContext(ctx).Error(logTag, fmt.Sprintf("error while getting clearing refund failure. %v", err))
		return nil, err
	}

	return result, nil
}
