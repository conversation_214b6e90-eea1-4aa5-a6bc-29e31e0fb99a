package paynetworker

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/filepaynet"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/cache"

	"github.com/google/uuid"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

const fetchOnlyFromS3 = "FETCH_ONLY_S3"

// FetchOnlyS3Worker ...
type FetchOnlyS3Worker struct {
	FetchOnlyS3Cfg         []config.FetchOnlyS3Cfg `inject:"config.paynetFetchOnlyS3"`
	FileProcessingWorkflow filepaynet.Workflow     `inject:"paynetFileProcessingWorkflow"`
}

func (w *FetchOnlyS3Worker) schedule(ctx context.Context) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logTag, fetchOnlyFromS3))
	for _, cfg := range w.FetchOnlyS3Cfg {
		cron := cfg.CronExpression
		for _, cronExp := range cron {
			if _, err := scheduler.Cron(cronExp).Do(fetchOnlyS3Job, ctx, w, cfg); err != nil {
				slog.FromContext(ctx).Fatal(logTag, fmt.Sprintf("failed to schedule Paynet fetch only worker for type %s", cfg.FilePrefix))
			}
			slog.FromContext(ctx).Info(logTag, fmt.Sprintf("scheduled Paynet fetch s3 only cronExp: %s", cronExp))
		}
	}
}

var fetchOnlyS3Job = func(ctx context.Context, w *FetchOnlyS3Worker, fetchOnlyS3Config config.FetchOnlyS3Cfg) error {
	if err := w.executor(ctx, fetchOnlyS3Config); err != nil {
		return err
	}
	return nil
}

func (w *FetchOnlyS3Worker) executor(ctx context.Context, fetchOnlyS3Config config.FetchOnlyS3Cfg) error {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logTag, constant.FetchOnlyS3))
	lockKey := fmt.Sprintf("%s_%s_%s", FetchOnlyS3WorkerRedisLockPrefix, fetchOnlyS3Config.FilePrefix, Lock)
	lock, err := cache.RedisClient.TryLock(ctx, lockKey, time.Duration(RedisLockDurationInSeconds)*time.Second)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("not able to obtain lock for worker: %s, skipping", lockKey), slog.Error(err))
		return nil // not returning error as only one pod is expected to acquire the lock
	}
	defer releaseLock(ctx, lock)
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("successfully obtain lock for worker: %s", lockKey))
	req := &dto.FileRequest{
		Type:            constant.FetchOnlyS3,
		FileName:        common.GenerateFetchOnlyFileName(fetchOnlyS3Config.FilePrefix, fetchOnlyS3Config.FileExt, fetchOnlyS3Config.FileNameFormat),
		IdempotencyKey:  uuid.NewString(),
		ENSFTPDirectory: fetchOnlyS3Config.ENSFTPDirectory,
		S3Directory:     fetchOnlyS3Config.S3Directory,
	}
	if err := w.FileProcessingWorkflow.ExecutePaynetFileWorkflow(ctx, req); err != nil {
		return err
	}
	return nil
}
