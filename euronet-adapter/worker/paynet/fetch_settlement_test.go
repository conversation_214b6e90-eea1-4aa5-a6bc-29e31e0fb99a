package paynetworker

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/sftputils"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/filepaynet"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/utils/cache"
	"gitlab.myteksi.net/dakota/common/redis/mocks"
)

func TestFetchSettlementWorker(t *testing.T) {
	errDummy := errors.New("simulate error")

	scenarios := []struct {
		errLock     error
		expectedErr error
		errBlackout bool
		errExecute  error
	}{
		{
			errLock:     errDummy,
			expectedErr: nil,
		},
		{
			errBlackout: true,
			errLock:     nil,
			expectedErr: nil,
		},
		{
			errExecute:  errDummy,
			expectedErr: errDummy,
		},
		{
			errLock:     nil,
			expectedErr: nil,
		},
	}

	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario
		mockRedis := &mocks.Client{}
		mockLock := &mocks.Lock{}
		mockWf := &filepaynet.MockWorkflow{}
		mockSftp := &sftputils.MockClient{}

		mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(
			mockLock, testcase.errLock).Once()
		mockWf.On("ExecutePaynetFileWorkflow", mock.Anything, mock.Anything).Return(testcase.errExecute)
		mockLock.On("Unlock", mock.Anything).Return(nil).Once()

		ret := []string{"prefixabc"}
		mockSftp.On("ListFiles", mock.Anything, mock.Anything).Return(ret, nil)

		cache.RedisClient = mockRedis

		workerConfig := config.FetchPaynetSettlementCfg{
			FilePrefixes: []string{"prefix"},
			CronExpression: []string{
				"15 10 * * *",
			},
		}

		w := FetchSettlementWorker{
			SettlementCfg:          workerConfig,
			FileProcessingWorkflow: mockWf,
			SftpClient:             mockSftp,
		}

		ctx := context.Background()
		err := w.executor(ctx)

		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, testcase.expectedErr, err, description)
		} else {
			assert.NoError(t, err, description)
		}
	}
}
