package sftputils

import "context"

// Client ..
type Client interface {
	// Download ..
	Download(context.Context, string) ([]byte, error)
	// DownloadAndLock ..
	DownloadAndLock(context.Context, string) ([]byte, error)
	// Upload ..
	Upload(context.Context, []byte, string) error
	// ListFiles ..
	ListFiles(context.Context, string) ([]string, error)
}

//go:generate mockery --name Client --inpackage --case=underscore
