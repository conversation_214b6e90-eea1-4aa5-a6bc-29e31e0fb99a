-- Deploy euronet_adapter:0014-add-product-variant-cardtransaction-clearingrecord to mysql

ALTER TABLE card_transaction
    ADD column `product_variant`
            VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'card product variant linked to account' AFTER `internal_transaction_id`;

ALTER TABLE clearing_record
    ADD column `product_variant`
            VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'card product variant linked to account' AFTER `line_number`;

ALTER TABLE user_card_mapping
    ADD column `product_variant`
            VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'card product variant linked to account' AFTER `internal_card_id`;

ALTER TABLE refund
    ADD column `product_variant`
            VARCHAR(64) NOT NULL DEFAULT '' COMMENT 'card product variant linked to account' AFTER `proxy_number`;
