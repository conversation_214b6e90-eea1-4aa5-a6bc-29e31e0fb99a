package data

import (
	"context"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
)

type ICanFilter[T any] interface {
	Filter(ctx context.Context, logTag string, input []*T) ([]*T, error)
}

//go:generate mockery --name ICanFilterCardTransaction --inpackage --case=underscore
type ICanFilterCardTransaction interface {
	Filter(ctx context.Context, logTag string, input []*storage.CardTransaction) ([]*storage.CardTransaction, error)
}
