package data

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"testing"
	"time"
)

func TestPaginatedFinder(t *testing.T) {
	mockCardTransactionDAO := &storage.MockICardTransactionDAO{}
	mockFilter := &mockCardTransactionFilter{}
	txn1 := &storage.CardTransaction{ID: 1}
	txn2 := &storage.CardTransaction{ID: 2}
	txn3 := &storage.CardTransaction{ID: 3}
	txn4 := &storage.CardTransaction{ID: 4}
	txn5 := &storage.CardTransaction{ID: 5}
	dummyTransactions := []*storage.CardTransaction{
		txn1, txn2, txn3, txn4, txn5,
	}

	parseUtcTime := func(s string) time.Time {
		v, _ := time.Parse(time.RFC3339, s)
		return v
	}

	scenarios := []struct {
		description    string
		startTime      time.Time
		endTime        time.Time
		window         time.Duration
		findOnSlave    bool
		expectedErr    error
		expectedResult []*storage.CardTransaction
		hasFilter      bool
		mockFunc       func()
	}{
		{
			description:    "findOnSlave happy path",
			startTime:      parseUtcTime("2024-04-01T00:00:00Z"),
			endTime:        parseUtcTime("2024-04-06T00:00:00Z"),
			window:         24 * time.Hour,
			findOnSlave:    true,
			expectedErr:    nil,
			expectedResult: dummyTransactions,
			mockFunc: func() {
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[0:1], nil)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[1:2], nil)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[2:3], nil)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[3:4], nil)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[4:5], nil)
			},
		},
		{
			description:    "find happy path",
			startTime:      parseUtcTime("2024-04-01T00:00:00Z"),
			endTime:        parseUtcTime("2024-04-06T00:00:00Z"),
			window:         24 * time.Hour,
			findOnSlave:    false,
			expectedErr:    nil,
			expectedResult: dummyTransactions,
			mockFunc: func() {
				mockCardTransactionDAO.On("Find", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[0:1], nil)
				mockCardTransactionDAO.On("Find", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[1:2], nil)
				mockCardTransactionDAO.On("Find", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[2:3], nil)
				mockCardTransactionDAO.On("Find", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[3:4], nil)
				mockCardTransactionDAO.On("Find", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[4:5], nil)
			},
		},
		{
			description:    "invalid range will return empty result",
			startTime:      parseUtcTime("2024-04-01T00:00:00Z"),
			endTime:        parseUtcTime("2024-03-01T00:00:00Z"),
			window:         24 * time.Hour,
			findOnSlave:    true,
			expectedErr:    nil,
			expectedResult: []*storage.CardTransaction{},
			mockFunc:       func() {},
		},
		{
			description:    "invalid window will set as 1 day",
			startTime:      parseUtcTime("2024-04-01T00:00:00Z"),
			endTime:        parseUtcTime("2024-04-06T00:00:00Z"),
			window:         0,
			findOnSlave:    true,
			expectedErr:    nil,
			expectedResult: dummyTransactions,
			mockFunc: func() {
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[0:1], nil)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[1:2], nil)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[2:3], nil)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[3:4], nil)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[4:5], nil)
			},
		},
		{
			description:    "max window cover start to end",
			startTime:      parseUtcTime("2024-04-01T00:00:00Z"),
			endTime:        parseUtcTime("2024-04-02T00:00:00Z"),
			window:         48 * time.Hour,
			findOnSlave:    true,
			expectedErr:    nil,
			expectedResult: dummyTransactions,
			mockFunc: func() {
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions, nil)
			},
		},
		{
			description:    "partially no data",
			startTime:      parseUtcTime("2024-04-01T00:00:00Z"),
			endTime:        parseUtcTime("2024-04-06T00:00:00Z"),
			window:         24 * time.Hour,
			findOnSlave:    true,
			expectedErr:    nil,
			expectedResult: dummyTransactions[0:3],
			mockFunc: func() {
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[0:1], nil)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(nil, data.ErrNoData)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(nil, data.ErrNoData)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[1:2], nil)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[2:3], nil)
			},
		},
		{
			description:    "totally no data returns ErrNoData",
			startTime:      parseUtcTime("2024-04-01T00:00:00Z"),
			endTime:        parseUtcTime("2024-04-06T00:00:00Z"),
			window:         24 * time.Hour,
			findOnSlave:    true,
			expectedErr:    data.ErrNoData,
			expectedResult: nil,
			mockFunc: func() {
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(nil, data.ErrNoData)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(nil, data.ErrNoData)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(nil, data.ErrNoData)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(nil, data.ErrNoData)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(nil, data.ErrNoData)
			},
		},
		{
			description:    "other error abort",
			startTime:      parseUtcTime("2024-04-01T00:00:00Z"),
			endTime:        parseUtcTime("2024-04-06T00:00:00Z"),
			window:         24 * time.Hour,
			findOnSlave:    true,
			expectedErr:    fmt.Errorf("dummy error"),
			expectedResult: nil,
			mockFunc: func() {
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions[0:1], nil)
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(nil, fmt.Errorf("dummy error"))
			},
		},
		{
			description:    "filter exclude all",
			startTime:      parseUtcTime("2024-04-01T00:00:00Z"),
			endTime:        parseUtcTime("2024-04-02T00:00:00Z"),
			window:         24 * time.Hour,
			findOnSlave:    true,
			expectedErr:    nil,
			expectedResult: nil,
			hasFilter:      true,
			mockFunc: func() {
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions, nil)

				mockFilter.On("Filter", mock.Anything, mock.Anything, mock.Anything).Once().Return([]*storage.CardTransaction{}, nil)
			},
		},
		{
			description:    "filter include all",
			startTime:      parseUtcTime("2024-04-01T00:00:00Z"),
			endTime:        parseUtcTime("2024-04-02T00:00:00Z"),
			window:         24 * time.Hour,
			findOnSlave:    true,
			expectedErr:    nil,
			expectedResult: dummyTransactions,
			hasFilter:      true,
			mockFunc: func() {
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions, nil)

				mockFilter.On("Filter", mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions, nil)
			},
		},
		{
			description:    "filter include some",
			startTime:      parseUtcTime("2024-04-01T00:00:00Z"),
			endTime:        parseUtcTime("2024-04-02T00:00:00Z"),
			window:         24 * time.Hour,
			findOnSlave:    true,
			expectedErr:    nil,
			expectedResult: []*storage.CardTransaction{txn1, txn3, txn5},
			hasFilter:      true,
			mockFunc: func() {
				mockCardTransactionDAO.On("FindOnSlave", mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).Once().Return(dummyTransactions, nil)

				mockFilter.On("Filter", mock.Anything, mock.Anything, mock.Anything).Once().Return([]*storage.CardTransaction{txn1, txn3, txn5}, nil)
			},
		},
	}

	for _, scenario := range scenarios {
		testcase := scenario
		description := testcase.description

		t.Run(description, func(t *testing.T) {
			testcase.mockFunc()
			ctx := context.Background()
			var filter ICanFilter[storage.CardTransaction]
			if testcase.hasFilter {
				filter = mockFilter
			}
			finder := PaginatedFinder[storage.CardTransaction]{
				Finder:      mockCardTransactionDAO,
				Filter:      filter,
				StartAt:     testcase.startTime,
				EndAt:       testcase.endTime,
				Window:      testcase.window,
				FindOnSlave: testcase.findOnSlave,
			}
			conditions := []data.Condition{
				data.EqualTo("Field1", "value 1"),
				data.ContainedIn("Field2", []string{"value 2.1", "value 2.2"}),
			}
			result, err := finder.FindInTimeFrame(ctx, "TEST-FindInTimeFrame", conditions...)
			if testcase.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, testcase.expectedErr, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, testcase.expectedResult, result)
			}
		})
	}
}
