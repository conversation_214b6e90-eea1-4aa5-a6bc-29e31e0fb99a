// Package stats ...
package stats

import (
	"gitlab.myteksi.net/dakota/common/tracing"
	"gitlab.myteksi.net/dakota/servus/v2"
	servusStatsD "gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var (
	// StatsDClient defines the stats client
	StatsDClient servusStatsD.Client

	// TracerClient defines the tracer client
	TracerClient tracing.Tracer
)

// Init initialize the clients required for stats and tracing for logging purposes
func Init(app *servus.Application) {
	StatsDClient = app.GetStatsD()
	TracerClient = app.GetTracer()
}
