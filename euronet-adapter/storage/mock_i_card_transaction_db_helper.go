// Code generated by mockery v2.44.1. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/gophers/go/commons/data"
)

// MockICardTransactionDBHelper is an autogenerated mock type for the ICardTransactionDBHelper type
type MockICardTransactionDBHelper struct {
	mock.Mock
}

// FindFirst provides a mock function with given fields: ctx, conditions
func (_m *MockICardTransactionDBHelper) FindFirst(ctx context.Context, conditions ...data.Condition) (*CardTransaction, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FindFirst")
	}

	var r0 *CardTransaction
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) (*CardTransaction, error)); ok {
		return rf(ctx, conditions...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) *CardTransaction); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CardTransaction)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindFirstOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockICardTransactionDBHelper) FindFirstOnSlave(ctx context.Context, conditions ...data.Condition) (*CardTransaction, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FindFirstOnSlave")
	}

	var r0 *CardTransaction
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) (*CardTransaction, error)); ok {
		return rf(ctx, conditions...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) *CardTransaction); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CardTransaction)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDAO provides a mock function with given fields:
func (_m *MockICardTransactionDBHelper) GetDAO() ICardTransactionDAO {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDAO")
	}

	var r0 ICardTransactionDAO
	if rf, ok := ret.Get(0).(func() ICardTransactionDAO); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(ICardTransactionDAO)
		}
	}

	return r0
}

// Save provides a mock function with given fields: ctx, new
func (_m *MockICardTransactionDBHelper) Save(ctx context.Context, new *CardTransaction) (*CardTransaction, error) {
	ret := _m.Called(ctx, new)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 *CardTransaction
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *CardTransaction) (*CardTransaction, error)); ok {
		return rf(ctx, new)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *CardTransaction) *CardTransaction); ok {
		r0 = rf(ctx, new)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CardTransaction)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *CardTransaction) error); ok {
		r1 = rf(ctx, new)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateEntity provides a mock function with given fields: ctx, from, to
func (_m *MockICardTransactionDBHelper) UpdateEntity(ctx context.Context, from *CardTransaction, to *CardTransaction) (*CardTransaction, error) {
	ret := _m.Called(ctx, from, to)

	if len(ret) == 0 {
		panic("no return value specified for UpdateEntity")
	}

	var r0 *CardTransaction
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *CardTransaction, *CardTransaction) (*CardTransaction, error)); ok {
		return rf(ctx, from, to)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *CardTransaction, *CardTransaction) *CardTransaction); ok {
		r0 = rf(ctx, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*CardTransaction)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *CardTransaction, *CardTransaction) error); ok {
		r1 = rf(ctx, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockICardTransactionDBHelper creates a new instance of MockICardTransactionDBHelper. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockICardTransactionDBHelper(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockICardTransactionDBHelper {
	mock := &MockICardTransactionDBHelper{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
