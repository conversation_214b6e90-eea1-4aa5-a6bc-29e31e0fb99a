package storage

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/stretchr/testify/mock"
	"gitlab.myteksi.net/dakota/servus/v2/data"
)

func Test_clearingRecordDBHelperImpl_FindFirst(t *testing.T) {
	ctx := context.Background()
	type fields struct {
		ClearingRecordDAO IClearingRecordDAO
	}
	type args struct {
		ctx        context.Context
		conditions []data.Condition
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ClearingRecord
		wantErr bool
	}{
		{
			name: "happy path",
			fields: fields{
				ClearingRecordDAO: func() IClearingRecordDAO {
					mc := MockIClearingRecordDAO{}
					mc.On("Find", mock.Anything, mock.Anything).Return([]*ClearingRecord{{}}, nil).Once()
					return &mc
				}(),
			},
			args: args{
				ctx: ctx,
			},
			want:    &ClearingRecord{},
			wantErr: false,
		},
		{
			name: "error path - no data",
			fields: fields{
				ClearingRecordDAO: func() IClearingRecordDAO {
					mc := MockIClearingRecordDAO{}
					mc.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
					return &mc
				}(),
			},
			args: args{
				ctx: ctx,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error path - err",
			fields: fields{
				ClearingRecordDAO: func() IClearingRecordDAO {
					mc := MockIClearingRecordDAO{}
					mc.On("Find", mock.Anything, mock.Anything).Return(nil, errors.New("some err")).Once()
					return &mc
				}(),
			},
			args: args{
				ctx: ctx,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			i := &clearingRecordDBHelperImpl{
				ClearingRecordDAO: tt.fields.ClearingRecordDAO,
			}
			got, err := i.FindFirst(tt.args.ctx, tt.args.conditions...)
			if (err != nil) != tt.wantErr {
				t.Errorf("FindFirst() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FindFirst() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_clearingRecordDBHelperImpl_Save(t *testing.T) {
	ctx := context.Background()
	type fields struct {
		ClearingRecordDAO IClearingRecordDAO
	}
	type args struct {
		ctx context.Context
		new *ClearingRecord
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ClearingRecord
		wantErr bool
	}{
		{
			name: "happy path",
			fields: fields{
				ClearingRecordDAO: func() IClearingRecordDAO {
					mc := MockIClearingRecordDAO{}
					mc.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					return &mc
				}(),
			},
			args: args{
				ctx: ctx,
				new: &ClearingRecord{},
			},
			want:    &ClearingRecord{},
			wantErr: false,
		},
		{
			name: "error path - err",
			fields: fields{
				ClearingRecordDAO: func() IClearingRecordDAO {
					mc := MockIClearingRecordDAO{}
					mc.On("Save", mock.Anything, mock.Anything).Return(errors.New("some err")).Once()
					return &mc
				}(),
			},
			args: args{
				ctx: ctx,
				new: &ClearingRecord{},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			i := &clearingRecordDBHelperImpl{
				ClearingRecordDAO: tt.fields.ClearingRecordDAO,
			}
			got, err := i.Save(tt.args.ctx, tt.args.new)
			if (err != nil) != tt.wantErr {
				t.Errorf("Save() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				tt.want.CreatedAt = got.CreatedAt
				tt.want.UpdatedAt = got.UpdatedAt
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Save() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_clearingRecordDBHelperImpl_SaveBatch(t *testing.T) {
	ctx := context.Background()
	type fields struct {
		ClearingRecordDAO IClearingRecordDAO
	}
	type args struct {
		ctx             context.Context
		newDataEntities []*ClearingRecord
		batchSize       int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "happy path",
			fields: fields{
				ClearingRecordDAO: func() IClearingRecordDAO {
					mc := MockIClearingRecordDAO{}
					mc.On("SaveBatch", mock.Anything, mock.Anything).Return(nil).Once()
					return &mc
				}(),
			},
			args: args{
				ctx: ctx,
				newDataEntities: []*ClearingRecord{
					{}, {}, {},
				},
				batchSize: 2,
			},
			wantErr: false,
		},
		{
			name: "error path - SaveBatch error",
			fields: fields{
				ClearingRecordDAO: func() IClearingRecordDAO {
					mc := MockIClearingRecordDAO{}
					mc.On("SaveBatch", mock.Anything, mock.Anything).Return(errors.New("batch save error")).Once()
					return &mc
				}(),
			},
			args: args{
				ctx: ctx,
				newDataEntities: []*ClearingRecord{
					{}, {}, {},
				},
				batchSize: 2,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			i := &clearingRecordDBHelperImpl{
				ClearingRecordDAO: tt.fields.ClearingRecordDAO,
			}
			err := i.SaveBatch(tt.args.ctx, tt.args.newDataEntities)
			if (err != nil) != tt.wantErr {
				t.Errorf("SaveBatch() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			for _, entity := range tt.args.newDataEntities {
				if entity.CreatedAt.IsZero() || entity.UpdatedAt.IsZero() {
					t.Errorf("SaveBatch() timestamps not set properly")
				}
			}
		})
	}
}

func Test_clearingRecordDBHelperImpl_UpdateEntity(t *testing.T) {
	ctx := context.Background()
	type fields struct {
		ClearingRecordDAO IClearingRecordDAO
	}
	type args struct {
		ctx  context.Context
		from *ClearingRecord
		to   *ClearingRecord
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ClearingRecord
		wantErr bool
	}{
		{
			name: "happy path",
			fields: fields{
				ClearingRecordDAO: func() IClearingRecordDAO {
					mc := MockIClearingRecordDAO{}
					mc.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
					return &mc
				}(),
			},
			args: args{
				ctx:  ctx,
				from: &ClearingRecord{},
				to:   &ClearingRecord{},
			},
			want:    &ClearingRecord{},
			wantErr: false,
		},
		{
			name: "error path - err",
			fields: fields{
				ClearingRecordDAO: func() IClearingRecordDAO {
					mc := MockIClearingRecordDAO{}
					mc.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("some err")).Once()
					return &mc
				}(),
			},
			args: args{
				ctx:  ctx,
				from: &ClearingRecord{},
				to:   &ClearingRecord{},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			i := &clearingRecordDBHelperImpl{
				ClearingRecordDAO: tt.fields.ClearingRecordDAO,
			}
			got, err := i.UpdateEntity(tt.args.ctx, tt.args.from, tt.args.to)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateEntity() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				tt.want.UpdatedAt = got.UpdatedAt
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateEntity() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_clearingRecordDBHelperImpl_FindFirstOnSlave(t *testing.T) {
	ctx := context.Background()
	type fields struct {
		ClearingRecordDAO IClearingRecordDAO
	}
	type args struct {
		ctx        context.Context
		conditions []data.Condition
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ClearingRecord
		wantErr bool
	}{
		{
			name: "happy path",
			fields: fields{
				ClearingRecordDAO: func() IClearingRecordDAO {
					mc := MockIClearingRecordDAO{}
					mc.On("FindOnSlave", mock.Anything, mock.Anything).Return([]*ClearingRecord{{}}, nil).Once()
					return &mc
				}(),
			},
			args: args{
				ctx:        ctx,
				conditions: []data.Condition{},
			},
			want:    &ClearingRecord{},
			wantErr: false,
		},
		{
			name: "error path - no data",
			fields: fields{
				ClearingRecordDAO: func() IClearingRecordDAO {
					mc := MockIClearingRecordDAO{}
					mc.On("FindOnSlave", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
					return &mc
				}(),
			},
			args: args{
				ctx: ctx,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "error path - err",
			fields: fields{
				ClearingRecordDAO: func() IClearingRecordDAO {
					mc := MockIClearingRecordDAO{}
					mc.On("FindOnSlave", mock.Anything, mock.Anything).Return(nil, errors.New("some err")).Once()
					return &mc
				}(),
			},
			args: args{
				ctx: ctx,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			i := &clearingRecordDBHelperImpl{
				ClearingRecordDAO: tt.fields.ClearingRecordDAO,
			}
			got, err := i.FindFirstOnSlave(tt.args.ctx, tt.args.conditions...)
			if (err != nil) != tt.wantErr {
				t.Errorf("FindFirstOnSlave() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FindFirstOnSlave() got = %v, want %v", got, tt.want)
			}
		})
	}
}
