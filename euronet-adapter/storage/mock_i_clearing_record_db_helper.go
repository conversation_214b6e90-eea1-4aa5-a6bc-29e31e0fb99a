// Code generated by mockery v2.46.0. DO NOT EDIT.

package storage

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
	data "gitlab.myteksi.net/gophers/go/commons/data"
)

// MockIClearingRecordDBHelper is an autogenerated mock type for the IClearingRecordDBHelper type
type MockIClearingRecordDBHelper struct {
	mock.Mock
}

// Clone provides a mock function with given fields: clearingRecord
func (_m *MockIClearingRecordDBHelper) Clone(clearingRecord *ClearingRecord) *ClearingRecord {
	ret := _m.Called(clearingRecord)

	if len(ret) == 0 {
		panic("no return value specified for <PERSON>lone")
	}

	var r0 *ClearingRecord
	if rf, ok := ret.Get(0).(func(*ClearingRecord) *ClearingRecord); ok {
		r0 = rf(clearingRecord)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ClearingRecord)
		}
	}

	return r0
}

// FindFirst provides a mock function with given fields: ctx, conditions
func (_m *MockIClearingRecordDBHelper) FindFirst(ctx context.Context, conditions ...data.Condition) (*ClearingRecord, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FindFirst")
	}

	var r0 *ClearingRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) (*ClearingRecord, error)); ok {
		return rf(ctx, conditions...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) *ClearingRecord); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ClearingRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindFirstOnSlave provides a mock function with given fields: ctx, conditions
func (_m *MockIClearingRecordDBHelper) FindFirstOnSlave(ctx context.Context, conditions ...data.Condition) (*ClearingRecord, error) {
	_va := make([]interface{}, len(conditions))
	for _i := range conditions {
		_va[_i] = conditions[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FindFirstOnSlave")
	}

	var r0 *ClearingRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) (*ClearingRecord, error)); ok {
		return rf(ctx, conditions...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...data.Condition) *ClearingRecord); ok {
		r0 = rf(ctx, conditions...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ClearingRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...data.Condition) error); ok {
		r1 = rf(ctx, conditions...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDAO provides a mock function with given fields:
func (_m *MockIClearingRecordDBHelper) GetDAO() IClearingRecordDAO {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetDAO")
	}

	var r0 IClearingRecordDAO
	if rf, ok := ret.Get(0).(func() IClearingRecordDAO); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(IClearingRecordDAO)
		}
	}

	return r0
}

// GetSlaveStrategy provides a mock function with given fields: ctx
func (_m *MockIClearingRecordDBHelper) GetSlaveStrategy(ctx context.Context) (data.DbEntityRelationStrategy, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetSlaveStrategy")
	}

	var r0 data.DbEntityRelationStrategy
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) (data.DbEntityRelationStrategy, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) data.DbEntityRelationStrategy); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(data.DbEntityRelationStrategy)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Save provides a mock function with given fields: ctx, new
func (_m *MockIClearingRecordDBHelper) Save(ctx context.Context, new *ClearingRecord) (*ClearingRecord, error) {
	ret := _m.Called(ctx, new)

	if len(ret) == 0 {
		panic("no return value specified for Save")
	}

	var r0 *ClearingRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ClearingRecord) (*ClearingRecord, error)); ok {
		return rf(ctx, new)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ClearingRecord) *ClearingRecord); ok {
		r0 = rf(ctx, new)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ClearingRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ClearingRecord) error); ok {
		r1 = rf(ctx, new)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SaveBatch provides a mock function with given fields: ctx, new
func (_m *MockIClearingRecordDBHelper) SaveBatch(ctx context.Context, new []*ClearingRecord) error {
	ret := _m.Called(ctx, new)

	if len(ret) == 0 {
		panic("no return value specified for SaveBatch")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*ClearingRecord) error); ok {
		r0 = rf(ctx, new)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateEntity provides a mock function with given fields: ctx, from, to
func (_m *MockIClearingRecordDBHelper) UpdateEntity(ctx context.Context, from *ClearingRecord, to *ClearingRecord) (*ClearingRecord, error) {
	ret := _m.Called(ctx, from, to)

	if len(ret) == 0 {
		panic("no return value specified for UpdateEntity")
	}

	var r0 *ClearingRecord
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ClearingRecord, *ClearingRecord) (*ClearingRecord, error)); ok {
		return rf(ctx, from, to)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ClearingRecord, *ClearingRecord) *ClearingRecord); ok {
		r0 = rf(ctx, from, to)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*ClearingRecord)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ClearingRecord, *ClearingRecord) error); ok {
		r1 = rf(ctx, from, to)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockIClearingRecordDBHelper creates a new instance of MockIClearingRecordDBHelper. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockIClearingRecordDBHelper(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockIClearingRecordDBHelper {
	mock := &MockIClearingRecordDBHelper{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
