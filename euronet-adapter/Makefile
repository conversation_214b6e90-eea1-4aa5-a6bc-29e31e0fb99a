# for local build, setup environment variables from .env
include .env

.PHONY: ensure-mod lint install-tools test run-local

install-tools:
	@echo ">  Installing tools..."
	(cd /tmp && go get -u github.com/golangci/golangci-lint/cmd/golangci-lint)

lint:
	@echo ">  Checking codes..."
	golangci-lint run --out-format code-climate | jq -r '.[] | "\(.location.path):\(.location.lines.begin) \(.description)"'

test:
	@echo ">  Running tests..."
	go test -cover -race -v -count=1 ./...

run-local: ensure-mod
	cd cmd/euronet-adapter && go run main.go

ensure-mod:
	go mod download
	go mod vendor