package dto

// TransactionType is the type of transaction
type TransactionType string

// TransactionSubType is the sub-type of transaction
type TransactionSubType string

// CaptureMethod is the type of capture (automatic/manual)
type CaptureMethod string

// MessageIdentifier is the type for message identification
type MessageIdentifier string

// PANEntryMode is the type for pan entry mode
type <PERSON>NEntryMode string

// ServiceName is the type for service name
type ServiceName string

const (
	// Authorize represents type for authorize transaction
	Authorize TransactionType = "AUTHORIZE"

	// Reversal represents type for reversal transaction
	Reversal TransactionType = "REVERSAL"

	// BalanceInquiry represents type for balance inquiry transaction
	BalanceInquiry TransactionType = "BALANCE_INQUIRY"

	// Advice represents type for advice transaction
	Advice TransactionType = "ADVICE"

	// SaleCompletion transaction type
	SaleCompletion TransactionType = "SALE_COMPLETION"

	// SaleCompletionRepeat transaction type
	SaleCompletionRepeat TransactionType = "SALE_COMPLETION_REPEAT"
)

const (
	// POSPurchaseSwipe represents sub-type for Pos Purchase (Swipe) transaction
	POSPurchaseSwipe TransactionSubType = "POS_PURCHASE_SWIPE"
)

const (
	// Automatic is to indicate that the transaction is straight-through without explicit capture
	Automatic CaptureMethod = "AUTOMATIC"

	// Manual is to indicate that the transaction requires explicit capture call
	Manual CaptureMethod = "MANUAL"
)

const (
	// AuthMTI defines the MTI for auth transactions like ECOM, POS
	AuthMTI MessageIdentifier = "0100"

	// FinancialTxnMTI defines the MTI for financial transactions like ATM withdrawal, check balance
	FinancialTxnMTI MessageIdentifier = "0200"

	// PaynetSaleCompletionMTI defines the MTI for sale completion
	PaynetSaleCompletionMTI MessageIdentifier = "0120"

	// PaynetSaleCompletionRepeatMTI defines the MTI for sale completion
	PaynetSaleCompletionRepeatMTI MessageIdentifier = "0121"
)

const (
	// Unknown defines PAN entry mode to be unknown
	Unknown PANEntryMode = "00"

	// ManualEntryMode defines manual entry mode
	ManualEntryMode PANEntryMode = "01"

	// MagneticStripeRead defines magnetic stripe entry mode
	MagneticStripeRead PANEntryMode = "02"

	// BarCodeReader defines barcode reader entry mode
	BarCodeReader PANEntryMode = "03"

	// OCR defines optical character reader entry mode
	OCR PANEntryMode = "04"

	// Chip defines auto-entry via chip entry mode
	Chip PANEntryMode = "05"

	// ContactlessChipCard defines contactless chip card entry mode
	ContactlessChipCard PANEntryMode = "07"

	// ECOM defines electronic commerce entry mode
	ECOM PANEntryMode = "09"

	// COF defines credential on file entry mode
	COF PANEntryMode = "10"

	// HYBRID defines hybrid terminal entry mode
	HYBRID PANEntryMode = "79"

	// Fallback defines chip-capable terminal defaulted to the magnetic stripe-read PAN entry mode
	Fallback PANEntryMode = "80"

	// OpticalIdentityCheck defines PAN/Token entry via electronic commerce with optional Identity Check-AAV or DSRP cryptogram in UCAF entry mode
	OpticalIdentityCheck PANEntryMode = "81"

	// Server defines auto entry via Server entry mode
	Server PANEntryMode = "82"

	// MagneticStripe defines full and unaltered magnetic stripe read
	MagneticStripe PANEntryMode = "90"

	// ContactlessMagneticStripe defines contactless magnetic stripe entry mode
	ContactlessMagneticStripe PANEntryMode = "91"
)

const (
	// TxnAuth defines service name for auth transaction
	TxnAuth = "TXNAUTH"

	// TxnReversal defines service name for reversal transaction
	TxnReversal = "TXNREVERSAL"

	// TxnReversalSettled ...
	TxnReversalSettled = "TXNREVERSALSETTLED"

	// TxnAdvice defines service name for advice transaction
	TxnAdvice = "TXNADVICE"

	// TxnBalance defines service name for balance transaction
	TxnBalance = "TXNBALANCE"

	// SettlementCapture defines process for transaction capture
	SettlementCapture = "SETTLEMENTCAPTURE"

	// ClearingDirect defines direct transfer from clearing
	ClearingDirect = "CLEARINGDIRECT"

	// TxnSaleCompletion defines service name for sale completion transaction
	TxnSaleCompletion = "TXNCOMPLETE"
)
