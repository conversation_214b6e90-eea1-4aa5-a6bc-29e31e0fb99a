// Package config ...
package config

import (
	"bytes"
	"context"
	"fmt"
	"text/template"

	"gitlab.myteksi.net/dbmy/payment/common/cronmanager"

	"gitlab.myteksi.net/dakota/schemas/streams"

	"gitlab.com/gx-regional/dbmy/digicard/common/utils"

	"github.com/Masterminds/sprig"
	"github.com/myteksi/hystrix-go/hystrix"
	commoncfg "gitlab.com/gx-regional/dbmy/digicard/common/config"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/external/acsclient"
	"gitlab.myteksi.net/dakota/common/redis"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	rsConfig "gitlab.myteksi.net/dbmy/common/retryable-stream/sqs/config"
	sndconfig "gitlab.myteksi.net/snd/streamsdk/kafka/config"
)

const (
	ServiceName = "euronet-adapter"
)

// Loader ...
type Loader struct{}

// AppConfig ...
type AppConfig struct {
	servus.DefaultAppConfig
	Locale *commoncfg.Locale `json:"locale"` //this was added when we worked on clearing processor, it's registered for DI as `config.locale`

	TimeZone                        string                                      `json:"timeZone"` //this is the OG timezone config, it's not registered in DI container, we should eventually replace it with the Locale config
	RedisConfig                     *redis.Config                               `json:"redisCluster"`
	Euronet                         *external.Config                            `json:"euronetConfig"`
	DigicardTxn                     *ServiceConfig                              `json:"digicardTxn"`
	DigicardCore                    *ServiceConfig                              `json:"digicardCore"`
	PaymentCore                     *ServiceConfig                              `json:"paymentCore"`
	IdentityExperience              *ServiceConfig                              `json:"identityExperience"`
	KeyStore                        *KeyStore                                   `json:"keyStore"`
	FeatureFlags                    *FeatureFlags                               `json:"featureFlags"`
	DigicardTransactionKafka        *KafkaConfig                                `json:"digicardTransactionStream"`
	PaymentCoreKafka                *KafkaConfig                                `json:"paymentCoreStream"`
	AutoReversalKafka               *KafkaConfig                                `json:"autoReversalStream"`
	EuronetAdapterS3BucketName      string                                      `json:"euronetAdapterS3BucketName"`
	EuronetAdapterS3ClientDevMode   bool                                        `json:"euronetAdapterS3ClientDevMode"`
	Settlement                      *WorkerCfg                                  `json:"settlement"`
	AdjustmentScenario2             *WorkerCfg                                  `json:"adjustment_scenario_2"`
	CBSDump                         *WorkerCfg                                  `json:"cbs_dump"`
	PaynetCBSDump                   *WorkerCfg                                  `json:"paynet_cbs_dump"`
	AuthExpiry                      *WorkerCfg                                  `json:"auth_expiry"`
	FetchOnly                       []FetchOnlyCfg                              `json:"fetch_only"`
	FetchOnlyV2                     []FetchOnlyV2Cfg                            `json:"fetch_only_v2"`
	PaynetFetchOnly                 []FetchOnlyCfg                              `json:"paynet_fetch_only"`
	PaynetSettlementsFetch          []FetchPaynetSettlementCfg                  `json:"paynet_settlement_fetch_only"`
	FetchOnlyS3                     []FetchOnlyS3Cfg                            `json:"fetch_only_s3"`
	FetchOnlyS3V2                   []FetchOnlyS3V2Cfg                          `json:"fetch_only_s3_v2"`
	PaynetFetchOnlyS3               []FetchOnlyS3Cfg                            `json:"paynet_fetch_only_s3"`
	Sftp                            *Sftp                                       `json:"sftp"`
	AllowTransactionTypes           *AllowTransactionTypes                      `json:"allowTransactionTypes"`
	CardInfoStaticPrivateKey        string                                      `json:"cardInfoStaticPrivateKey"`
	CountryConfig                   CountryConfig                               `json:"countryConfig"`
	TransactionTimeoutInMiliseconds TransactionTimeoutInMiliseconds             `json:"transactionTimeoutInMiliseconds"`
	WorkflowRetryConfig             *WorkflowRetryConfig                        `json:"workflowRetryConfig"`
	JVConfig                        map[string]JVProcessCfg                     `json:"jvConfig"`
	PaynetJVConfig                  map[string]JVProcessCfg                     `json:"paynetJvConfig"`
	AllowListCardProxy              []string                                    `json:"allowListCardProxy"` // for allow list card cbs dump
	DenyListCardProxy               []string                                    `json:"denyListCardProxy"`  // for deny list card cbs dump, mutually exclusive with allow lit
	ACSConfig                       *ACSConfig                                  `json:"acsConfig"`
	Pigeon                          *ServiceConfig                              `json:"pigeon"`
	ACSClientConfig                 *acsclient.Config                           `json:"acsClientConfig"`
	ReleasePreAuthCfg               []ReleasePreAuthCfg                         `json:"releasePreAuthCfg"`
	CustomerMaster                  *ServiceConfig                              `json:"customerMaster"`
	GdProxy                         *ServiceConfig                              `json:"gdProxy"`
	DtRetryStream                   *rsConfig.SQSConfig                         `json:"dtRetryStream"`
	PcRetryStream                   *rsConfig.SQSConfig                         `json:"pcRetryStream"`
	ArRetryStream                   *rsConfig.SQSConfig                         `json:"arRetryStream"`
	ReleaseMastercardAuthCfg        []ReleaseMastercardAuthCfg                  `json:"releaseMastercardAuthCfg"`
	FileProcessingSleepCfg          *FileProcessingSleepCfg                     `json:"fileProcessingSleepCfg"`
	ResumeClearingRefundCfg         *WorkerCfg                                  `json:"resumeClearingRefundWorker"`
	AdminConfig                     *AdminCfg                                   `json:"adminConfig"`
	CronManagerConfig               map[string]cronmanager.CronManagerJobConfig `json:"cronManager"`
	AuthAmountMarkupConfig          []AuthAmountMarkupConfig                    `json:"authAmountMarkupConfig"`
	QueueHandlerSQSConfig           *QueueHandlerSQSConfig                      `json:"queueHandlerSQSConfig"`
	QueueHandlerClearingConfig      *QueueHandlerClearingConfig                 `json:"queueHandlerClearingConfig"` // for allow list card cbs dump
	PreTransactionCheckConfig       *PreTransactionCheckConfig                  `json:"preTransactionCheckConfig"`
	AtmFeesWaiverConfig             *AtmFeesWaiverConfig                        `json:"atmFeesWaiverConfig"`
}

// JVProcessCfg ...
type JVProcessCfg struct {
	Enable                     bool         `json:"enable"`
	EnableCreditDebitIndicator bool         `json:"enableCreditDebitIndicator"`
	TransactionCodeType        string       `json:"transactionCodeType"`
	TransactionDomain          string       `json:"transactionDomain"`
	TransactionSubType         string       `json:"transactionSubType"`
	DebitAccountID             string       `json:"debitAccountID"`
	CreditAccountID            string       `json:"creditAccountID"`
	CreditAccountMap           JVAccountMap `json:"creditAccountMap"`
	DebitAccountMap            JVAccountMap `json:"debitAccountMap"`
}

type JVAccountMap struct {
	TransactionCodeType string `json:"transactionCodeType"`
	SrcAccountID        string `json:"srcAccountID"`
	DstAccountID        string `json:"dstAccountID"`
}

// KeyStore defines config required for RSA keys
type KeyStore struct {
	PrivateKey string `json:"privateKey"`
	PublicKey  string `json:"publicKey"`
}

// ACSConfig defines config required for ACS flow
type ACSConfig struct {
	AuthnReqRSAPublicKey   string `json:"authnReqRSAPublicKey"`
	AuthnRespRSAPrivateKey string `json:"authnRespRSAPrivateKey"`
	AuthnAESKey            string `json:"authnAESKey"`
	ByPassFinTrust         bool   `json:"byPassFinTrust"`
	DisableSignatureVerify bool   `json:"disableSignatureVerify"`
}

// KafkaConfig defines the config of Kafka.
type KafkaConfig struct {
	Brokers       []string             `json:"brokers"`
	TopicName     string               `json:"topicName"`
	ClientID      string               `json:"clientID"`
	InitOffset    sndconfig.OffsetType `json:"initOffset"`
	ClusterType   string               `json:"clusterType"`
	EnableTLS     bool                 `json:"enableTLS"`
	DisableStream bool                 `json:"disableStream"`
	DtoName       string               `json:"dtoName"`
	StreamId      streams.StreamID     `json:"streamId"`
}

// FeatureFlags defines flags for flexible feature toggling
type FeatureFlags struct {
	EnableWorker                                   bool `json:"enableWorker"`
	EnablePaynetWorker                             bool `json:"enablePaynetWorker"`
	EnableActivationCodeCheck                      bool `json:"enableActivationCodeCheck"`
	BypassChallengeCheck                           bool `json:"bypassChallengeCheck"`
	EnableEnhancedMatchingLogic                    bool `json:"enableEnhancedMatchingLogic"`
	EnableClearingDryRun                           bool `json:"enableClearingDryRun"`
	EnablePreTransactionCheck                      bool `json:"enablePreTransactionCheck"`
	EnableFetchOnlyV2                              bool `json:"enableFetchOnlyV2"`
	EnableFetchOnlyV2Wildcard                      bool `json:"enableFetchOnlyV2Wildcard"`
	EnableFetchOnlyS3V2                            bool `json:"enableFetchOnlyS3V2"`
	EnableClearingWithAmount0                      bool `json:"enableClearingWithAmount0"`
	EnableUpdateCardStatusForSomeOutOfSyncENStatus bool `json:"enableUpdateCardStatusForSomeOutOfSyncENStatus"`
	EnableKafkaRetryHandling                       bool `json:"enableKafkaRetryHandling"`
	EnableStaticECDHDecryptV2                      bool `json:"enableStaticECDHDecryptV2"`
	EnableReleaseMastercardAuth                    bool `json:"enableReleaseMastercardAuth"`
	EnableResumeClearingRefund                     bool `json:"enableResumeClearingRefund"`
	EnableClearingLongLockDuration                 bool `json:"enableClearingLongLockDuration"`
	EnableClearingIn3Stage                         bool `json:"enableClearingIn3Stage"`
	EnableClearingRateLimiting                     bool `json:"enableClearingRateLimiting"`
	EnableAutoReversal                             bool `json:"enableAutoReversal"`
	EnableSkipMrc1403ForcePosting                  bool `json:"enableSkipMrc1403ForcePosting"`
}

// AdminCfg defines some administrative configs
type AdminCfg struct {
	PartialAuthExpiryInDay          int   `json:"partialAuthExpiryInDays"`
	ClearingConcurrency             int64 `json:"clearingConcurrency"`
	ClearingPartialCaptureDelayInMs int   `json:"clearingPartialCaptureDelayInMs"`
	ClearingDefaultDelayInMs        int   `json:"clearingDefaultDelayInMs"`
	ClearingBetweenStagesDelayInMs  int   `json:"clearingBetweenStagesDelayInMs"`
}

// WorkerCfg defines the config for worker
type WorkerCfg struct {
	CronExpression []string          `json:"cronExpression"`
	BlackoutDay    map[string]string `json:"blackoutDays"`
	FilePrefix     string            `json:"filePrefix"`
}

// FetchOnlyCfg defines the config for fetch only worker
type FetchOnlyCfg struct {
	FilePrefix      string   `json:"filePrefix"`
	CronExpression  []string `json:"cronExpression"`
	ENSFTPDirectory string   `json:"enSFTPDirectory"`
	S3Directory     string   `json:"s3Directory"`
	FileExt         string   `json:"fileExt"`
	FileNameFormat  string   `json:"fileNameFormat"`
}

// FetchOnlyV2Cfg defines the config for fetch only v2 worker
type FetchOnlyV2Cfg struct {
	Name                 string   `json:"name"`
	FilePrefix           string   `json:"filePrefix"`
	AllowWildcard        bool     `json:"allowWildcard"`
	AppendTodaySuffix    bool     `json:"appendTodaySuffix"`
	CronExpression       []string `json:"cronExpression"`
	ENSFTPDirectory      string   `json:"enSFTPDirectory"`
	S3Directory          string   `json:"s3Directory"`
	FileExt              string   `json:"fileExt"`
	MaxConcurrentFetches int      `json:"maxConcurrentFetches"`
}

// FetchPaynetSettlementCfg define the config for fetch only paynet settlement config
type FetchPaynetSettlementCfg struct {
	Name            string   `json:"name"`
	FilePrefixes    []string `json:"filePrefixes"`
	CronExpression  []string `json:"cronExpression"`
	ENSFTPDirectory string   `json:"enSFTPDirectory"`
	S3Directory     string   `json:"s3Directory"`
}

// FetchOnlyS3Cfg defines the config for fetch and push worker
type FetchOnlyS3Cfg struct {
	FilePrefix      string   `json:"filePrefix"`
	CronExpression  []string `json:"cronExpression"`
	ENSFTPDirectory string   `json:"enSFTPDirectory"`
	S3Directory     string   `json:"s3Directory"`
	FileExt         string   `json:"fileExt"`
	FileNameFormat  string   `json:"fileNameFormat"`
}

// FetchOnlyS3V2Cfg defines the config for fetch and upload worker
type FetchOnlyS3V2Cfg struct {
	FilePrefix      string   `json:"filePrefix"`
	CronExpression  []string `json:"cronExpression"`
	ENSFTPDirectory string   `json:"enSFTPDirectory"`
	S3Directory     string   `json:"s3Directory"`
	FileExt         string   `json:"fileExt"`
}

// ReleaseMastercardAuthCfg defines the config for releasing pre-auth automatically
type ReleaseMastercardAuthCfg struct {
	Name                  string     `json:"name"`
	Conditions            []string   `json:"conditions"`
	ConditionKeys         []string   `json:"conditionKeys"`
	ConditionValues       [][]string `json:"conditionValues"`
	MerchantNamePattern   string     `json:"merchantNamePattern"`
	CronExpression        []string   `json:"cronExpression"`
	ExpiryField           string     `json:"expiryField"`
	ExpiryDays            int        `json:"expiryDays"`
	ExpiryBackTrackDays   int        `json:"expiryBackTrackDays"`
	ExpiryQueryWindowDays int        `json:"expiryQueryWindowDays"`
}

// ReleasePreAuthCfg defines the config for releasing pre-auth automatically
type ReleasePreAuthCfg struct {
	Name                  string     `json:"name"`
	Conditions            []string   `json:"conditions"`
	ConditionKeys         []string   `json:"conditionKeys"`
	ConditionValues       [][]string `json:"conditionValues"`
	CronExpression        []string   `json:"cronExpression"`
	ExpiryField           string     `json:"expiryField"`
	ExpiryDays            int        `json:"expiryDays"`
	ExpiryBackTrackDays   int        `json:"expiryBackTrackDays"`
	ExpiryQueryWindowDays int        `json:"expiryQueryWindowDays"`
}

// FileProcessingSleepCfg defines the config file processing sleep in MS
type FileProcessingSleepCfg struct {
	Clearing int `json:"clearing"`
}

// BuildConditions ...
func (r *ReleasePreAuthCfg) BuildConditions(ctx context.Context) (ret []data.Condition) {
	for idx, condition := range r.Conditions {
		switch condition {
		case "ContainedIn":
			values := utils.ConvertToInterface(r.ConditionValues[idx])
			ret = append(ret, data.ContainedIn(r.ConditionKeys[idx], values...))
		case "NotContainedIn":
			values := utils.ConvertToInterface(r.ConditionValues[idx])
			ret = append(ret, data.NotContainedIn(r.ConditionKeys[idx], values...))
		case "GreaterThanOrEqualTo":
			tmplStr := r.ConditionValues[idx][0]
			conditionVal := r.resolveTmpl(ctx, tmplStr, nil)
			ret = append(ret, data.GreaterThanOrEqualTo(r.ConditionKeys[idx], conditionVal))
		case "LessThan":
			tmplStr := r.ConditionValues[idx][0]
			conditionVal := r.resolveTmpl(ctx, tmplStr, nil)
			ret = append(ret, data.LessThan(r.ConditionKeys[idx], conditionVal))
		case "LessThanOrEqualTo":
			tmplStr := r.ConditionValues[idx][0]
			conditionVal := r.resolveTmpl(ctx, tmplStr, nil)
			ret = append(ret, data.LessThanOrEqualTo(r.ConditionKeys[idx], conditionVal))
		case "EqualTo":
			ret = append(ret, data.EqualTo(r.ConditionKeys[idx], r.ConditionValues[idx][0]))
		}
	}
	return
}

// BuildMCConditions ...
func (r *ReleaseMastercardAuthCfg) BuildMCConditions(ctx context.Context) (ret []data.Condition) {
	for idx, condition := range r.Conditions {
		switch condition {
		case "ContainedIn":
			values := utils.ConvertToInterface(r.ConditionValues[idx])
			ret = append(ret, data.ContainedIn(r.ConditionKeys[idx], values...))
		case "NotContainedIn":
			values := utils.ConvertToInterface(r.ConditionValues[idx])
			ret = append(ret, data.NotContainedIn(r.ConditionKeys[idx], values...))
		case "GreaterThanOrEqualTo":
			tmplStr := r.ConditionValues[idx][0]
			conditionVal := r.resolveMCTmpl(ctx, tmplStr, nil)
			ret = append(ret, data.GreaterThanOrEqualTo(r.ConditionKeys[idx], conditionVal))
		case "LessThan":
			tmplStr := r.ConditionValues[idx][0]
			conditionVal := r.resolveMCTmpl(ctx, tmplStr, nil)
			ret = append(ret, data.LessThan(r.ConditionKeys[idx], conditionVal))
		case "LessThanOrEqualTo":
			tmplStr := r.ConditionValues[idx][0]
			conditionVal := r.resolveMCTmpl(ctx, tmplStr, nil)
			ret = append(ret, data.LessThanOrEqualTo(r.ConditionKeys[idx], conditionVal))
		case "EqualTo":
			ret = append(ret, data.EqualTo(r.ConditionKeys[idx], r.ConditionValues[idx][0]))
		}
	}
	return
}

// resolveTmpl ...
func (r *ReleasePreAuthCfg) resolveTmpl(ctx context.Context, tmplDetail string, data any) string {
	templateStr := fmt.Sprintf(`{{ %s }}`, tmplDetail)
	fmap := sprig.TxtFuncMap()
	tmplExecutor := template.Must(template.New("resolver").Funcs(fmap).Parse(templateStr))
	var buf bytes.Buffer
	if err := tmplExecutor.Execute(&buf, data); err != nil {
		slog.FromContext(ctx).Error("ReleasePreAuthCfg", fmt.Sprintf("can't resolve template. Error %v", err))
		panic(err)
	}
	return buf.String()
}

// resolveTmpl ...
func (r *ReleaseMastercardAuthCfg) resolveMCTmpl(ctx context.Context, tmplDetail string, data any) string {
	templateStr := fmt.Sprintf(`{{ %s }}`, tmplDetail)
	fmap := sprig.TxtFuncMap()
	tmplExecutor := template.Must(template.New("resolver").Funcs(fmap).Parse(templateStr))
	var buf bytes.Buffer
	if err := tmplExecutor.Execute(&buf, data); err != nil {
		slog.FromContext(ctx).Error("ReleaseMastercardAuthCfg", fmt.Sprintf("can't resolve template. Error %v", err))
		panic(err)
	}
	return buf.String()
}

// Sftp defines the sftp config
type Sftp struct {
	User                        string    `json:"user"`
	Key                         string    `json:"key"`
	Timeout                     int       `json:"timeout"`
	HostAndPort                 string    `json:"hostAndPort"`
	SSHPassword                 string    `json:"sshPassword"`
	Retry                       SftpRetry `json:"retry"`
	DownloadLimitBytesPerSecond int       `json:"downloadLimitBytesPerSecond"`
}

type SftpRetry struct {
	Time             int `json:"time"`
	IntervalInSecond int `json:"intervalInSecond"`
}

// CountryConfig ...
type CountryConfig struct {
	CurrencyCode      string          `json:"currencyCode"`
	CurrencyNum       string          `json:"currencyNum"`
	CountryCode       string          `json:"countryCode"`
	CountryCodeAlpha3 string          `json:"countryCodeAlpha3"`
	CountryName       string          `json:"countryName"`
	AppHeaderConfig   AppHeaderConfig `json:"appHeaderConfig"`
}

// TransactionTimeoutInMiliseconds ...
type TransactionTimeoutInMiliseconds struct {
	Mastercard map[string]int `json:"mastercard"`
	Paynet     map[string]int `json:"paynet"`
}

// AppHeaderConfig ...
type AppHeaderConfig struct {
	ParticipantID  string   `json:"partID"`
	ParticipantIDs []string `json:"partIDs"`
	ChannelID      string   `json:"channelID"`
	UserID         string   `json:"userID"`
}

// AllowTransactionTypes defines the list of whitelisted transaction types
type AllowTransactionTypes struct {
	Code []string `json:"code"`
}

// ServiceConfig defines config required to call dakota services.
type ServiceConfig struct {
	BaseURL        string     `json:"baseURL"`
	ServiceName    string     `json:"serviceName"`
	CircuitBreaker *CBSetting `json:"circuitBreaker"`
}

// CBSetting is the circuit breaker configurations for storage
// It adds ErrorHandler to ignore context.Canceled errors.
type CBSetting struct {
	hystrix.CommandConfig
}

// WorkflowRetryConfig defines the different retry options
type WorkflowRetryConfig struct {
	SaleCompletionStateRetry *RetryPolicy `json:"saleCompletionStateRetry"`
	CancelFailedStateRetry   *RetryPolicy `json:"cancelFailedStateRetry"`
}

// RetryPolicy defines the configs for each retry policy
type RetryPolicy struct {
	IntervalInMilliSeconds int `json:"intervalInMilliSeconds"`
	MaxAttempt             int `json:"maxAttempt"`
}

type AuthAmountMarkupConfig struct {
	Name                 string  `json:"name"`
	MCC                  string  `json:"mcc"`
	MerchantNamePattern  string  `json:"merchantNamePattern"`
	AuthOriginalCurrency string  `json:"authOriginalCurrency"`
	Multiplier           float32 `json:"multiplier"`
}

type MarkupConfig struct {
	Name                 string  `json:"name"`
	MCC                  string  `json:"mcc"`
	MerchantNamePattern  string  `json:"merchantNamePattern"`
	AuthOriginalCurrency string  `json:"authOriginalCurrency"`
	Multiplier           float32 `json:"multiplier"`
}

// QueueHandlerSQSConfig defines the config of SQS.
type QueueHandlerSQSConfig struct {
	QueueURL                              string `json:"queueURL"`
	AwsRegion                             string `json:"awsRegion"`
	ExponentialBackoffBaseIntervalSeconds uint   `json:"exponentialBackoffBaseIntervalSeconds"`
	WaitTimeSeconds                       int32  `json:"waitTimeSeconds"`
	DefaultVisibilityTimeoutSeconds       uint   `json:"defaultVisibilityTimeoutSeconds"`
	WorkerCount                           int    `json:"workerCount"`
	MaxNumberOfMessages                   int64  `json:"maxNumberOfMessages"`
}

// QueueHandlerClearingConfig defines the config of the clearing process
type QueueHandlerClearingConfig struct {
	AllowedProxyNumbers                []string `json:"allowedProxyNumbers"`
	IncreaseVisibilityTimeOutBatchSize int      `json:"increaseVisibilityTimeOutBatchSize"`
	AllowedProxyNumberThreshold        int      `json:"allowedProxyNumberThreshold"`
	ProcessInitClearingDelayInSeconds  int      `json:"processInitClearingDelayInSeconds"`
}

// PreTransactionCheckConfig defines the config of the pre transaction check, specifically risk check
type PreTransactionCheckConfig struct {
	AllowedProxyNumbers         []string `json:"allowedProxyNumbers"`
	AllowedProxyNumberThreshold int      `json:"allowedProxyNumberThreshold"`
}

type AtmFeesWaiverConfig struct {
	TracksWithdrawalUntilNext string `json:"tracksWithdrawalUntilNext"`
}
