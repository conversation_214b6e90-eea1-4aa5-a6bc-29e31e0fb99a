// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import mock "github.com/stretchr/testify/mock"

// NewEuronetAdapterT is an autogenerated mock type for the NewEuronetAdapterT type
type NewEuronetAdapterT struct {
	mock.Mock
}

// Cleanup provides a mock function with given fields: _a0
func (_m *NewEuronetAdapterT) Cleanup(_a0 func()) {
	_m.Called(_a0)
}

// Errorf provides a mock function with given fields: format, args
func (_m *NewEuronetAdapterT) Errorf(format string, args ...interface{}) {
	var _ca []interface{}
	_ca = append(_ca, format)
	_ca = append(_ca, args...)
	_m.Called(_ca...)
}

// FailNow provides a mock function with given fields:
func (_m *NewEuronetAdapterT) FailNow() {
	_m.Called()
}

// Logf provides a mock function with given fields: format, args
func (_m *NewEuronetAdapterT) Logf(format string, args ...interface{}) {
	var _ca []interface{}
	_ca = append(_ca, format)
	_ca = append(_ca, args...)
	_m.Called(_ca...)
}

type mockConstructorTestingTNewNewEuronetAdapterT interface {
	mock.TestingT
	Cleanup(func())
}

// NewNewEuronetAdapterT creates a new instance of NewEuronetAdapterT. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewNewEuronetAdapterT(t mockConstructorTestingTNewNewEuronetAdapterT) *NewEuronetAdapterT {
	mock := &NewEuronetAdapterT{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
