syntax = "proto3";

package euronetAdapter;

option go_package = "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api";

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "gxs/api/annotations.proto";

message CardIssuanceRequest {
    string cardID = 1;
    string cardIssuanceType = 2;
    string idempotencyKey = 3;
    string displayName = 4;
    string phoneNumber = 5;
    MailingAddress mailingAddress = 6;
    string activationCode = 7;
}

message MailingAddress {
    string street = 1;
    string block = 2;
    string unit = 3;
    string city = 4;
    string country = 5;
    string postalCode = 6;
    string addrLine1 = 7;
    string addrLine2 = 8;
    string state = 9;
}

message CardIssuanceResponse {
    CardResource data = 1;
}

message GetEncryptedCVV2CardDetailsRequest {
    string cardID = 1;
    string encryptedData = 2;
    string iv = 3;
    string salt = 4;
    string publicKey = 5;
}

message GetEncryptedCVV2CardDetailsResponse {
    string encryptedCardDetails = 1;
    string iv = 2;
    string salt = 3;
    string publicKey = 4;
}

message UpdateCardStatusRequest {
    string targetCardStatus = 1;
    string cardID = 2;
    string idempotencyKey = 3;
    string reasonCode = 4; // required only when target status is retired
}

message UpdateCardStatusResponse {
    string status = 1;
    string statusReason = 2;
    string statusMessage = 3;
    string referenceID = 4;
    string proxyNumber = 5;
    int64 cardSequenceNumber = 6;
}

message PhysicalCardActivateRequest {
    string cardID = 1;
    string idempotencyKey = 2;
}

message PhysicalCardActivateResponse {
    string status = 1;
    string statusReason = 2;
    string statusMessage = 3;
    string referenceID = 4;
    string proxyNumber = 5;
    int64 cardSequenceNumber = 6;
}

message CardSetPinRequest {
    string cardID = 1 [(gxs.api.validate) = "string,max=36,required"];
    string encryptedPin = 2 [(gxs.api.validate) = "string,required"];
    string idempotencyKey = 3;
}

message CardSetPinResponse {
    string status = 1;
    string statusReason = 2;
    string statusMessage = 3;
    string referenceID = 4;
    string proxyNumber = 5;
    int64 cardSequenceNumber = 6;
}


message CardResource {
    string status = 1;
    string statusReason = 2;
    string statusMessage = 3;
    string maskedCardNumber = 4;
    string internalCardID = 5;
    string expiryDate = 6;
    string cvv = 7;
    string proxyNumber = 8;
    int64 cardSequenceNumber = 9;
}

message AppHeader {
    string partID = 1;
    string serviceName = 2;
    string channelID = 3;
    string userID = 4;
    string reqID = 5;
    string reqDateTime = 6;
}

message TransactionInfo {
    string mti = 1;
    string proxyNumber = 2;
    string processingCode = 3;
    int64 transactionAmount = 4;
    int64 cardHolderBillingAmount = 5;
    google.protobuf.Timestamp transmissionDateTime = 6;
    string systemTraceAuditNumber = 7;
    string localTransactionDateTime = 8;
    string merchantCategoryCode = 9;
    string acquirerCountryCode = 10;
    string panEntryMode = 11;
    string networkID = 12;

    string POSDataSF4 = 13;
    string POSDataSF5 = 14;
    string POSDataSF7 = 15;
    string POSDataSF10 = 16;

    string acquirerID = 17;
    string retrievalReferenceNumber = 18;
    string authorizationID = 19;
    string cardAcceptorTerminalID = 20;
    string cardAcceptorMerchantID = 21;
    string cardAcceptorTermName = 22;
    string transactionCurrencyCode = 23;
    string cardHolderCurrencyCode = 24;
    string transactionID = 25;

    string orgTransactionID = 26;
    string threeDsAuthTID = 27;
    string DCCIndicator = 28;
    string ECOMIndicator = 29;
    string UCAFIndicator = 30;
    string threeDsValidation = 31;

    string PANValidation = 32;
    string expiryDateValidation = 33;
    string pinValidation = 34;
    string CVVValidation = 35;
    string CVV2Validation = 36;
    string EMVValidation = 37;

    int64 replacementAmount = 38;
    int64 cardHolderReplacementAmount = 39;

    string DISecIssData = 40;
    string DIFraudScore = 41;
    string DIScoreReasonCode = 42;
    string MSTransactionTypeID = 43;
    string MSTransactionRefData = 44;

    string acquirerReferenceData = 45;

    int64 transactionFees = 46;
    int64 conversionRate = 47;
    TransactionProperties properties = 48;
}

message TransactionProperties {
    TransactionRequestProperties requestProperties = 1;
}

message TransactionRequestProperties {
    int64 originalAmount = 1;
    int64 requestAmount = 2;
}

message Response {
    string responseCode = 1;
    string description = 2;
}

message BalanceInfo {
    int64 availableBalance = 1 [(gxs.api.noomit) = true];
    string currencyCode = 2  [(gxs.api.noomit) = true];
    int64 withdrawalLimit = 3 [(gxs.api.noomit) = true];
}

message DeclineInfo {
    string declineCode = 1;
    string declineReason = 2;
}

message TransactionAPIRequest {
    TransactionRequest transactionRequest = 1;
    string signature = 2;
}

message TransactionRequest {
    AppHeader appHeader = 1;
    TransactionInfo transactionInfo = 2;
    DeclineInfo declineInfo = 3;
}

message TransactionAPIResponse {
    TransactionResponse transactionResponse = 1;
    string signature = 2;
}

message TransactionResponse {
    AppHeader appHeader = 1;
    TransactionInfo transactionInfo = 2;
    Response  response = 3;
    BalanceInfo balanceInfo = 4;
    string signature = 5;
}

message ChallengeCheckAPIRequest {
    ChallengeCheckRequest challengeCheckRequest = 1;
    string signature = 2;
}

message ChallengeCheckAPIResponse {
    ChallengeCheckResponse challengeCheckResponse = 1;
    string signature = 2;
}

message ChallengeCheckRequest {
    AppHeader appHeader = 1;
    string maskCardNumber = 2;
    string proxyNumber = 3;
    string transactionDateTime = 4;
    string acsTransactionID = 5;
    string cardType = 6;
    string threeDsTransactionID = 7;
    string dsTransactionID = 8;
    string purchaseDateTime = 9;
    AmountInfo amountInfo = 10;
    MerchantInfo merchantInfo = 11;
    CardHolderInfo cardHolderInfo = 12;
    RiskInfo riskInfo = 13;
    AcquirerInfo acquirerInfo = 14;
    Address billingAddress = 15;
    Address shippingAddress = 16;
}

message AmountInfo {
    int64 amount = 1;
    int64 currency = 2;
    int64 currencyExponent = 3;
}

message MerchantInfo {
    string name = 1;
    int64 countryCode = 2;
    string url = 3;
}

message CardHolderInfo {
    string homePhone = 1;
    string mobilePhone = 2;
    string emailAddress = 3;
}

message RiskInfo {
    string browserIP = 1;
    string browserTimeZone = 2;
    string browserUserAgent = 3;
    string messageProtocol = 4;
}

message AcquirerInfo {
    string acquirerBIN = 1;
    string acquirerMerchantID = 2;
}

message Address {
    string city = 1;
    string country = 2;
    string addressLine1 = 3;
    string addressLine2 = 4;
    string addressLine3 = 5;
    string postalCode = 6;
}

message DeliveryInfo {
    string deliveryID = 1;
    string channel = 2;
    string channelStatus = 3;
    string challengeCheckResponse = 4;
}

message ChallengeCheckResponse {
    AppHeader appHeader = 1;
    string acsTransactionID = 2;
    string dsTransactionID = 3;
    Response response = 4;
}

message TriggerOTPAPIRequest {
    TriggerOTPRequest triggerOTPRequest = 1;
    string signature = 2;
}

message TriggerOTPAPIResponse {
    TriggerOTPResponse triggerOTPResponse = 1;
    string signature = 2;
}

message TriggerOTPRequest {
    AppHeader appHeader = 1;
    string maskCardNumber = 2;
    string proxyNumber = 3;
    string transactionDateTime = 4;
    string acsTransactionID = 5;
    string otpValue = 6;
    int64 otpValidity = 7;
    string resentOTPFlag = 8;
    MerchantInfo merchantInfo = 9;
    AmountInfo amountInfo = 10;
}

message TriggerOTPResponse {
    AppHeader appHeader = 1;
    string acsTransactionID = 2;
    Response response = 3;
}

message ACSAdviceAPIRequest {
    ACSAdviceRequest acsAdviceRequest = 1;
    string signature = 2;
}

message ACSAdviceAPIResponse {
    ACSAdviceResponse acsAdviceResponse = 1;
    string signature = 2;
}

message ACSAdviceRequest {
    AppHeader appHeader = 1;
    string maskCardNumber = 2;
    string proxyNumber = 3;
    string transactionDateTime = 4;
    string acsTransactionID = 5;
    string cardType = 6;
    string threeDsTransactionID = 7;
    string dsTransactionID = 8;
    string purchaseDateTime = 9;
    string acsReferenceNumber = 10;
    string transactionStatus = 11;
    int64 resendAttempts = 12;
    string eciIndicator = 13;
    string verificationResponse = 14;
    string authRequestResponse = 15;
    string messageID = 16;
    DeliveryInfo deliveryInfo = 17;
    AmountInfo amountInfo = 18;
    MerchantInfo merchantInfo = 19;
    CardHolderInfo cardHolderInfo = 20;
    RiskInfo riskInfo = 21;
    AcquirerInfo acquirerInfo = 22;
    Address billingAddress = 23;
    Address shippingAddress = 24;
}

message ACSAdviceResponse {
    AppHeader appHeader = 1;
    string acsTransactionID = 2;
    string dsTransactionID = 3;
    Response response = 4;
}

message TriggerFileWorkflowRequest {
    string type = 1;
    string fileName = 2;
    string idempotencyKey = 3;
    string enSftpDirectory = 4;
    string s3Directory = 5;
}

message TriggerFileWorkflowResponse {
    string status = 1;
}

message PaynetTriggerFileWorkflowRequest {
    string type = 1;
    string fileName = 2;
    string idempotencyKey = 3;
    string enSftpDirectory = 4;
    string s3Directory = 5;
}

message PaynetTriggerFileWorkflowResponse {
    string status = 1;
}

message PaynetAuthorizeRequest {
    message AuthorizeReq {
        string mti = 1;
        string proxyNumber = 2;
        string processingCode = 3;
        int64 transactionAmount = 4;
        int64 settlementAmount = 5;
        google.protobuf.Timestamp transmissionDateTime = 6;
        string systemTraceAuditNumber = 7;
        string localTransactionDateTime = 8;
        string settlementDate = 9;
        string captureDate = 10;
        string merchantCategoryCode = 11;
        string acquirerCountryCode = 12;
        string posEntryMode = 13;
        string posConditionCode = 14;
        string networkID = 15;
        int64 transactionFeeAmount = 16;
        int64 processingFeeAmount = 17;
        string acquirerID = 18;
        string retrievalReferenceNumber = 19;
        string authorizationID = 20;
        string cardAcceptorTerminalID = 21;
        string cardAcceptorIDCode = 22;
        string cardAcceptorTermName = 23;
        string retailerID = 24;
        string transactionCurrencyCode = 25;
        string cardHolderCurrencyCode = 26;
        string transactionID = 27;
        string origTransactionID = 28;
        string threeDsAuthTID = 29;
        string threeDsAuthResultCode = 30;
        string secondFactorAuthCode = 31;
        string cnpPaymentIndicator = 32;
        string cnpOrigRRN = 33;
        string cnpOrigTransactionDate = 34;
        string transitTransactionTypeID = 35;
        string transitTransportModeID = 36;
        string transitTransactionDetails = 37;
        string transitOrigRRN = 38;
        string transitOrigTransactionDate = 39;
        string terminalAddress = 40;
        string terminalOwnerFIID = 41;
        string acquirerFIID = 42;
        string threeDsValidation = 43;
        string PANValidation = 44;
        string expiryDateValidation = 45;
        string pinValidation = 46;
        string CVVValidation = 47;
        string CVV2Validation = 48;
        string EMVValidation = 49;
        string CardAcceptorMerchantID = 50;
    }

    message TransactionAuthorizeRequest {
        AppHeader appHeader = 1;
        AuthorizeReq transactionInfo = 2;
    }

    TransactionAuthorizeRequest transactionAuthorizeRequest = 1;
    string signature = 2;
}

message PaynetAuthorizeResponse {
    message AuthorizeRes {
        string mti = 1;
        string proxyNumber = 2;
        string processingCode = 3;
        int64 transactionAmount = 4;
        google.protobuf.Timestamp transmissionDateTime = 5;
        string systemTraceAuditNumber = 6;
        string localTransactionDateTime = 7;
        string merchantCategoryCode = 8;
        string acquirerCountryCode = 9;
        string acquirerID = 10;
        string retrievalReferenceNumber = 11;
        string authorizationID = 12;
        string cardAcceptorTerminalID = 13;
        string cardAcceptorMerchantID = 14;
        string cardAcceptorTermName = 15;
        string transactionCurrencyCode = 16;
        string cardHolderCurrencyCode = 17;
        string transactionID = 18;
        string origTransactionID = 19;
    }

    message Response {
        string responseCode = 1;
        string description = 2;
    }

    message TransactionAuthorizeResponse {
        AppHeader appHeader = 1;
        AuthorizeRes transactionInfo = 2;
        Response response = 3;
        BalanceInfo balanceInfo = 4;
    }

    TransactionAuthorizeResponse transactionAuthorizeResponse = 1;
    string signature = 2;
}

message PaynetReversalPreAuthRequest {
    message ReversalPreAuthReq {
        string mti = 1;
        int64 transactionAmount = 2;
        google.protobuf.Timestamp transmissionDateTime = 3;
        string systemTraceAuditNumber = 4;
        string localTransactionDateTime = 5;
        string captureDate = 6;
        string merchantCategoryCode = 7;
        string posEntryMode = 8;
        string posConditionCode = 9;
        string retrievalReferenceNumber = 10;
        string cardAcceptorTerminalID = 11;
        string cardAcceptorIDCode = 12;
        string cardAcceptorTermName = 13;
        string retailerID = 14;
        string transactionCurrencyCode = 15;
        string terminalOwnerFIID = 16;
        string acquirerFIID = 17;
        string threeDsAuthTID = 18;
        string threeDsAuthResultCode = 19;
        string secondFactorAuthCode = 20;
        string cnpPaymentIndicator = 21;
        string cnpOrigRRN = 22;
        string cnpOrigTransactionDate = 23;
        string acquirerCountryCode = 24;
        string networkID = 25;
        string authorizationID = 26;
        string cardHolderCurrencyCode = 27;
        string transactionID = 28;
        string origTransactionID = 29;
        string threeDsValidation = 30;
        string PANValidation = 31;
        string expiryDateValidation = 32;
        string pinValidation = 33;
        string CVVValidation = 34;
        string CVV2Validation = 35;
        string EMVValidation = 36;
        string proxyNumber = 37;
        int64 CardHolderReplacementAmount = 38;
        string processingCode = 39;
        string settlementDate = 40;
    }

    message TransactionReversalPreAuthRequest {
        AppHeader appHeader = 1;
        ReversalPreAuthReq transactionInfo = 2;
        DeclineInfo declineInfo =3;
    }

    TransactionReversalPreAuthRequest transactionReversalPreAuthRequest = 1;
    string signature = 2;
}

message PaynetReversalPreAuthResponse {
    message ReversalPreAuthRes {
        string mti = 1;
        string proxyNumber = 2;
        string processingCode = 3;
        int64 transactionAmount = 4;
        google.protobuf.Timestamp transmissionDateTime = 5;
        string systemTraceAuditNumber = 6;
        string localTransactionDateTime = 7;
        string merchantCategoryCode = 8;
        string acquirerCountryCode = 9;
        string acquirerID = 10;
        string retrievalReferenceNumber = 11;
        string authorizationID = 12;
        string cardAcceptorTerminalID = 13;
        string cardAcceptorMerchantID = 14;
        string cardAcceptorTermName = 15;
        string transactionCurrencyCode = 16;
        string cardHolderCurrencyCode = 17;
        string transactionID = 18;
        string origTransactionID = 19;
    }

    message Response {
        string responseCode = 1;
        string description = 2;
    }

    message TransactionReversalPreAuthResponse {
        AppHeader appHeader = 1;
        ReversalPreAuthRes transactionInfo = 2;
        Response response = 3;
    }

    TransactionReversalPreAuthResponse transactionReversalPreAuthResponse = 1;
    string signature = 2;
}

message PaynetReversalSettledRequest {
    message ReversalSettledReq {
        string mti = 1;
        string processingCode = 2;
        int64 transactionAmount = 3;
        google.protobuf.Timestamp transmissionDateTime = 4;
        string systemTraceAuditNumber = 5;
        string localTransactionDateTime = 6;
        string settlementDate = 7;
        string captureDate = 8;
        string merchantCategoryCode = 9;
        string posEntryMode = 10;
        string posConditionCode = 11;
        string retrievalReferenceNumber = 12;
        string cardAcceptorIDCode = 13;
        string threeDsAuthTID = 14;
        string threeDsAuthResultCode = 15;
        string secondFactorAuthCode = 16;
        string cnpPaymentIndicator = 17;
        string cnpOrigRRN = 18;
        string cnpOrigTransactionDate = 19;
        string transactionCurrencyCode = 20;
        string terminalOwnerFIID = 21;
        string acquirerFIID = 22;
        string cardAcceptorTermName = 23;
        string transitTransactionTypeID = 24;
        string transitTransportModeID = 25;
        string transitTransactionDetails = 26;
        string transitOrigRRN = 27;
        string transitOrigTransactionDate = 28;
        int64 transactionFeeAmount = 29;
        int64 processingFeeAmount = 30;
        string terminalAddress = 31;
        string cardAcceptorTerminalID = 32;
        string retailerID = 33;
        string proxyNumber = 34;
        string acquirerCountryCode = 35;
        string networkID = 36;
        string acquirerID = 37;
        string authorizationID = 38;
        string cardAcceptorMerchantID = 39;
        string cardHolderCurrencyCode = 40;
        string transactionID = 41;
        string origTransactionID = 42;
        string threeDsValidation = 43;
        string PANValidation = 44;
        string expiryDateValidation = 45;
        string pinValidation = 46;
        string CVVValidation = 47;
        string CVV2Validation = 48;
        string EMVValidation = 49;
    }

    message TransactionReversalSettledRequest {
        AppHeader appHeader = 1;
        ReversalSettledReq transactionInfo = 2;
        DeclineInfo declineInfo =3;
    }

    TransactionReversalSettledRequest transactionReversalSettledRequest = 1;
    string signature = 2;
}

message PaynetReversalSettledResponse {
    message ReversalPreAuthRes {
        string mti = 1;
        string proxyNumber = 2;
        string processingCode = 3;
        int64 transactionAmount = 4;
        google.protobuf.Timestamp transmissionDateTime = 5;
        string systemTraceAuditNumber = 6;
        string localTransactionDateTime = 7;
        string merchantCategoryCode = 8;
        string acquirerCountryCode = 9;
        string acquirerID = 10;
        string retrievalReferenceNumber = 11;
        string authorizationID = 12;
        string cardAcceptorTerminalID = 13;
        string cardAcceptorMerchantID = 14;
        string cardAcceptorTermName = 15;
        string transactionCurrencyCode = 16;
        string cardHolderCurrencyCode = 17;
        string transactionID = 18;
        string origTransactionID = 19;
    }

    message Response {
        string responseCode = 1;
        string description = 2;
    }

    message TransactionReversalSettledResponse {
        AppHeader appHeader = 1;
        ReversalPreAuthRes transactionInfo = 2;
        Response response = 3;
    }

    TransactionReversalSettledResponse transactionReversalSettledResponse = 1;
    string signature = 2;
}


message PaynetSaleCompletionRequest {
    message SaleCompletionReq {
        string mti = 1;
        string processingCode = 2;
        int64 transactionAmount = 3;
        google.protobuf.Timestamp transmissionDateTime = 4;
        string systemTraceAuditNumber = 5;
        string localTransactionDateTime = 6;
        string captureDate = 7;
        string merchantCategoryCode = 8;
        string posEntryMode = 9;
        string posConditionCode = 10;
        string retrievalReferenceNumber = 11;
        string cardAcceptorTerminalID = 12;
        string cardAcceptorIDCode = 13;
        string cardAcceptorTermName = 14;
        string retailerID = 15;
        string transactionCurrencyCode = 16;
        string terminalOwnerFIID = 17;
        string acquirerFIID = 18;
        string threeDsAuthTID = 19;
        string threeDsAuthResultCode = 20;
        string secondFactorAuthCode = 21;
        string cnpPaymentIndicator = 22;
        string cnpOrigRRN = 23;
        string cnpOrigTransactionDate = 24;
        string proxyNumber = 25;
        string networkID = 26;
        string acquirerID = 27;
        string authorizationID = 28;
        string cardHolderCurrencyCode = 29;
        string transactionID = 30;
        string origTransactionID = 31;
        string transitTransactionTypeID = 32;
        string transitTransportModeID = 33;
        string transitTransactionDetails = 34;
        string transitOrigRRN = 35;
        string transitOrigTransactionDate = 36;
        string terminalAddress = 37;
        string threeDsValidation = 38;
        string PANValidation = 39;
        string expiryDateValidation = 40;
        string pinValidation = 41;
        string CVVValidation = 42;
        string CVV2Validation = 43;
        string EMVValidation = 44;
    }

    message TransactionSaleCompletionRequest {
        AppHeader appHeader = 1;
        SaleCompletionReq transactionInfo = 2;
    }

    TransactionSaleCompletionRequest transactionSaleCompletionRequest = 1;
    string signature = 2;
}

message PaynetSaleCompletionResponse {
    message SaleCompletionRes {
        string mti = 1;
        string proxyNumber = 2;
        string processingCode = 3;
        int64 transactionAmount = 4;
        google.protobuf.Timestamp transmissionDateTime = 5;
        string systemTraceAuditNumber = 6;
        string localTransactionDateTime = 7;
        string merchantCategoryCode = 8;
        string acquirerCountryCode = 9;
        string acquirerID = 10;
        string retrievalReferenceNumber = 11;
        string authorizationID = 12;
        string cardAcceptorTerminalID = 13;
        string cardAcceptorMerchantID = 14;
        string cardAcceptorTermName = 15;
        string transactionCurrencyCode = 16;
        string cardHolderCurrencyCode = 17;
        string transactionID = 18;
        string origTransactionID = 19;
    }

    message Response {
        string responseCode = 1;
        string description = 2;
    }

    message TransactionSaleCompletionResponse {
        AppHeader appHeader = 1;
        SaleCompletionRes transactionInfo = 2;
        Response response = 3;
    }

    TransactionSaleCompletionResponse transactionSaleCompletionResponse = 1;
    string signature = 2;
}

message PaynetAdviceRequest {
    message AdviceReq {
        string mti = 1;
        string proxyNumber = 2;
        string processingCode = 3;
        int64 transactionAmount = 4;
        int64 cardHolderBillingAmount = 5;
        int64 settlementAmount = 6;
        string transmissionDateTime = 7;
        string systemTraceAuditNumber = 8;
        string localTransactionDateTime = 9;
        string settlementDate = 10;
        string merchantCategoryCode = 11;
        string acquirerCountryCode = 12;
        string posEntryMode = 13;
        string posConditionCode = 14;
        string networkID = 15;
        int64 transactionFeeAmount = 16;
        int64 processingFeeAmount = 17;
        string acquirerID = 18;
        string retrievalReferenceNumber = 19;
        string authorizationID = 20;
        string cardAcceptorTerminalID = 21;
        string cardAcceptorIDCode = 22;
        string cardAcceptorMerchantID = 23;
        string cardAcceptorTermName = 24;
        string retailerID = 25;
        string transactionCurrencyCode = 26;
        string cardHolderCurrencyCode = 27;
        string transactionID = 28;
        string origTransactionID = 29;
        string threeDsAuthTID = 30;
        string PANValidation = 31;
        string expiryDateValidation = 32;
        string pinValidation = 33;
        string CVVValidation = 34;
        string CVV2Validation = 35;
        string EMVValidation = 36;
    }

    message TransactionAdviceRequest {
        AppHeader appHeader = 1;
        AdviceReq transactionInfo = 2;
        DeclineInfo declineInfo = 3;
    }

    TransactionAdviceRequest transactionAdviceRequest = 1;
    string signature = 2;
}

message PaynetAdviceResponse {
    message AdviceRes {
        string mti = 1;
        string proxyNumber = 2;
        string processingCode = 3;
        int64 transactionAmount = 4;
        string transmissionDateTime = 5;
        string systemTraceAuditNumber = 6;
        string localTransactionDateTime = 7;
        string merchantCategoryCode = 8;
        string acquirerCountryCode = 9;
        string acquirerID = 10;
        string retrievalReferenceNumber = 11;
        string authorizationID = 12;
        string cardAcceptorTerminalID = 13;
        string cardAcceptorMerchantID = 14;
        string cardAcceptorTermName = 15;
        string transactionCurrencyCode = 16;
        string cardHolderCurrencyCode = 17;
        string transactionID = 18;
        string origTransactionID = 19;
    }

    message Response {
        string responseCode = 1;
        string description = 2;
    }

    message TransactionAdviceResponse {
        AppHeader appHeader = 1;
        AdviceRes transactionInfo = 2;
        Response response = 3;
    }

    TransactionAdviceResponse transactionAdviceResponse = 1;
    string signature = 2;
}

message PaynetBalanceEnquiryRequest {
    message BalanceEnquiryReq {
        string mti = 1;
        string proxyNumber = 2;
        string processingCode = 3;
        google.protobuf.Timestamp transmissionDateTime = 4;
        string systemTraceAuditNumber = 5;
        string localTransactionDateTime = 6;
        string merchantCategoryCode = 7;
        string acquirerCountryCode = 8;
        string posEntryMode = 9;
        string posConditionCode = 10;
        string networkID = 11;
        string acquirerID = 12;
        string retrievalReferenceNumber = 13;
        string cardAcceptorTerminalID = 14;
        string cardAcceptorMerchantID = 15;
        string cardAcceptorTermName = 16;
        string transactionID = 17;
        string terminalAddress = 18;
        string PANValidation = 19;
        string expiryDateValidation = 20;
        string pinValidation = 21;
        string CVVValidation = 22;
        string CVV2Validation = 23;
        string EMVValidation = 24;
    }

    message TransactionBalanceEnquiryRequest {
        AppHeader appHeader = 1;
        BalanceEnquiryReq transactionInfo = 2;
    }

    TransactionBalanceEnquiryRequest transactionBalanceEnquiryRequest = 1;
    string signature = 2;
}

message PaynetBalanceEnquiryResponse {
    message BalanceEnquiryRes {
        string mti = 1;
        string proxyNumber = 2;
        string processingCode = 3;
        google.protobuf.Timestamp transmissionDateTime = 4;
        string systemTraceAuditNumber = 5;
        string localTransactionDateTime = 6;
        string merchantCategoryCode = 7;
        string acquirerCountryCode = 8;
        string acquirerID = 9;
        string retrievalReferenceNumber = 10;
        string cardAcceptorTerminalID = 11;
        string cardAcceptorMerchantID = 12;
        string cardAcceptorTermName = 13;
        string transactionID = 14;
    }

    message Response {
        string responseCode = 1;
        string description = 2;
    }

    message TransactionBalanceEnquiryResponse {
        AppHeader appHeader = 1;
        BalanceEnquiryRes transactionInfo = 2;
        BalanceInfo balanceInfo = 3;
        Response response = 4;
    }

    TransactionBalanceEnquiryResponse transactionBalanceEnquiryResponse = 1;
    string signature = 2;
}

message ACSAuthenticateRequest {
    string encryptedRequest = 1;
}

message ACSAuthenticateResponse {
    string encryptedRequest = 1;
}

message ACSAuthCallbackRequest {
    string ReferenceID = 1;
    string Status = 2;
}

message ACSAuthCallbackResponse {

}

message FetchFailedProcessingRequest {
    string processingType = 1;
    string internalTxnID = 2;
    string acquirerRefData = 3;
    string approvalCode = 4;
}

message FetchFailedProcessingResponse {
    ClearingFailure clearingFailure = 1;
}

message ResumeFailedProcessingRequest {
    string processingType = 1;
    string internalTxnID = 2;
    string acquirerRefData = 3;
    string approvalCode = 4;
    int64 amountOverWrite = 5;
}

message ResumeFailedProcessingResponse {
    ClearingFailure clearingFailure = 1;
    AdjustmentScenarioFailure adjustmentFailure = 2;
}

message AdjustmentScenarioFailure {
    string transactionType = 1;
    string cardNumber = 2;
    string proxyNumber = 3;
    string txnAuditNumber = 4;
    string authCode = 5;
    string retrievalReferenceNumber = 6;
    string txnCurrency = 7;
    int64 txnAmount = 8;
    string billingCurrency = 9;
    int64 billingAmount = 10;
    string txnDate = 11;
    string merchantNameLoc = 12;
    string julianDate = 13;
    string status = 14;
    string statusReason = 15;
    string internalTxnID = 16;
    string source = 17;
    google.protobuf.Timestamp createdAt = 18;
    google.protobuf.Timestamp updatedAt = 19;
}

message ClearingFailure {
    string type = 1;
    string proxyNumber = 2;
    string processingCode = 3;
    int64 settlementAmount = 4;
    string settlementCurrency = 5;
    int64 originalAmount = 6;
    string originalCurrency = 7;
    int64 authAmount = 8;
    string authCurrency = 9;
    string localTxnDatetime = 10;
    string panEntryMode = 11;
    string mcc = 12;
    string acquirerRefData = 13;
    string acquirerID = 14;
    string retrievalRefNumber = 15;
    string approvalCode = 16;
    string cardAcceptorTermID = 17;
    string cardAcceptorID = 18;
    string cardAcceptorName = 19;
    string cardSchemeTxnID = 20;
    string messageReversalIndicator = 21;
    string messageReasonCode = 22;
    string cifNumber = 23;
    string accountNumber = 24;
    string internalTxnID = 25;
    string source = 26;
    string status = 27;
    string statusReason = 28;
    google.protobuf.Timestamp createdAt = 29;
    google.protobuf.Timestamp updateAt = 30;
}

message ResumeFailedClearingRequest {
    uint64 id = 1 [(gxs.api.validate) = "uint64,required"];
    bool enableHoldCodeOverWrite = 2;
    int64 amountOverWrite = 3;
}

message ResumeFailedClearingResponse {}

// EuronetAdapter provide API to handle different operations
service EuronetAdapter {

    // ---------------------------------- Internal APIs ----------------------------------

    // Get encrypted cvv2 card details
    rpc GetEncryptedCVV2CardDetails(GetEncryptedCVV2CardDetailsRequest) returns (GetEncryptedCVV2CardDetailsResponse) {
        option (google.api.http) = {
            post:  "/v1/card/getcvv2",
            body: "*",
        };
    }

    // Issue a card for user
    rpc CardIssuance(CardIssuanceRequest) returns (CardIssuanceResponse) {
        option (google.api.http) = {
            post:  "/v1/card/issuance",
            body: "*",
        };
    }

    // Update card status
    rpc CardStatusUpdate(UpdateCardStatusRequest) returns (UpdateCardStatusResponse) {
        option (google.api.http) = {
            put:  "/v1/card/status",
            body: "*",
        };
    }

    // Trigger a file workflow
    rpc TriggerFileWorkflow(TriggerFileWorkflowRequest) returns (TriggerFileWorkflowResponse) {
        option (google.api.http) = {
            post:  "/v1/card/file",
            body: "*",
        };
    }

    // Physical card activate
    rpc PhysicalCardActivate(PhysicalCardActivateRequest) returns (PhysicalCardActivateResponse) {
        option (google.api.http) = {
            post:  "/v1/card/physical/activate",
            body: "*",
        };
    }

    // Card Set Pin
    rpc CardSetPin(CardSetPinRequest)  returns (CardSetPinResponse) {
        option (google.api.http) = {
            post:  "/v1/internal/card/set-pin",
            body: "*",
        };
    }

    // ---------------------------------- EN Facing APIs ----------------------------------


    // For Authorize Transactions
    rpc Authorize(TransactionAPIRequest) returns (TransactionAPIResponse) {
        option (google.api.http) = {
            post:  "/v1/transactions/authorize",
            body: "*",
        };
    }

    // For Reversal Transactions
    rpc Reversal(TransactionAPIRequest) returns (TransactionAPIResponse) {
        option (google.api.http) = {
            post:  "/v1/transactions/reversal",
            body: "*",
        };
    }

    // For Advice
    rpc Advice(TransactionAPIRequest) returns (TransactionAPIResponse) {
        option (google.api.http) = {
            post:  "/v1/transactions/advice",
            body: "*",
        };
    }

    // For Balance Retrieval
    rpc BalanceEnquiry(TransactionAPIRequest) returns (TransactionAPIResponse) {
        option (google.api.http) = {
            post:  "/v1/transactions/balance",
            body: "*",
        };
    }

    // ---------------------------------- ACS APIs ----------------------------------

    // For Checking if challenge is required
    rpc ChallengeCheck(ChallengeCheckAPIRequest) returns (ChallengeCheckAPIResponse) {
        option (google.api.http) = {
            post:  "/v1/acs/challengeCheck",
            body: "*",
        };
    }

    // For OTP Trigger
    rpc TriggerOTP(TriggerOTPAPIRequest) returns (TriggerOTPAPIResponse) {
        option (google.api.http) = {
            post:  "/v1/acs/triggerOTP",
            body: "*",
        };
    }

    // For ACSAdvice
    rpc ACSAdvice(ACSAdviceAPIRequest) returns (ACSAdviceAPIResponse) {
        option (google.api.http) = {
            post:  "/v1/acs/advice",
            body: "*",
        };
    }

    // ACS Authenticate - this API is used to integrate with FinTrust to send in-app approve/reject transaction
    rpc ACSAuthenticate(ACSAuthenticateRequest) returns (ACSAuthenticateResponse) {
        option (google.api.http) = {
            post: "/v1/acs/authn",
            body: "*"
        };
    }

    // ACSAuthCallback - this callback is used for authentication status update
    rpc ACSAuthCallback(ACSAuthCallbackRequest) returns (ACSAuthCallbackResponse) {
        option (google.api.http) = {
            post: "/v1/acs/authn/callback",
            body: "*"
        };
    }

    // ---------------------------------- [Paynet] EN Facing APIs ----------------------------------

    // For Authorize Transactions
    rpc PaynetAuthorize(PaynetAuthorizeRequest) returns (PaynetAuthorizeResponse) {
        option (google.api.http) = {
            post:  "/v1/transactions/paynet/authorize",
            body: "*",
        };
    }

    // For Reversal PreAuth Transactions
    rpc PaynetReversalPreAuth(PaynetReversalPreAuthRequest) returns (PaynetReversalPreAuthResponse) {
        option (google.api.http) = {
            post:  "/v1/transactions/paynet/reversal-preauth",
            body: "*"
        };
    }

    // For Reversal Settled Transactions
    rpc PaynetReversalSettled(PaynetReversalSettledRequest) returns (PaynetReversalSettledResponse) {
        option (google.api.http) = {
            post:  "/v1/transactions/paynet/reversal-settled",
            body: "*",
        };
    }

    // For Sale Completion Transactions
    rpc PaynetSaleCompletion(PaynetSaleCompletionRequest) returns (PaynetSaleCompletionResponse) {
        option (google.api.http) = {
            post:  "/v1/transactions/paynet/sales-completion",
            body: "*",
        };
    }

    // For Advice
    rpc PaynetAdvice(PaynetAdviceRequest) returns (PaynetAdviceResponse) {
        option (google.api.http) = {
            post:  "/v1/transactions/paynet/advice",
            body: "*",
        };
    }

    // For Balance Retrieval
    rpc PaynetBalanceEnquiry(PaynetBalanceEnquiryRequest) returns (PaynetBalanceEnquiryResponse) {
        option (google.api.http) = {
            post:  "/v1/transactions/paynet/balance",
            body: "*",
        };
    }

    // Trigger a paynet file workflow
    rpc PaynetTriggerFileWorkflow(PaynetTriggerFileWorkflowRequest) returns (PaynetTriggerFileWorkflowResponse) {
        option (google.api.http) = {
            post:  "/v1/card/paynet/file",
            body: "*",
        };
    }

    // fetch txn that has failed in the file processing
    rpc FetchFailedProcessing(FetchFailedProcessingRequest) returns (FetchFailedProcessingResponse) {
        option (google.api.http) = {
            post:  "/v1/fetch-failed-processing",
            body: "*",
        };
        option(gxs.api.auth) = {
            client_identities: ["servicename.SentryPartnerT6"]
        };
    }

    // resume/reattempt txn that has failed in the file processing
    rpc ResumeFailedProcessing(ResumeFailedProcessingRequest) returns (ResumeFailedProcessingResponse) {
        option (google.api.http) = {
            post:  "/v1/resume-failed-processing",
            body: "*",
        };
        option(gxs.api.auth) = {
            client_identities: ["servicename.SentryPartnerT6"]
        };
    }

    // ResumeFailedClearing resume failed clearing record(s)
    rpc ResumeFailedClearing(ResumeFailedClearingRequest) returns (ResumeFailedClearingResponse) {
        option (google.api.http) = {
            post:  "/v1/resume-failed-clearing",
            body: "*",
        };
    }
}
