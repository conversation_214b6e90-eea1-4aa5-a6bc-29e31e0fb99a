package handlers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	getcarddetails "gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic/card/get_card_details"
)

func TestEuronetAdapterService_GetEncryptedCVV2CardDetails(t *testing.T) {
	mockClient := &getcarddetails.MockClient{}
	mockClient.On("GetEncryptedCVV2CardDetails", mock.Anything, mock.Anything).Once().Return(&api.GetEncryptedCVV2CardDetailsResponse{}, nil)
	service := EuronetAdapterService{
		GetAllCardsClient: mockClient,
	}
	_, err := service.GetEncryptedCVV2CardDetails(context.Background(), &api.GetEncryptedCVV2CardDetailsRequest{})
	assert.NoError(t, err)
}
