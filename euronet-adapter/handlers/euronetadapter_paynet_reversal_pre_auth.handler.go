package handlers

import (
	"context"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/recorder"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/common"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/api"
	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/constants"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/dto"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/logic"
	"gitlab.com/gx-regional/dbmy/digicard/euronet-adapter/server/config"
)

// PaynetReversalPreAuth For Reversal PreAuth Transactions
func (e *EuronetAdapterService) PaynetReversalPreAuth(ctx context.Context, req *api.PaynetReversalPreAuthRequest) (*api.PaynetReversalPreAuthResponse, error) {
	recorder.RecordCommon(ctx, string(constant.Paynet), common.PaynetReversalPreAuth)
	ctx = addTxnHeadersToSlogCtx(ctx, req.TransactionReversalPreAuthRequest.AppHeader)
	countryConfig := e.CountryConfig
	if err := validatePaynetReversalPreAuthRequest(ctx, req, countryConfig); err != nil {
		slog.FromContext(ctx).Error(paynetReversalPreAuthEvTag, "invalid pn reversal pre-auth request",
			slog.CustomTag(constants.CodeTag, err.Code), slog.CustomTag(constants.MessageTag, err.Message))
		recorder.RecordResponseCode(ctx, err.Code)
		errResp := toPaynetReversalPreAuthErrorResponse(req, &logic.ErrSystemMalfunction)
		e.publishTransactionRecord(ctx, req, errResp)

		return errResp, nil
	}

	txnInfo := req.TransactionReversalPreAuthRequest.TransactionInfo
	if time.Now().Before(txnInfo.TransmissionDateTime) {
		slog.FromContext(ctx).Error(paynetReversalPreAuthEvTag, "transmission date is invalid")
		recorder.RecordResponseCode(ctx, logic.ErrInvalidTransmissionDateTime.Code)
		errResp := toPaynetReversalPreAuthErrorResponse(req, &logic.ErrInvalidTransmissionDateTime)
		e.publishTransactionRecord(ctx, req, errResp)

		return errResp, nil
	}
	txn, err := logic.DetectPaynetReversalPreAuthTxnCategory(txnInfo.ProcessingCode, txnInfo.PosConditionCode, txnInfo.PosEntryMode)
	if err != nil {
		slog.FromContext(ctx).Error(paynetReversalPreAuthEvTag, "unable to detect paynet transaction")
		recorder.RecordResponseCode(ctx, err.Code)
		errResp := toPaynetReversalPreAuthErrorResponse(req, &logic.ErrSystemMalfunction)
		e.publishTransactionRecord(ctx, req, errResp)

		return errResp, nil
	}
	recorder.RecordTransactionCategoryValue(ctx, string(txn.TransactionCategory))
	recorder.RecordTransactionCategoryValue(ctx, string(txn.TransactionSubCategory))

	reversalResp, revErr := e.PaynetAuthorizeWorkflow.ExecuteReversalPreAuthWorkflow(ctx, req)
	if revErr != nil {
		slog.FromContext(ctx).Error(paynetReversalPreAuthEvTag, "handling PaynetReversalPreAuth failed due to errors from workflow", slog.Error(revErr))
		recorder.RecordResponseCode(ctx, logic.ErrSystemMalfunction.Code)
		errResp := toPaynetReversalPreAuthErrorResponse(req, &logic.ErrSystemMalfunction)
		e.publishTransactionRecord(ctx, req, errResp)
		return errResp, nil
	}
	e.publishTransactionRecord(ctx, req, reversalResp)

	return reversalResp, nil
}

func validatePaynetReversalPreAuthRequest(ctx context.Context, req *api.PaynetReversalPreAuthRequest, config *config.CountryConfig) *logic.AdapterError {
	if req == nil || req.TransactionReversalPreAuthRequest == nil {
		return &logic.ErrInvalidRequest
	}
	if err := txnAppHeadersValidation(ctx, req.TransactionReversalPreAuthRequest.AppHeader, dto.TxnReversal, config); err != nil {
		return err
	}
	txInfo := req.TransactionReversalPreAuthRequest.TransactionInfo
	declineInfo := req.TransactionReversalPreAuthRequest.DeclineInfo
	if txInfo == nil || declineInfo == nil {
		return &logic.ErrInvalidRequest
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.RRNTag, txInfo.RetrievalReferenceNumber),
		slog.CustomTag(logic.ProxyNumberTag, txInfo.ProxyNumber),
		slog.CustomTag(logic.OrigTransactionIDTag, txInfo.OrigTransactionID))
	slog.FromContext(ctx).Info(paynetReversalPreAuthEvTag, "received reversal pre-auth request")
	if err := validatePaynetReversalSettledMti(txInfo.Mti); err != nil {
		return err
	}
	strValidators := []stringValidator{
		{field: txInfo.ProxyNumber, max: 20, error: &logic.ErrInvalidPaynetProxyNumber},
		{field: txInfo.SystemTraceAuditNumber, max: 6, error: &logic.ErrInvalidPaynetSystemTraceAuditNumber},
		{field: txInfo.MerchantCategoryCode, max: 4, error: &logic.ErrInvalidPaynetMerchantCategoryCode},
		{field: txInfo.PosEntryMode, max: 3, error: &logic.ErrInvalidPaynetPosEntryMode},
		{field: txInfo.PosConditionCode, max: 3, error: &logic.ErrInvalidPaynetPosConditionCode},
		{field: txInfo.NetworkID, max: 3, error: &logic.ErrInvalidPaynetNetworkID},
		{field: txInfo.RetrievalReferenceNumber, max: 12, error: &logic.ErrInvalidPaynetRetrievalReferenceNumber},
		{field: txInfo.CardAcceptorTermName, max: 40, error: &logic.ErrInvalidPaynetCardAcceptorTermName},
		{field: txInfo.TransactionCurrencyCode, max: 3, error: &logic.ErrInvalidPaynetTransactionCurrencyCode},
		{field: txInfo.CardHolderCurrencyCode, max: 3, error: &logic.ErrInvalidPaynetCardHolderCurrencyCode},
		{field: txInfo.OrigTransactionID, max: 32, error: &logic.ErrInvalidPaynetOrigTransactionID},
		{field: txInfo.PANValidation, max: 1, error: &logic.ErrInvalidPaynetPANValidation},
		{field: txInfo.ExpiryDateValidation, max: 1, error: &logic.ErrInvalidPaynetExpiryDateValidation},
		{field: txInfo.LocalTransactionDateTime, max: 10, error: &logic.ErrInvalidPaynetLocalTransactionDateTime},
		{field: txInfo.ProcessingCode, max: 6, error: &logic.ErrInvalidPaynetProcessingCode},
		{field: declineInfo.DeclineCode, max: 3, error: &logic.ErrMissingDeclineCode},
		{field: declineInfo.DeclineReason, max: 40, error: &logic.ErrMissingDeclineReason},
	}
	amountValidators := []integerValidator{
		{field: txInfo.TransactionAmount, error: &logic.ErrInvalidPaynetTransactionAmount},
	}
	if err := bodyValidation(strValidators, amountValidators); err != nil {
		return err
	}

	// paynet reversal pre-auth api should only accept enquiry from MYDEBIT network
	if txInfo.NetworkID != constant.NETWORK_MYDEBIT {
		return &logic.ErrMissingNetworkID
	}

	return nil
}

func toPaynetReversalPreAuthErrorResponse(req *api.PaynetReversalPreAuthRequest, err *logic.AdapterError) *api.PaynetReversalPreAuthResponse {
	resp := &api.PaynetReversalPreAuthResponse{
		TransactionReversalPreAuthResponse: &api.PaynetReversalPreAuthResponse_TransactionReversalPreAuthResponse{},
	}

	if req != nil && req.TransactionReversalPreAuthRequest != nil {
		reqTxInfo := req.TransactionReversalPreAuthRequest.TransactionInfo
		resp.TransactionReversalPreAuthResponse.AppHeader = req.TransactionReversalPreAuthRequest.AppHeader
		resp.TransactionReversalPreAuthResponse.TransactionInfo = &api.PaynetReversalPreAuthResponse_ReversalPreAuthRes{
			Mti:                      logic.ConvertMtiPaynetRequestToResponse(reqTxInfo.Mti),
			ProxyNumber:              reqTxInfo.ProxyNumber,
			TransactionAmount:        reqTxInfo.TransactionAmount,
			TransmissionDateTime:     reqTxInfo.TransmissionDateTime,
			SystemTraceAuditNumber:   reqTxInfo.SystemTraceAuditNumber,
			LocalTransactionDateTime: reqTxInfo.LocalTransactionDateTime,
			MerchantCategoryCode:     reqTxInfo.MerchantCategoryCode,
			AcquirerCountryCode:      reqTxInfo.AcquirerCountryCode,
			AcquirerID:               reqTxInfo.AcquirerFIID,
			RetrievalReferenceNumber: reqTxInfo.RetrievalReferenceNumber,
			AuthorizationID:          reqTxInfo.AuthorizationID,
			CardAcceptorTerminalID:   reqTxInfo.CardAcceptorTerminalID,
			CardAcceptorTermName:     reqTxInfo.CardAcceptorTermName,
			TransactionCurrencyCode:  reqTxInfo.TransactionCurrencyCode,
			CardHolderCurrencyCode:   reqTxInfo.CardHolderCurrencyCode,
			TransactionID:            reqTxInfo.TransactionID,
			OrigTransactionID:        reqTxInfo.OrigTransactionID,
			ProcessingCode:           reqTxInfo.ProcessingCode,
		}
		resp.TransactionReversalPreAuthResponse.Response = &api.PaynetReversalPreAuthResponse_Response{
			ResponseCode: err.Code,
			Description:  err.Message,
		}
	}

	return resp
}
