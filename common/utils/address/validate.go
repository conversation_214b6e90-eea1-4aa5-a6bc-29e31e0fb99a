package address

import (
	"regexp"

	customerMaster "gitlab.myteksi.net/dakota/customer-master/api/v2"
)

// IsMailingAddressPostalCodeValid validates if the given customer master mailing address postal code
// matches the given regex. It will automatically find the MAILING address type in the array
// Returns false if no mailing address present
func IsMailingAddressPostalCodeValid(addresses []*customerMaster.Address, regex string) bool {
	var mailingAddress string
	mailingAddressRegex := regexp.MustCompile(regex)
	// Only will contain one MAILING address type
	for _, address := range addresses {
		if *address.AddressType == customerMaster.AddressType_Mailing {
			mailingAddress = *address.PostalCode
		}
	}
	return mailingAddressRegex.MatchString(mailingAddress)
}

// ValidateMailingAddressCountry validates if the given customer master mailing address country
// matches the given regex . It will automatically find the MAILING address type in the array
// Returns false if no mailing address present
func ValidateMailingAddressCountry(addresses []*customerMaster.Address, regex string) bool {
	var country string
	countryRegex := regexp.MustCompile(regex)
	// Only will contain one MAILING address type
	for _, address := range addresses {
		if *address.AddressType == customerMaster.AddressType_Mailing {
			country = *address.Country
		}
	}
	return countryRegex.MatchString(country)
}
