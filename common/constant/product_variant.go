package constant

// ProductVariant ...
type ProductVariant string

// ProductConfig ...
type ProductConfig struct {
	ProductVariant          ProductVariant
	TxnDomain               TxnDomain
	CardIssuanceConfig      CardIssuance
	TxnLimitNames           TxnLimits
	VendorIdentifier        string
	NotificationProductName string
	TncAgreementID          string
	PhysicalCardOrderFee    int64
	ChargeWorkflowID        string
	ChargeGroupTag          string
}

type CardIssuance struct {
	PhysicalCardOrderFeeName string
	ChargeWorkflowID         string
	ChargeGroupTag           string
	HoldingAccountID         string
	ChargeWorkflowLogTag     string
	TransactionDomain        string
	TransactionType          string
	TransactionSubType       string
}

type TxnLimits struct {
	Contactless string
	Online      string
	PinAndPay   string
	ATM         string
}

// ProductVariantComponents ...
type ProductVariantComponents struct {
	ProductVariantID string
}

// Product variants
var (
	// MYDebitCard -> CASA Account debit card
	MYDebitCard ProductVariant = "DEPOSITS_ACCOUNT"

	// MYCreditCard -> Credit card
	MYCreditCard ProductVariant = "commercial_credit_card"
)

const (
	// NotificationProductNameDebitCard ...
	NotificationProductNameDebitCard = "GXB Debit Card"
	// NotificationProductNameCreditCard ...
	NotificationProductNameCreditCard = "GXB Credit Card"
)

// ProductConfigs maps productVariant to card related configs
var ProductConfigs = map[ProductVariant]*ProductConfig{
	MYDebitCard: {
		ProductVariant:          MYDebitCard,
		TxnDomain:               DomainDebitCard,
		VendorIdentifier:        "MDP",
		NotificationProductName: NotificationProductNameDebitCard,
		TncAgreementID:          "termsAndConditions_Cards_Issuance", // TODO: To be updated after finalizing the value (From Customer Master)
		CardIssuanceConfig: CardIssuance{
			ChargeWorkflowID:         "card_issuance_charge",
			ChargeGroupTag:           string(CardIssuanceCharge),
			HoldingAccountID:         "*********",
			ChargeWorkflowLogTag:     "card_issuance_charge",
			TransactionDomain:        "DEBIT_CARD",
			TransactionType:          "NEW_CARD_ISSUANCE_FEE",
			TransactionSubType:       "BANK_INITIATED",
			PhysicalCardOrderFeeName: "debitCardFeeAmount",
		},
		TxnLimitNames: TxnLimits{
			Contactless: DebitCardTxnLimitContactless,
			Online:      DebitCardTxnLimitOnline,
			PinAndPay:   DebitCardTxnLimitPinAndPay,
			ATM:         DebitCardTxnLimitAtmWithdrawal,
		},
	},
	MYCreditCard: {
		ProductVariant:          MYCreditCard,
		TxnDomain:               DomainCreditCard,
		VendorIdentifier:        "CCM", //commercial credit card MasterCard CCM, commercial credit card Visa CCV
		NotificationProductName: NotificationProductNameCreditCard,
		TncAgreementID:          "termsAndConditions_CreditCard_Issuance", // TODO: To be updated after finalizing the value (From Customer Master)
		CardIssuanceConfig: CardIssuance{
			ChargeWorkflowID:         "credit_card_issuance_charge",
			ChargeGroupTag:           string(CreditCardIssuanceCharge),
			HoldingAccountID:         "",
			ChargeWorkflowLogTag:     "credit_card_issuance_charge",
			TransactionDomain:        "CREDIT_CARD",
			TransactionType:          "NEW_CREDIT_CARD_ISSUANCE_FEE",
			TransactionSubType:       "BANK_INITIATED",
			PhysicalCardOrderFeeName: "creditCardFeeAmount",
		},
		TxnLimitNames: TxnLimits{
			Contactless: CreditCardTxnLimitContactless,
			Online:      CreditCardTxnLimitOnline,
			PinAndPay:   CreditCardTxnLimitPinAndPay,
			ATM:         CreditCardTxnLimitAtmWithdrawal,
		},
	},
}

// --------------------
// Helpers
// --------------------

// ToProductVariant ...
func ToProductVariant(comps ProductVariantComponents) ProductVariant {
	return ProductVariant(comps.ProductVariantID)
}

// ToProductVariantComps ...
func ToProductVariantComps(pVariant ProductVariant) ProductVariantComponents {
	return ProductVariantComponents{
		ProductVariantID: string(pVariant),
	}
}

// GetProductConfig returns *ProductConfig of pVariant
// if not found, nil will be returned
func GetProductConfig(pVariant ProductVariant) *ProductConfig {
	value := ProductConfigs[pVariant]
	return value
}

func GetLimitName(pVariant ProductVariant) TxnLimits {
	productConfig := GetProductConfig(pVariant)
	return productConfig.TxnLimitNames
}

// GetNotificationProductName return the type of the card as a string, currently used in notification context
func GetNotificationProductName(pVariant ProductVariant) string {
	productConfig := GetProductConfig(pVariant)
	if productConfig == nil {
		return ""
	}
	return productConfig.NotificationProductName
}
