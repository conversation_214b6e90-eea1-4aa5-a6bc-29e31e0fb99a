package dao

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"sync/atomic"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/gophers/go/commons/data"
	"gitlab.myteksi.net/gophers/go/commons/util/log/logging"
)

// GetDatabaseHandle ...
func GetDatabaseHandle(ctx context.Context, config *data.MysqlConfig) (*sql.DB, error) {
	atomic.AddInt64(&config.PendingCalls, 1)
	defer atomic.AddInt64(&config.PendingCalls, -1)
	dbs, err := getDatabase(config)
	if err != nil {
		slog.FromContext(ctx).Warn(constant.GetDatabaseHandleLogTag, fmt.Sprintf("GetDatabaseHandle error: %s", err.Error()))
		return nil, err
	}
	return dbs, nil
}

func getDB(config *data.MysqlConfig) *sql.DB {
	return config.DBCache.DB
}

func setDB(config *data.MysqlConfig, db *sql.DB) {
	config.DBCache.DB = db
}

// getDatabase returns a connection the database (either real or mocked) depending on the ENV('Mode') variable.
var getDatabase = func(config *data.MysqlConfig) (db *sql.DB, err error) {
	defer panicRecovery("commons.data.getDatabase")
	connect := func() {
		db, err = createDB(config)
	}
	config.ConnectOnce.Do(connect)
	if err != nil {
		logging.Fatal(constant.TransactionConnectionLogTag, "createDB error: %v", err)
		return nil, err
	}
	if db != nil {
		// statsd.Count("mysql_functions.getDatabase", 1, "type:new", "mysqlHost:"+hostname())
		return db, nil
	}

	// check for existing connection
	db = getDB(config)
	if db == nil {
		return nil, errors.New("failed to connect to DB")
	}
	return db, nil
}

var createDB = func(config *data.MysqlConfig) (db *sql.DB, err error) {
	db, err = sql.Open("mysql", config.Dsn)
	if err != nil {
		logging.Error(constant.TransactionConnectionLogTag, "Failed to open database connection. Error:", err)
		return
	}
	db.SetMaxIdleConns(config.MaxIdle)
	db.SetMaxOpenConns(config.MaxOpen)
	if (config.ConnMaxLifetime.Duration) > 0 {
		db.SetConnMaxLifetime(config.ConnMaxLifetime.Duration)
	}

	logging.Info(constant.TransactionConnectionLogTag, "Opened db connection to %s", config.String())
	// store connection for later reuse
	setDB(config, db)
	return
}

// panicRecovery recovers from a panic happened during specified function
func panicRecovery(tag string) {
	if r := recover(); r != nil {
		logging.Info(constant.TransactionConnectionLogTag, "[%s] Recovered from panic. Error: %s", tag, r)
	}
}
