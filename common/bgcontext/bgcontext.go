// Package bgcontext ...
package bgcontext

import (
	"context"
)

type bgContext struct {
	context.Context
	valueCtx context.Context
}

// BackgroundWithValue creates a new Background context with value from valueCtx. This is useful for creating
// context for use in goroutine that has different lifecycle from the server Handler
func BackgroundWithValue(valueCtx context.Context) context.Context {
	return &bgContext{context.Background(), valueCtx}
}

// Value returns the value contained in the valueCtx
func (c *bgContext) Value(key interface{}) interface{} {
	return c.valueCtx.Value(key)
}
