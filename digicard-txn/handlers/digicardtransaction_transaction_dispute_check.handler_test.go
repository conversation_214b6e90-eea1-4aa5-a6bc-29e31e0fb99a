package handlers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/dispute/validatedispute"
)

func TestDigicardTransactionService_TransactionDisputeCheck(t *testing.T) {
	testCustomerID := "testID"
	testTxnID := "testTxnID"
	mockValidateDisputeClient := validatedispute.MockClient{}
	tests := []struct {
		name     string
		mockFunc func()
		req      *api.TransactionDisputeCheckRequest
		resp     *api.TransactionDisputeCheckResponse
		err      error
	}{
		{
			name: "happy path - transaction disputable",
			mockFunc: func() {
				mockValidateDisputeClient.On("IsTxnDisputable", mock.Anything, mock.Anything, mock.Anything).Return(true, nil).Once()
			},
			req: &api.TransactionDisputeCheckRequest{
				CustomerID:    testCustomerID,
				TransactionID: testTxnID,
			},
			resp: &api.TransactionDisputeCheckResponse{IsTransactionDisputable: true},
		},
		{
			name: "happy path - transaction not disputable",
			mockFunc: func() {
				mockValidateDisputeClient.On("IsTxnDisputable", mock.Anything, mock.Anything, mock.Anything).Return(false, nil).Once()
			},
			req: &api.TransactionDisputeCheckRequest{
				CustomerID:    testCustomerID,
				TransactionID: testTxnID,
			},
			resp: &api.TransactionDisputeCheckResponse{IsTransactionDisputable: false},
		},
		{
			name: "some error",
			mockFunc: func() {
				mockValidateDisputeClient.On("IsTxnDisputable", mock.Anything, mock.Anything, mock.Anything).Return(false, api.DefaultInternalServerError).Once()
			},
			req: &api.TransactionDisputeCheckRequest{
				CustomerID:    testCustomerID,
				TransactionID: testTxnID,
			},
			resp: nil,
			err:  api.DefaultInternalServerError,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			d := &DigicardTransactionService{
				ValidateDisputeClient: &mockValidateDisputeClient,
			}
			tt.mockFunc()
			got, err := d.TransactionDisputeCheck(context.Background(), tt.req)
			if tt.err != nil {
				assert.Nil(t, got)
				assert.Equal(t, tt.err, err)
			} else {
				assert.Nil(t, err)
				assert.Equal(t, got, tt.resp)
			}
		})
	}
}

func Test_validateTransactionDisputeCheckRequest(t *testing.T) {
	testCustomerID := "testCustomerID"
	testTxnID := "testTxnID"
	tests := []struct {
		name string
		req  *api.TransactionDisputeCheckRequest
		err  error
	}{
		{
			name: "happy path",
			req: &api.TransactionDisputeCheckRequest{
				CustomerID:    testCustomerID,
				TransactionID: testTxnID,
			},
			err: nil,
		},
		{
			name: "missing customerID",
			req: &api.TransactionDisputeCheckRequest{
				CustomerID:    "",
				TransactionID: testTxnID,
			},
			err: logic.BuildErrorResponse(api.BadRequest, "CustomerID and Transaction ID is mandatory"),
		},
		{
			name: "missing txnID",
			req: &api.TransactionDisputeCheckRequest{
				CustomerID:    testCustomerID,
				TransactionID: "",
			},
			err: logic.BuildErrorResponse(api.BadRequest, "CustomerID and Transaction ID is mandatory"),
		},

		{
			name: "nil request",
			req:  nil,
			err:  logic.BuildErrorResponse(api.BadRequest, "Invalid Request"),
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			err := validateTransactionDisputeCheckRequest(tt.req)
			assert.Equal(t, tt.err, err, tt.name)
		})
	}
}
