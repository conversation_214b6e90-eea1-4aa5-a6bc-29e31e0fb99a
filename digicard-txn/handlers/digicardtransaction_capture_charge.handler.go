package handlers

import (
	context "context"
	"net/http"

	api "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction/charge"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
)

var (
	workflowResponseToChargeResponse = charge.WorkflowResponseToChargeResponse
)

// CaptureCharge ...
func (d *DigicardTransactionService) CaptureCharge(ctx context.Context, req *api.CaptureChargeRequest) (*api.ChargeResponse, error) {
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.IdempotencyKeyHeader, req.IdempotencyKey),
		slog.CustomTag(logic.VendorChargeIDTag, req.Id))

	if errors := captureChargeRequestValidator(req); len(errors) != 0 {
		slog.FromContext(ctx).Warn(logCharge, "Error in validating capture charge request body")
		return nil, logic.CustomError(http.StatusBadRequest, errors)
	}

	chargeData, err := transaction.LoadTransactionByChargeID(ctx, req.Id)
	if err != nil {
		if err == data.ErrNoData {
			message := "The requested charge transaction resource is not found."
			return nil, logic.BuildErrorResponse(api.ResourceNotFound, message)
		}
		return nil, logic.ErrGenericServer
	}

	if chargeData.CaptureMethod != string(dto.Manual) {
		return nil, api.ErrorWrongCaptureMode
	}

	// check idempotency
	properties := dto.GetProperties(chargeData)
	if properties.CaptureIdempotencyKey == req.IdempotencyKey {
		response := dto.ChargeStorageToAPIResponse(chargeData)
		return response, nil
	}

	workflowImpl := d.ChargeWorkflow
	captureCharge := &dto.ChargeManualData{
		VendorChargeID:        req.Id,
		IdempotencyKey:        req.IdempotencyKey,
		Amount:                req.AmountParams,
		ChargeStorage:         chargeData,
		IsCapture:             true,
		IsPartialCapture:      req.IsPartialCapture,
		AcquirerID:            req.AcquirerID,
		AcquirerReferenceData: req.AcquirerReferenceData,
		PostingSource:         req.PostingSource,
	}
	capturedWorkflow, err := workflowImpl.CaptureOrCancelWorkflow(ctx, captureCharge)
	if err != nil {
		return nil, err
	}

	result, err := workflowResponseToChargeResponse(ctx, capturedWorkflow)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func captureChargeRequestValidator(req *api.CaptureChargeRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	captureChargeRequestBodyValidator(req, &errs)
	return errs
}

func captureChargeRequestBodyValidator(req *api.CaptureChargeRequest, errs *[]servus.ErrorDetail) {
	if req.IdempotencyKey == "" {
		*errs = append(*errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "'idempotencyKey' is a mandatory field.",
			Path:      "idempotencyKey",
		})
	}
	if req.AmountParams == nil {
		*errs = append(*errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "'amountParams' is a mandatory field.",
			Path:      "amountParams",
		})
	} else {
		if req.AmountParams.RequestAmount <= 0 {
			*errs = append(*errs, servus.ErrorDetail{
				ErrorCode: string(api.FieldInvalid),
				Message:   "'requestAmount' is a mandatory non-zero and non-negative field.",
				Path:      "requestAmount",
			})
		}
		if req.AmountParams.RequestCurrency == "" {
			*errs = append(*errs, servus.ErrorDetail{
				ErrorCode: string(api.FieldInvalid),
				Message:   "'requestCurrency' is a mandatory field.",
				Path:      "requestCurrency",
			})
		}
	}
}
