package handlers

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction/refund"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/server/config"
	//"gitlab.myteksi.net/gophers/go/commons/grabtesting/testtools"
)

func TestRefundHandler(t *testing.T) {
	//defer testtools.Restore(testtools.Pairs(&createRefund, &workflowToRefundResponse)...)
	errDummy := errors.New("simulate error")
	testIdempotencyKey := uuid.NewString()
	testcardID := uuid.NewString()
	testChargeID := uuid.NewString()
	defaultRefundRequest := &api.RefundRequest{
		CardID:         testcardID,
		RefundAmount:   100,
		ChargeID:       testChargeID,
		IdempotencyKey: testIdempotencyKey,
	}
	defaultRefundResponse := &api.RefundResponse{
		Data: &api.RefundResource{
			RefundStatus: "PROCESSING",
		},
	}
	scenarios := []struct {
		refundReq      *api.RefundRequest
		refundResp     *api.RefundResponse
		errCreate      error
		errGet         error
		errConvert     error
		expectedStatus string
		expectedErr    error
	}{
		{
			refundReq:   defaultRefundRequest,
			errCreate:   errDummy,
			expectedErr: errDummy,
			refundResp:  defaultRefundResponse,
		},
		{
			refundReq:   defaultRefundRequest,
			errGet:      errDummy,
			expectedErr: errDummy,
			refundResp:  defaultRefundResponse,
		},
		{
			refundReq:   defaultRefundRequest,
			errConvert:  errDummy,
			expectedErr: errDummy,
			refundResp:  defaultRefundResponse,
		},
		{
			refundReq: defaultRefundRequest,
			refundResp: &api.RefundResponse{
				Data: &api.RefundResource{
					RefundStatus: "PROCESSING",
				},
			},
			expectedStatus: "PROCESSING",
		},
		{
			refundReq: defaultRefundRequest,
			refundResp: &api.RefundResponse{
				Data: &api.RefundResource{
					RefundStatus: "COMPLETED",
				},
			},
			expectedStatus: "COMPLETED",
		},
	}

	for index, scenario := range scenarios {
		description := fmt.Sprintf("test case #%d", index)
		testcase := scenario
		createRefund = func(ctx context.Context, req *api.RefundRequest, refundWorkflow refund.Workflow) (*api.RefundResponse, error) {
			return testcase.refundResp, testcase.errCreate
		}
		mockRefundWorkflow := &refund.MockWorkflow{}
		mockRefundWorkflow.On("GetWorkflow", mock.Anything, mock.Anything).Return(&dto.WorkflowResponse{
			Status: testcase.expectedStatus,
		}, testcase.errGet)
		testService := &DigicardTransactionService{
			RefundWorkflow: mockRefundWorkflow,
			TimeLimitConfig: &config.TimeLimitConfig{
				Sync: 2,
			},
		}
		workflowToRefundResponse = func(ctx context.Context, workflowResponse *dto.WorkflowResponse) (*api.RefundResponse, error) {
			return testcase.refundResp, testcase.errConvert
		}

		resp, err := testService.RefundTransaction(context.Background(), testcase.refundReq)
		if scenario.expectedErr != nil {
			assert.Error(t, err, description)
			assert.Equal(t, testcase.expectedErr, err, description)
			assert.Nil(t, resp, description)
		} else {
			assert.NoError(t, err, description)
			assert.NotNil(t, resp, description)
			assert.Equal(t, testcase.expectedStatus, resp.Data.RefundStatus, description)
		}
	}
}
