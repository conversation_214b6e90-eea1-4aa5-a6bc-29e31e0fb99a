package handlers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	createdispute "gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/dispute/create"
	"gitlab.myteksi.net/dakota/servus/v2"
)

func TestValidateRequest(t *testing.T) {
	t.Run("Valid flow", func(t *testing.T) {
		err := validateRequest(&api.CreateDisputeRequest{
			CustomerID:                  "1",
			TransactionID:               "1",
			ChargeBackStatusDescription: "Bank Review",
			ChargeBackStatusCode:        "02",
			DisputeReasonCode:           "Sample Reason Code",
		})
		assert.Nil(t, err)
	})
	t.Run("When empty request", func(t *testing.T) {
		err := validateRequest(nil)
		errorCode := api.BadRequest
		expectedErr := servus.ServiceError{
			Code:     string(errorCode),
			Message:  "request has invalid parameter(s).",
			HTTPCode: errorCode.HTTPStatusCode(),
		}
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("When empty request", func(t *testing.T) {
		err := validateRequest(nil)
		errorCode := api.BadRequest
		expectedErr := servus.ServiceError{
			Code:     string(errorCode),
			Message:  "request has invalid parameter(s).",
			HTTPCode: errorCode.HTTPStatusCode(),
		}
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("When Request does not have any data", func(t *testing.T) {
		err := validateRequest(&api.CreateDisputeRequest{})
		errorCode := api.BadRequest

		var errs []servus.ErrorDetail

		emptyFieldValidator("", "customerID", &errs)
		emptyFieldValidator("", "transactionID", &errs)
		emptyFieldValidator("", "chargeBackStatusCode", &errs)
		emptyFieldValidator("", "chargeBackStatusDescription", &errs)
		emptyFieldValidator("", "disputeReasonCode", &errs)

		expectedErr := servus.ServiceError{
			Code:     string(errorCode),
			Message:  "request has invalid parameter(s) or header(s).",
			HTTPCode: errorCode.HTTPStatusCode(),
			Errors:   errs,
		}
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
}

func TestCreateDispute(t *testing.T) {
	t.Run("Valid flow", func(t *testing.T) {
		req := &api.CreateDisputeRequest{
			CustomerID:                  "1",
			TransactionID:               "1",
			ChargeBackStatusDescription: "Bank Review",
			ChargeBackStatusCode:        "02",
			DisputeReasonCode:           "Sample Reason Code",
		}
		createDisputeClient := &createdispute.MockClient{}
		service := DigicardTransactionService{CreateDisputeClient: createDisputeClient}
		createDisputeClient.On("CreateDispute", mock.Anything, mock.Anything).Return(&api.CreateDisputeResponse{}, nil)
		resp, err := service.CreateDispute(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, resp)
	})

	t.Run("Request validation fail", func(t *testing.T) {
		req := &api.CreateDisputeRequest{}
		createDisputeClient := &createdispute.MockClient{}
		service := DigicardTransactionService{CreateDisputeClient: createDisputeClient}
		createDisputeClient.On("CreateDispute", mock.Anything, mock.Anything).Return(&api.CreateDisputeResponse{}, nil)
		resp, err := service.CreateDispute(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})
}
