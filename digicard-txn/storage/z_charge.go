package storage

// Code generated .* DO NOT EDIT.

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

var chargeDao = "charge_dao"

// GetID implements the GetID function for Entity Interface
func (impl *Charge) GetID() string {
	// if field ID missing, define it inside entity
	return strconv.FormatUint(impl.ID, 10)
}

// SetID implements the SetID function for Entity Interface
func (impl *Charge) SetID(ID string) {
	// replace the logic to populate unique ID for Charge
	idInt, err := strconv.ParseUint(ID, 10, 64)
	if err != nil {
		return
	}
	// if field ID missing, define it inside entity
	impl.ID = idInt
}

// NewEntity implements the NewEntity function for Entity Interface
func (impl *Charge) NewEntity() data.Entity {
	return &Charge{}
}

// GetTableName implements the GetTableName for MysqlData interface
func (impl *Charge) GetTableName() string {
	return "charge"
}

// IChargeDAO is the dao interface for Charge
//
//go:generate mockery --name IChargeDAO --inpackage --case=underscore
type IChargeDAO interface {
	// LoadByID ...
	LoadByID(ctx context.Context, ID uint64, fields ...string) (*Charge, error)

	// LoadByIDOnSlave ...
	LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*Charge, error)

	// Save ...
	Save(ctx context.Context, newData *Charge) error

	// SaveBatch ...
	SaveBatch(ctx context.Context, newData []*Charge) error

	// Update ...
	Update(ctx context.Context, newData *Charge) error

	// UpdateEntity ...
	UpdateEntity(ctx context.Context, preData *Charge, newData *Charge) error

	// Find ...
	Find(ctx context.Context, conditions ...data.Condition) ([]*Charge, error)

	// FindOnSlave ...
	FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*Charge, error)

	// Upsert ...
	Upsert(ctx context.Context, newData *Charge) error
}

// ChargeDAO ...
type ChargeDAO struct {
	data.DAO
	StatsD statsd.Client
}

// NewChargeDAO creates a data access object for Charge
func NewChargeDAO(cfg *servus.DataConfig, statsDClient statsd.Client) *ChargeDAO {
	if statsDClient == nil {
		statsDClient = statsd.NewNoop()
	}
	return &ChargeDAO{
		DAO:    data.NewMysqlDAO(cfg.MySQL, &Charge{}),
		StatsD: statsDClient,
	}
}

// LoadByID ...
func (dao *ChargeDAO) LoadByID(ctx context.Context, ID uint64, fields ...string) (*Charge, error) {
	methodName := "load_by_id"
	defer dao.StatsD.Duration(chargeDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByID(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(chargeDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*Charge), err
}

// LoadByIDOnSlave ...
func (dao *ChargeDAO) LoadByIDOnSlave(ctx context.Context, ID uint64, fields ...string) (*Charge, error) {
	methodName := "load_by_id_on_slave"
	defer dao.StatsD.Duration(chargeDao, methodName, time.Now())
	entity, err := dao.DAO.LoadByIDOnSlave(ctx, strconv.FormatUint(ID, 10), fields...)
	if err != nil {
		dao.StatsD.Count1(chargeDao, methodName, fmt.Sprintf("error:%s", err))
		return nil, err
	}
	return entity.(*Charge), err
}

// Save ...
func (dao *ChargeDAO) Save(ctx context.Context, entity *Charge) error {
	methodName := "save"
	defer dao.StatsD.Duration(chargeDao, methodName, time.Now())
	err := dao.DAO.Save(ctx, entity)
	if err != nil {
		dao.StatsD.Count1(chargeDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// SaveBatch ...
func (dao *ChargeDAO) SaveBatch(ctx context.Context, entities []*Charge) error {
	methodName := "save_batch"
	defer dao.StatsD.Duration(chargeDao, methodName, time.Now())
	dataEntities := make([]data.Entity, 0, len(entities))
	for _, e := range entities {
		dataEntities = append(dataEntities, e)
	}
	err := dao.DAO.SaveBatch(ctx, dataEntities)
	if err != nil {
		dao.StatsD.Count1(chargeDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Update ...
func (dao *ChargeDAO) Update(ctx context.Context, data *Charge) error {
	methodName := "update"
	defer dao.StatsD.Duration(chargeDao, methodName, time.Now())
	err := dao.DAO.Update(ctx, data)
	if err != nil {
		dao.StatsD.Count1(chargeDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// UpdateEntity ...
func (dao *ChargeDAO) UpdateEntity(ctx context.Context, preData *Charge, newData *Charge) error {
	methodName := "update_entity"
	defer dao.StatsD.Duration(chargeDao, methodName, time.Now())
	err := dao.DAO.UpdateEntity(ctx, preData, newData)
	if err != nil {
		dao.StatsD.Count1(chargeDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}

// Find ...
func (dao *ChargeDAO) Find(ctx context.Context, conditions ...data.Condition) ([]*Charge, error) {
	methodName := "find"
	defer dao.StatsD.Duration(chargeDao, methodName, time.Now())
	foundEntities, err := dao.DAO.Find(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(chargeDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*Charge, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*Charge))
	}
	return foundObjects, err
}

// FindOnSlave ...
func (dao *ChargeDAO) FindOnSlave(ctx context.Context, conditions ...data.Condition) ([]*Charge, error) {
	methodName := "find_on_slave"
	defer dao.StatsD.Duration(chargeDao, methodName, time.Now())
	foundEntities, err := dao.DAO.FindOnSlave(ctx, conditions...)
	if err != nil {
		dao.StatsD.Count1(chargeDao, methodName, fmt.Sprintf("error:%s", err))
	}
	foundObjects := make([]*Charge, 0, len(foundEntities))
	for _, entity := range foundEntities {
		foundObjects = append(foundObjects, entity.(*Charge))
	}
	return foundObjects, err
}

// Upsert ...
func (dao *ChargeDAO) Upsert(ctx context.Context, data *Charge) error {
	methodName := "upsert"
	defer dao.StatsD.Duration(chargeDao, methodName, time.Now())
	err := dao.DAO.Upsert(ctx, data)
	if err != nil {
		dao.StatsD.Count1(chargeDao, methodName, fmt.Sprintf("error:%s", err))
	}
	return err
}
