-- Deploy transaction:0000-init to mysql

BEGIN;

CREATE TABLE `charge`
(
    `id`                        BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `type`                      VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Type of the charge transaction',
    `card_id`                   VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Card reference id of the customer',
    `customer_id`               VARCHAR(36) COMMENT 'Customer who initiates the transaction',
    `charge_id`                 CHAR(36)    NOT NULL COMMENT 'Charge ID generated internally to be sent to downstream, used for tracking purpose',
    `reference_id`              VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Idempotency Key given by the upstream',
    `status`                    VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Status of the charge transaction',
    `request_amount`            BIGINT      NOT NULL COMMENT 'Amount requested to transfer',
    `request_currency`          CHAR(3)     NOT NULL DEFAULT '' COMMENT 'Request currency of the transfer',
    `markup_amount`             BIGINT      NOT NULL COMMENT 'Additional charges involved in transfer',
    `original_amount`           BIGINT      NOT NULL COMMENT 'Original amount to transfer',
    `original_currency`         CHAR(3)     NOT NULL DEFAULT '' COMMENT 'Original currency of the transfer',
    `capture_amount`            BIGINT      NOT NULL COMMENT 'Amount captured once authorisation is done',
    `source_account_id`         VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Sender source digibank account id',
    `destination_account_id`    VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Destination source digibank account id',
    `capture_method`            VARCHAR(36)          DEFAULT '' COMMENT 'Option to indicate whether capture method is automatic or manual',
    `status_reason`             VARCHAR(50)          DEFAULT '' COMMENT 'Additional code to the status',
    `status_reason_description` VARCHAR(100)         DEFAULT '' COMMENT 'Additional description to the status code',
    `capture_mode`              VARCHAR(36)          DEFAULT '' COMMENT 'Capture mode of transfer, eg: Online, POS',
    `properties`                JSON COMMENT 'Additional required data',
    `metadata`                  JSON COMMENT 'Additional non-essential data to the transfer',
    `vendor_charge_timestamp`   DATETIME COMMENT 'Charge Transaction timestamp received from vendor',
    `merchant_description`      VARCHAR(100)         DEFAULT '' COMMENT 'Merchant description received from vendor',
    `merchant_id`               VARCHAR(100)         DEFAULT '',
    `mcc`                       CHAR(4)     NOT NULL DEFAULT '' COMMENT 'Merchant category code received from vendor',
    `vendor_charge_id`          VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'charge id received from vendor',
    `payment_transaction_id`            VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'transaction id received from downstream services',
    `transaction_domain`        VARCHAR(36)          DEFAULT '' COMMENT 'Txn code domain required to send downstream services',
    `transaction_type`          VARCHAR(36)          DEFAULT '' COMMENT 'Txn code type required to send downstream services',
    `transaction_subtype`       VARCHAR(36)          DEFAULT '' COMMENT 'Txn code subtype required to send downstream services',
    `dispute_list`              JSON COMMENT 'Dispute ID List',
    `reversal_amount`           BIGINT NOT NULL DEFAULT 0 COMMENT 'Reversal Amount',
    `reversal_currency`         CHAR(3) NOT NULL DEFAULT ''  COMMENT 'Reversal Currency Code',
    `reversal_timestamp`        DATETIME NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'Reversal Time stamp',
    `captured_amount_till_date` BIGINT NOT NULL DEFAULT '0',
    `created_at`                DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `valued_timestamp`          DATETIME,
    `updated_at`                DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_charge_id` (`charge_id`),
    UNIQUE KEY `uk_reference_id` (`reference_id`),
    KEY                         `index_vendor_charge_id` (`vendor_charge_id`),
    KEY                         `index_created_at` (`created_at`),
    KEY                         `index_valued_timestamp` (`valued_timestamp`),
    KEY                         `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;


CREATE TABLE `refund`
(
    `id`                        BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `type`                      VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Type of the refund transaction',
    `card_id`                   VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Card reference id of the customer',
    `refund_id`                 CHAR(36)    NOT NULL COMMENT 'Refund ID generated internally to be sent to downstream, used for tracking purpose',
    `status`                    VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Status of the refund transaction',
    `amount`                    BIGINT      NOT NULL COMMENT 'Amount requested to refund',
    `currency`                  CHAR(3)     NOT NULL DEFAULT '' COMMENT 'Currency of amount to refund',
    `remaining_amount`          BIGINT      NOT NULL COMMENT 'Remaining amount calculated from corresponding charge transaction',
    `status_reason`             VARCHAR(50)          DEFAULT '' COMMENT 'Additional code to the status',
    `status_reason_description` VARCHAR(100)         DEFAULT '' COMMENT 'Additional description to the status code',
    `metadata`                  JSON COMMENT 'Additional non-essential data to the transfer',
    `original_charge_id`        VARCHAR(36) NOT NULL COMMENT 'original charge id in charge table',
    `payment_transaction_id`    VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'payment transaction id received from downstream services',
    `transaction_domain`        VARCHAR(36)          DEFAULT '' COMMENT 'Txn code domain required to send downstream services',
    `transaction_type`          VARCHAR(36)          DEFAULT '' COMMENT 'Txn code type required to send downstream services',
    `transaction_subtype`       VARCHAR(36)          DEFAULT '' COMMENT 'Txn code subtype required to send downstream services',
    `created_at`                DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `valued_timestamp`          DATETIME,
    `updated_at`                DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `reason_code`               VARCHAR(255)  NOT NULL DEFAULT '' COMMENT 'reason code provided by OPS part of dispute refund',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_refund_id` (`refund_id`),
    KEY                         `index_original_charge_id` (`original_charge_id`),
    KEY                         `index_created_at` (`created_at`),
    KEY                         `index_valued_timestamp` (`valued_timestamp`),
    KEY                         `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `workflow_execution`
(
    `id`            BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `workflow_id`   VARCHAR(36) NOT NULL DEFAULT '' COMMENT 'Identifier of the workflow',
    `run_id`        CHAR(36)    NOT NULL DEFAULT '' COMMENT 'ID per workflow execution',
    `transition_id` CHAR(36)    NOT NULL DEFAULT '' COMMENT 'ID of current state transition',
    `prev_trans_id` CHAR(36)             DEFAULT '' COMMENT 'ID of previous state transition',
    `attempt`       INT         NOT NULL DEFAULT 0 COMMENT 'Attempt count per transition',
    `state`         INT         NOT NULL COMMENT 'state of the workflow',
    `data`          JSON        NOT NULL COMMENT 'data required for running workflow',
    `created_at`    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`    DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_run_id_workflow_id` (`run_id`, `workflow_id`) COMMENT 'Identifier for a specific execution',
    UNIQUE KEY `uk_transition_id` (`transition_id`) COMMENT 'Identifier for a transition within an execution',
    UNIQUE KEY `uk_prev_trans_id` (`prev_trans_id`) COMMENT 'Identifier for continuing an existing execution',
    INDEX           `index_state_updated_at` (`workflow_id`, `state`, `updated_at`) COMMENT 'For worker to retry failed transitions',
    KEY             `index_created_at` (`created_at`),
    KEY             `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `dispute`
(
    `id`                        BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
    `dispute_id`                CHAR(36) NOT NULL COMMENT 'Dispute Id generated internally',
    `customer_id`               VARCHAR(36) NOT NULL COMMENT 'Customer who initiates the dispute',
    `charge_id`            VARCHAR(36) NOT NULL COMMENT 'charge id received',
    `sequence_no`               BIGINT NOT NULL COMMENT 'sequence order of the disputes for the same transaction',
    `status_code`               CHAR(2) NOT NULL DEFAULT '' COMMENT 'Chargeback Status Code',
    `status_description`        VARCHAR(300) NOT NULL DEFAULT '' COMMENT 'Chargeback Status Description',
    `refund_id`                 VARCHAR(36) DEFAULT '' COMMENT 'Refund ID received',
    `refund_type`               VARCHAR(100) DEFAULT '' COMMENT 'Refund Type',
    `metadata`                  JSON COMMENT 'Additional non-essential data to the dispute',
    `waiver_reason`             varchar(300) DEFAULT '' COMMENT 'Reason of waiver',
    `dispute_reason_code`       varchar(300) NOT NULL DEFAULT '' COMMENT 'Dispute Reason code',
    `dispute_comments`          varchar(300) DEFAULT '' COMMENT 'Dispute Comments',
    `refund_at`                DATETIME     NOT NULL DEFAULT '0001-01-01 00:00:00' COMMENT 'Refunded time stamp',
    `created_at`                DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`                DATETIME    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_dispute_id` (`dispute_id`),
    KEY                         `index_created_at` (`created_at`),
    KEY                         `index_updated_at` (`updated_at`)
) DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

COMMIT;
