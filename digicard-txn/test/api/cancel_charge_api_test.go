package api

import (
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction/charge"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/workflowengine"
	weStorage "gitlab.myteksi.net/dakota/workflowengine/storage"
)

var _ = Describe("CancelCharge", func() {
	var (
		client   *hcl.Client
		setupErr error
	)

	BeforeEach(func() {
		client, setupErr = hcl.NewClient(server.URL())
		Expect(setupErr).ShouldNot(HaveOccurred())
		mockChargeStorage = &storage.MockIChargeDAO{}
		storage.ChargeD = mockChargeStorage
	})

	Describe("Calling cancel charge API with appropriate parameters", func() {
		apiURL := "/v1/charges/12343424/cancel"
		testIdempotencyKey := uuid.NewString()
		testCaptureMethod := string(dto.Manual)
		errDummy := errors.New("simulate error")
		defaultChargeData := []*storage.Charge{
			{
				CreationTimestamp: time.Now(),
				ValueTimestamp:    time.Now(),
				CaptureMethod:     string(dto.Manual),
				ReferenceID:       uuid.NewString(),
			},
		}

		Context("Invalid Request Parameters", func() {

			When("Idempotency key is missing in the body", func() {
				requestBody := dummyCancelChargeRequest("")
				idempotencyModifier := []hcl.RequestModifier{
					hcl.JSON(requestBody),
				}
				It("Returns 400", func() {
					resp, err := client.Post(apiURL, idempotencyModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))

					Expect(resp.Body.String).Should(MatchJSON(idempotencyErrResponse))
				})
			})
		})

		Context("Cancel Charge request with all valid parameters", func() {
			defaultBody := dummyCancelChargeRequest(testIdempotencyKey)
			defaultModifier := []hcl.RequestModifier{
				hcl.JSON(defaultBody),
			}

			When("Error from database", func() {
				It("Return 500", func() {
					mockChargeStorage.On("Find", mock.Anything, mock.Anything).Return(nil, errDummy).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(500))
					respObj := &servus.ServiceError{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Code).Should(Equal("INTERNAL_SERVER_ERROR"))
				})
			})

			When("Charge transaction is not found", func() {
				It("Return 404", func() {
					mockChargeStorage.On("Find", mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(404))
					respObj := &servus.ServiceError{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Code).Should(Equal("RESOURCE_NOT_FOUND"))
				})
			})

			When("Charge transaction is canceled already", func() {
				It("Return 409", func() {
					mockChargeStorage.On("Find", mock.Anything, mock.Anything).Return(defaultChargeData, nil).Once()
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyCancelChargeExecutionData(workflowengine.State(600), string(logic.Canceled), testCaptureMethod), nil).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(409))
					respObj := &servus.ServiceError{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Code).Should(Equal("ALREADY_CANCELED"))
				})
			})

			When("Charge transaction is not authorised yet", func() {
				It("Return 409", func() {
					mockChargeStorage.On("Find", mock.Anything, mock.Anything).Return(defaultChargeData, nil).Once()
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyCancelChargeExecutionData(workflowengine.State(220), string(logic.Processing), testCaptureMethod), nil).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(409))
					respObj := &servus.ServiceError{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Code).Should(Equal("NOT_AUTHORISED_YET"))
				})
			})
		})
	})
})

func dummyCancelChargeRequest(idempotencyKey string) *api.CancelChargeRequest {
	return &api.CancelChargeRequest{
		IdempotencyKey: idempotencyKey,
	}
}

func dummyCancelChargeExecutionData(state workflowengine.State, status string, captureMethod string) []*weStorage.WorkflowExecution {
	executeData := charge.ExecutionData{
		CreateCharge: &dto.CreateCharge{
			IdempotencyKey: uuid.NewString(),
			TransactionCode: &dto.TransactionCode{
				Domain:  dto.DebitCardDomain,
				Type:    dto.SpendRefundTransactionType,
				SubType: dto.MastercardTransactionSubtype,
			},
			AmountParams: &api.Amount{
				RequestAmount:   100,
				RequestCurrency: "SGD",
			},
			CaptureMethod: captureMethod,
		},
		Charge: storage.Charge{
			CardID:            uuid.NewString(),
			RequestAmount:     100,
			Status:            status,
			CreationTimestamp: time.Now(),
			ValueTimestamp:    time.Now(),
		},
		State: state,
	}
	byteData, _ := json.Marshal(executeData)
	return []*weStorage.WorkflowExecution{
		{
			WorkflowID: "workflow_charge",
			Data:       byteData,
			State:      0,
		},
	}
}
