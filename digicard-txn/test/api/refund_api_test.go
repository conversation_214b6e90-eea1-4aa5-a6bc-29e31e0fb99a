package api

import (
	"encoding/json"
	"errors"
	"time"

	"github.com/google/uuid"
	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction/refund"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/utils/cache"
	mockCache "gitlab.myteksi.net/dakota/common/redis/mocks"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	paymentcoreAPI "gitlab.myteksi.net/dakota/payment/payment-core/api"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/workflowengine"
	weStorage "gitlab.myteksi.net/dakota/workflowengine/storage"
	accountServiceAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

var _ = Describe("Refund", func() {
	var (
		client   *hcl.Client
		setupErr error
	)

	BeforeEach(func() {
		client, setupErr = hcl.NewClient(server.URL())
		Expect(setupErr).ShouldNot(HaveOccurred())
		mockChargeStorage = &storage.MockIChargeDAO{}
		mockRefundStorage = &storage.MockIRefundDAO{}
		storage.ChargeD = mockChargeStorage
		storage.RefundD = mockRefundStorage
		mockRedis = &mockCache.Client{}
		mockLock = &mockCache.Lock{}
		cache.RedisClient = mockRedis
	})

	Describe("Calling refund API with appropriate parameters", func() {
		apiURL := "/v1/refunds"
		testCardID := uuid.NewString()
		testChargeID := uuid.NewString()
		var testAmount int64 = 1000
		testIdempotencyKey := uuid.NewString()
		errDummy := errors.New("simulate error")

		Context("Invalid Request Parameters", func() {

			When("Idempotency key is missing in the body", func() {
				requestBody := dummyRefundRequest(testCardID, testChargeID, testAmount, "")
				idempotencyModifier := []hcl.RequestModifier{
					hcl.JSON(requestBody),
				}
				It("Returns 400", func() {
					resp, err := client.Post(apiURL, idempotencyModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					Expect(resp.Body.String).Should(MatchJSON(idempotencyErrResponse))
				})
			})

			When("CardID is missing in the body", func() {
				requestBody := dummyRefundRequest("", testChargeID, testAmount, testIdempotencyKey)
				requestModifier := []hcl.RequestModifier{
					hcl.JSON(requestBody),
				}
				It("Return 400", func() {
					resp, err := client.Post(apiURL, requestModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					expectedResponse := `{
										  "code": "INVALID_PARAMETERS",
										  "message": "request has invalid parameter(s) or header(s).",
										  "errors": [
											{
											  "errorCode": "FIELD_MISSING",
											  "message": "'cardID' is a mandatory field.",
											  "path": "cardID"
											}
										  ]
										}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})

			When("Amount is invalid", func() {
				requestBody := dummyRefundRequest(testCardID, testChargeID, 0, testIdempotencyKey)
				requestModifier := []hcl.RequestModifier{
					hcl.JSON(requestBody),
				}
				It("Return 400", func() {
					resp, err := client.Post(apiURL, requestModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					expectedResponse := `{
										  "code": "INVALID_PARAMETERS",
										  "message": "request has invalid parameter(s) or header(s).",
										  "errors": [
											{
											  "errorCode": "FIELD_MISSING",
											  "message": "'refundAmount' is a mandatory non-zero and non-negative field.",
											  "path": "refundAmount"
											}
										  ]
										}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})
		})

		Context("Refund request with all valid parameters", func() {
			defaultBody := dummyRefundRequest(testCardID, testChargeID, testAmount, testIdempotencyKey)
			defaultModifier := []hcl.RequestModifier{
				hcl.JSON(defaultBody),
			}

			When("Corresponding charge transaction is not found", func() {
				It("Return 404", func() {
					mockChargeStorage.On("Find", mock.Anything, mock.Anything).Return(
						[]*storage.Charge{
							{
								CardID: testCardID,
								Status: string(logic.Completed),
							},
						}, data.ErrNoData).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(404))
					expectedResponse := `{
										  "code": "RESOURCE_NOT_FOUND",
										  "message": "The requested charge transaction resource is not found."
										}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})

			When("CardID is not valid", func() {
				It("Return 400", func() {
					mockChargeStorage.On("Find", mock.Anything, mock.Anything).Return(
						[]*storage.Charge{
							{
								CardID: uuid.NewString(),
								Status: string(logic.Completed),
							},
						}, nil).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					expectedResponse := `{
										  "code": "INVALID_PARAMETERS",
										  "message": "request has invalid parameter(s) or header(s).",
										  "errors": [
											{
											  "errorCode": "VALUE_INVALID",
											  "message": "'cardID' is a invalid.",
											  "path": "cardID"
											}
										  ]
										}`
					Expect(resp.Body.String).Should(MatchJSON(expectedResponse))
				})
			})

			When("Error in obtaining lock from redis", func() {
				It("Return 500", func() {
					mockChargeStorage.On("Find", mock.Anything, mock.Anything).Return(
						[]*storage.Charge{
							{
								CardID: testCardID,
								Status: string(logic.Completed),
							},
						}, nil).Once()
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					mockWorkflowDAO.On("Save", mock.Anything, mock.Anything).Return(nil)
					mockWorkflowDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyExecutionData(2000, workflowengine.StateInit, string(logic.Processing)), nil).Once()
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(nil, errDummy).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(500))
					respObj := &servus.ServiceError{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Code).Should(Equal("INTERNAL_SERVER_ERROR"))
				})
			})

			When("Error from database", func() {
				It("Return 500", func() {
					mockChargeStorage.On("Find", mock.Anything, mock.Anything).Return(
						[]*storage.Charge{
							{
								CardID: testCardID,
								Status: string(logic.Completed),
							},
						}, nil).Once()
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					mockWorkflowDAO.On("Save", mock.Anything, mock.Anything).Return(nil)
					mockWorkflowDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyExecutionData(500, workflowengine.StateInit, string(logic.Processing)), nil).Once()
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					mockRefundStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Refund{
						{
							CardID: testCardID,
						},
					}, nil).Once()
					mockRefundStorage.On("Save", mock.Anything, mock.Anything).Return(data.ErrInvalidObject).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(500))
					respObj := &servus.ServiceError{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Code).Should(Equal("INTERNAL_SERVER_ERROR"))
				})
			})

			When("Error from payment core service", func() {
				It("Return 500", func() {
					mockChargeStorage.On("Find", mock.Anything, mock.Anything).Return(
						[]*storage.Charge{
							{
								CardID: testCardID,
								Status: string(logic.Completed),
							},
						}, nil).Once()
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					mockWorkflowDAO.On("Save", mock.Anything, mock.Anything).Return(nil)
					mockWorkflowDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyExecutionData(500, workflowengine.StateInit, string(logic.Processing)), nil).Once()
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					mockRefundStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Refund{
						{
							CardID: testCardID,
						},
					}, nil).Once()
					mockRefundStorage.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
						Id:     "123",
						Status: accountServiceAPI.AccountStatus_ACTIVE,
					}}, nil).Once()
					mockKafkaClient.On("Save", mock.Anything).Return(nil).Once()
					mockPaymentCoreClient.On("InternalTransfer", mock.Anything, mock.Anything).Return(nil, errDummy).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(500))
					respObj := &servus.ServiceError{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Code).Should(Equal("INTERNAL_SERVER_ERROR"))
				})
			})

			When("Refund transaction is successful", func() {
				It("Return 200", func() {
					mockChargeStorage.On("Find", mock.Anything, mock.Anything).Return(
						[]*storage.Charge{
							{
								CardID: testCardID,
								Status: string(logic.Completed),
							},
						}, nil).Once()
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					mockWorkflowDAO.On("Save", mock.Anything, mock.Anything).Return(nil)
					mockWorkflowDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyExecutionData(500, workflowengine.StateInit, string(logic.Processing)), nil).Once()
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					mockRefundStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Refund{
						{
							CardID: testCardID,
						},
					}, nil).Once()
					mockRefundStorage.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
						Id:     "123",
						Status: accountServiceAPI.AccountStatus_ACTIVE,
					}}, nil).Once()
					mockKafkaClient.On("Save", mock.Anything).Return(nil).Once()
					mockPaymentCoreClient.On("InternalTransfer", mock.Anything, mock.Anything).Return(&paymentcoreAPI.InternalResponse{
						TxID:   uuid.NewString(),
						Status: "PROCESSING",
					}, nil).Once()
					mockRefundStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()

					// Assuming stRefundStreamPersisted and stRefundCompleted states are processed successfully
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyExecutionData(500, workflowengine.State(902), string(logic.Completed)), nil).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(200))
					respObj := &api.RefundResponse{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Data.RefundStatus).Should(Equal("COMPLETED"))
				})
			})

			When("Refund transaction is not successful", func() {
				It("Return 200", func() {
					mockChargeStorage.On("Find", mock.Anything, mock.Anything).Return(
						[]*storage.Charge{
							{
								CardID: testCardID,
								Status: string(logic.Completed),
							},
						}, nil).Once()
					workflowengine.InitWithMockDAO(&mockWorkflowDAO)
					mockWorkflowDAO.On("Save", mock.Anything, mock.Anything).Return(nil)
					mockWorkflowDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil)
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyExecutionData(500, workflowengine.StateInit, string(logic.Processing)), nil).Once()
					mockRedis.On("TryLock", mock.Anything, mock.Anything, mock.Anything).Return(mockLock, nil).Once()
					mockLock.On("Unlock", mock.Anything).Return(nil).Once()
					mockRefundStorage.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Refund{
						{
							CardID: testCardID,
						},
					}, nil).Once()
					mockRefundStorage.On("Save", mock.Anything, mock.Anything).Return(nil).Once()
					mockAccountServiceClient.On("GetAccountDetailsByAccountID", mock.Anything, mock.Anything).Return(&accountServiceAPI.GetAccountResponse{Account: &accountServiceAPI.Account{
						Id:     "123",
						Status: accountServiceAPI.AccountStatus_ACTIVE,
					}}, nil).Once()
					mockKafkaClient.On("Save", mock.Anything).Return(nil).Once()
					mockPaymentCoreClient.On("InternalTransfer", mock.Anything, mock.Anything).Return(&paymentcoreAPI.InternalResponse{
						TxID:   uuid.NewString(),
						Status: "PROCESSING",
					}, nil).Once()
					mockRefundStorage.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(nil)

					// Assuming stRefundStreamPersisted and stFailed states are processed successfully
					mockWorkflowDAO.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(dummyExecutionData(500, workflowengine.State(502), string(logic.Failed)), nil).Once()
					resp, err := client.Post(apiURL, defaultModifier...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(200))
					respObj := &api.RefundResponse{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Data.RefundStatus).Should(Equal("FAILED"))
					Expect(respObj.Data.StatusReason).Should(Equal("Dummy Error Message"))
				})
			})

		})
	})
})

func dummyRefundRequest(cardID string, vendorChargeID string, amount int64, idempotencyKey string) *api.RefundRequest {
	return &api.RefundRequest{
		CardID:         cardID,
		ChargeID:       vendorChargeID,
		RefundAmount:   amount,
		IdempotencyKey: idempotencyKey,
	}
}

func dummyExecutionData(amount int64, state workflowengine.State, status string) []*weStorage.WorkflowExecution {
	executeData := refund.ExecutionData{
		CreateRefund: &dto.CreateRefund{
			IdempotencyKey: uuid.NewString(),
			ChargeID:       uuid.NewString(),
			TransactionCode: &dto.TransactionCode{
				Domain:  dto.DebitCardDomain,
				Type:    dto.SpendRefundTransactionType,
				SubType: dto.MastercardTransactionSubtype,
			},
			Amount: amount,
		},
		Charge: &storage.Charge{
			CardID:        uuid.NewString(),
			RequestAmount: 1000,
		},
		State: state,
		Refund: storage.Refund{
			RefundID:          uuid.NewString(),
			CreationTimestamp: time.Now(),
			ValueTimestamp:    time.Now(),
			Status:            status,
			StatusReason:      "Dummy Error Message",
		},
	}
	byteData, _ := json.Marshal(executeData)
	return []*weStorage.WorkflowExecution{
		{
			Data:  byteData,
			State: 0,
		},
	}
}
