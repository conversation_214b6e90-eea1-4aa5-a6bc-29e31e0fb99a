package api

import (
	"encoding/json"
	"errors"
	"fmt"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"

	. "github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
	digicardcoreAPI "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.myteksi.net/dakota/common/testauto/hcl"
	"gitlab.myteksi.net/dakota/klient/errorhandling"
	"gitlab.myteksi.net/dakota/servus/v2"
	txnLimit "gitlab.myteksi.net/dakota/transaction-limit/api"
	accountAPI "gitlab.myteksi.net/dbmy/core-banking/account-service/api"
)

var _ = Describe("Fetch Balance", func() {
	var (
		client   *hcl.Client
		setupErr error
	)

	BeforeEach(func() {
		client, setupErr = hcl.NewClient(server.URL())
		Expect(setupErr).ShouldNot(HaveOccurred())
	})

	Describe("Calling fetch balance API with appropriate parameters", func() {
		testCardID := uuid.NewString()
		apiURL := fmt.Sprintf("/v1/balances/%s", testCardID)
		testAccountID := uuid.NewString()
		var testAmount int64 = 1000
		errDummy := errors.New("simulate error")

		Context("Fetch balance request with all valid parameters", func() {
			When("Error from digicard core service", func() {
				It("Return 500", func() {
					mockBalanceDigicardCoreClient.On("GetCardByInternalCardID", mock.Anything, mock.Anything).Return(nil, &errorhandling.Error{Code: "INTERNAL_CARD_ID_INVALID"}).Once()
					resp, err := client.Get(apiURL, []hcl.RequestModifier{}...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					respObj := &servus.ServiceError{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Code).Should(Equal("INTERNAL_CARD_ID_INVALID"))
				})
			})

			When("Error from digicard core service", func() {
				It("Return 500", func() {
					mockBalanceDigicardCoreClient.On("GetCardByInternalCardID", mock.Anything, mock.Anything).Return(nil, errDummy).Once()
					resp, err := client.Get(apiURL, []hcl.RequestModifier{}...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(500))
					respObj := &servus.ServiceError{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Code).Should(Equal("INTERNAL_SERVER_ERROR"))
				})
			})

			When("Error from account service", func() {
				It("Return 500", func() {
					mockBalanceDigicardCoreClient.On("GetCardByInternalCardID", mock.Anything, mock.Anything).Return(&digicardcoreAPI.CardDetail{}, nil).Once()
					mockAccountServiceClient.On("GetAccountBalance", mock.Anything, mock.Anything).Return(nil, errDummy).Once()
					resp, err := client.Get(apiURL, []hcl.RequestModifier{}...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(500))
					respObj := &servus.ServiceError{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Code).Should(Equal("INTERNAL_SERVER_ERROR"))
				})
			})

			When("AccountID is invalid", func() {
				It("Return 400", func() {
					mockBalanceDigicardCoreClient.On("GetCardByInternalCardID", mock.Anything, mock.Anything).Return(&digicardcoreAPI.CardDetail{}, nil).Once()
					mockAccountServiceClient.On("GetAccountBalance", mock.Anything, mock.Anything).Return(nil, &errorhandling.Error{Code: "1218"}).Once()
					resp, err := client.Get(apiURL, []hcl.RequestModifier{}...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(400))
					respObj := &servus.ServiceError{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.Code).Should(Equal("ACCOUNT_ID_INVALID"))
				})
			})

			When("Account balance fetched successfully", func() {
				It("Return 200", func() {
					mockBalanceDigicardCoreClient.On("GetCardByInternalCardID", mock.Anything, mock.Anything).Return(&digicardcoreAPI.CardDetail{}, nil).Once()
					mockAccountServiceClient.On("GetAccountBalance", mock.Anything, mock.Anything).Return(&accountAPI.GetAccountBalanceResponse{
						AccountID: testAccountID,
						AvailableBalance: &accountAPI.Money{
							CurrencyCode: "SGD",
							Val:          testAmount,
						},
					}, nil).Once()
					mockTxnLimitClient.On("SearchTransactionLimitRules", mock.Anything, mock.Anything).Return(&txnLimit.GetTransactionLimitResponse{
						Data: []txnLimit.GetTransactionLimitResponseType{
							{
								CumulativeAmount: 10,
								MaximumAmount:    20,
							},
						},
					}, nil)
					resp, err := client.Get(apiURL, []hcl.RequestModifier{}...)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(resp.StatusCode).Should(Equal(200))
					respObj := &api.FetchBalanceResponse{}
					err = json.Unmarshal(resp.Body.Bytes, respObj)
					Expect(err).ShouldNot(HaveOccurred())
					Expect(respObj.AccountID).Should(Equal(testAccountID))
					Expect(respObj.AvailableBalance.Amount).Should(Equal(testAmount))
				})
			})
		})
	})
})
