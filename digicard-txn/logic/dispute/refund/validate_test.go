package refund

import (
	"context"
	"encoding/json"
	"testing"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/google/uuid"
	digicardCoreAPI "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api"
	digicardCoreMock "gitlab.com/gx-regional/dbmy/digicard/digicard-core/api/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
)

var (
	customerID = uuid.NewString()
	chargeID   = uuid.NewString()
	disputeID  = uuid.NewString()
	cardID     = uuid.NewString()
	reasonCode = "Dummy"
)

func TestValidateRefundDispute(t *testing.T) {
	mockChargeDAO := &storage.MockIChargeDAO{}
	storage.ChargeD = mockChargeDAO

	mockDigicardCore := &digicardCoreMock.DigicardCore{}

	charge := &storage.Charge{
		ChargeID:      chargeID,
		CustomerID:    customerID,
		CardID:        cardID,
		DisputeList:   json.RawMessage(`["` + disputeID + `"]`),
		RequestAmount: 100,
		Status:        string(logic.Completed),
	}

	client := ValidateRefundDisputeClientImpl{
		DigicardCoreClient: mockDigicardCore,
	}

	req := &api.RefundDisputeRequest{
		ChargeID:         chargeID,
		CustomerID:       customerID,
		CardID:           cardID,
		DisputeID:        disputeID,
		RefundType:       string(constant.Goodwill),
		RefundAmount:     10,
		RefundReasonCode: reasonCode,
		DebitAccountID:   uuid.NewString(),
	}

	t.Run("Valid flow", func(t *testing.T) {
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Charge{charge}, nil).Once()
		mockDigicardCore.On("GetAccountStatus", mock.Anything, mock.Anything).Return(&digicardCoreAPI.AccountStatusEnquiryResponse{
			Status: "ACTIVE",
		}, nil).Once()
		resp, err := client.ValidateRefundDispute(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, resp)
	})
	t.Run("Invalid refund type", func(t *testing.T) {
		tempReq := *req
		tempReq.RefundType = "DUMMY"
		expectedErr := &servus.ServiceError{
			Code:     "INVALID_REFUND_TYPE",
			Message:  "Refund Type is invalid",
			HTTPCode: api.BadRequest.HTTPStatusCode(),
		}
		resp, err := client.ValidateRefundDispute(context.Background(), &tempReq)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("fetchCharge Failed", func(t *testing.T) {
		expectedErr := api.DefaultInternalServerError
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		resp, err := client.ValidateRefundDispute(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("Invalid customer ID", func(t *testing.T) {
		tempReq := *req
		tempReq.CustomerID = uuid.NewString()
		expectedErr := &servus.ServiceError{
			Code:     "INVALID_CUSTOMER_ID",
			Message:  "Customer not found",
			HTTPCode: api.BadRequest.HTTPStatusCode(),
		}
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Charge{charge}, nil).Once()
		resp, err := client.ValidateRefundDispute(context.Background(), &tempReq)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("Invalid card ID", func(t *testing.T) {
		tempReq := *req
		tempReq.CardID = uuid.NewString()
		expectedErr := &servus.ServiceError{
			Code:     "INVALID_CARD_ID",
			Message:  "Card not found",
			HTTPCode: api.BadRequest.HTTPStatusCode(),
		}
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Charge{charge}, nil).Once()
		resp, err := client.ValidateRefundDispute(context.Background(), &tempReq)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("Json Unmarshalling error - storage.ChargeD.DisputeList Failed", func(t *testing.T) {
		tempCharge := *charge
		tempCharge.DisputeList = json.RawMessage(`{}`)
		expectedErr := api.DefaultInternalServerError
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Charge{&tempCharge}, nil).Once()
		resp, err := client.ValidateRefundDispute(context.Background(), req)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
	})
	t.Run("Invalid dispute ID", func(t *testing.T) {
		tempReq := *req
		tempReq.DisputeID = uuid.NewString()
		expectedErr := &servus.ServiceError{
			Code:     "INVALID_DISPUTE_ID",
			Message:  "Dispute not found",
			HTTPCode: api.BadRequest.HTTPStatusCode(),
		}
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Charge{charge}, nil).Once()
		resp, err := client.ValidateRefundDispute(context.Background(), &tempReq)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, resp)
	})
	t.Run("Charge not completed", func(t *testing.T) {
		tempCharge := *charge
		tempCharge.Status = string(logic.Authorised)
		expectedErr := &servus.ServiceError{
			Code:     "CHARGE_NOT_COMPLETED",
			Message:  "Charge is not completed",
			HTTPCode: api.BadRequest.HTTPStatusCode(),
		}
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Charge{&tempCharge}, nil).Once()
		resp, err := client.ValidateRefundDispute(context.Background(), req)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, resp)
	})
	t.Run("Refund amount exceeds charge amount", func(t *testing.T) {
		tempReq := *req
		tempReq.RefundAmount = charge.RequestAmount + 1
		expectedErr := &servus.ServiceError{
			Code:     "REFUND_AMOUNT_EXCEEDS",
			Message:  "Refund amount is exceeded charge amount",
			HTTPCode: api.BadRequest.HTTPStatusCode(),
		}
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Charge{charge}, nil).Once()
		resp, err := client.ValidateRefundDispute(context.Background(), &tempReq)
		assert.NotNil(t, err)
		assert.Equal(t, expectedErr, err)
		assert.Nil(t, resp)
	})
	t.Run("validateCardAccount Failed", func(t *testing.T) {
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Charge{charge}, nil).Once()
		mockDigicardCore.On("GetAccountStatus", mock.Anything, mock.Anything).Return(nil, api.DefaultInternalServerError).Once()
		resp, err := client.ValidateRefundDispute(context.Background(), req)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, resp)
	})
}

func TestFetchCharge(t *testing.T) {
	mockChargeDAO := &storage.MockIChargeDAO{}
	storage.ChargeD = mockChargeDAO

	charge := &storage.Charge{
		ChargeID:      chargeID,
		CustomerID:    customerID,
		CardID:        cardID,
		DisputeList:   json.RawMessage(`[` + disputeID + `]`),
		RequestAmount: 100,
		Status:        string(logic.Completed),
	}

	client := ValidateRefundDisputeClientImpl{}
	t.Run("Valid flow", func(t *testing.T) {
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Charge{charge}, nil).Once()
		resp, err := client.fetchCharge(context.Background(), chargeID)
		assert.NotNil(t, resp)
		assert.Equal(t, chargeID, resp.ChargeID)
		assert.Equal(t, customerID, resp.CustomerID)
		assert.Equal(t, cardID, resp.CardID)
		assert.Nil(t, err)
	})
	t.Run("When no charge found", func(t *testing.T) {
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		resp, err := client.fetchCharge(context.Background(), chargeID)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})
	t.Run("storage.ChargeD.FindOnSlave Failed", func(t *testing.T) {
		mockChargeDAO.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrTimedOut).Once()
		resp, err := client.fetchCharge(context.Background(), chargeID)
		assert.Nil(t, resp)
		assert.NotNil(t, err)
	})
}

func TestValidateCardAccount(t *testing.T) {
	mockDigicardCore := &digicardCoreMock.DigicardCore{}
	client := ValidateRefundDisputeClientImpl{
		DigicardCoreClient: mockDigicardCore,
	}

	t.Run("Valid flow", func(t *testing.T) {
		mockDigicardCore.On("GetAccountStatus", mock.Anything, mock.Anything).Return(&digicardCoreAPI.AccountStatusEnquiryResponse{
			Status: "ACTIVE",
		}, nil).Once()
		err := client.validateCardAccount(context.Background(), cardID, false)
		assert.Nil(t, err)
	})
	t.Run("GetAccountStatus Failed with error", func(t *testing.T) {
		mockDigicardCore.On("GetAccountStatus", mock.Anything, mock.Anything).Return(nil, digicardCoreAPI.ErrHasHoldCode).Once()
		err := client.validateCardAccount(context.Background(), cardID, false)
		assert.NotNil(t, err)
		assert.Equal(t, digicardCoreAPI.ErrHasHoldCode, err)
	})
	t.Run("GetAccountStatus Failed with response", func(t *testing.T) {
		mockDigicardCore.On("GetAccountStatus", mock.Anything, mock.Anything).Return(nil, nil).Once()
		err := client.validateCardAccount(context.Background(), cardID, false)
		assert.NotNil(t, err)
		assert.Equal(t, logic.BuildErrorResponse(api.InvalidAccount, invalidAccount), err)
	})
	t.Run("GetAccountStatus with override hold code", func(t *testing.T) {
		mockDigicardCore.On("GetAccountStatus", mock.Anything, mock.Anything).Return(nil, digicardCoreAPI.ErrHasHoldCode).Once()
		err := client.validateCardAccount(context.Background(), cardID, true)
		assert.Nil(t, err)
	})
}
