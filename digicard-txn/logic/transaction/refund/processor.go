package refund

import (
	"context"
	"net/http"
	"time"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.myteksi.net/dakota/servus/v2"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/slog"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
)

// CreateRefund creates new refund
func CreateRefund(ctx context.Context, req *api.RefundRequest, refundWorkflow Workflow) (*api.RefundResponse, error) {
	var (
		origChargeMetadata api.MetaData
		chargeData         *storage.Charge
		err                error
	)

	if errors := refundRequestValidator(req); len(errors) != 0 {
		slog.FromContext(ctx).Error(logRefund, "Error in validating refund request body")
		return nil, logic.CustomError(http.StatusBadRequest, errors)
	}

	if req.ChargeID != "" { // use metadata from original txn
		if chargeData, err = handleRefundWithOrigChargeID(ctx, req.ChargeID, req.CardID); err != nil {
			return nil, err
		}
		origChargeMetadata, err = chargeData.GetMetadata()
		if err != nil {
			slog.FromContext(ctx).Error(logRefund, "Error getting original metadata", slog.Error(err))
			return nil, logic.BuildErrorResponse(api.BadRequest, "invalid original transaction")
		}
	}

	if req.Metadata != nil && req.Metadata.TransactionCategory == string(constant.Clearing) {
		// use refund metadata from clearing file instead
		origChargeMetadata = *req.Metadata
	}

	createRefund := createRefundDto(req, origChargeMetadata)
	refundWorkflowResponse, err := refundWorkflow.CreateWorkflow(ctx, createRefund, chargeData)
	if err != nil {
		slog.FromContext(ctx).Error(logRefund, "Error in refund create workflow", slog.Error(err))
		return nil, err
	}

	resp, err := WorkflowResponseToRefundResponse(ctx, refundWorkflowResponse)
	if err != nil {
		slog.FromContext(ctx).Warn(logRefund, "Error in converting workflow to refund response", slog.Error(err))
		return nil, err
	}
	return resp, nil
}

func getMasterCardRefundTransactionCodes(transactionCategory constant.TransactionCategory, transactionType constant.TransactionType) *dto.TransactionCode {
	if transactionCategory == constant.TransactionCategoryMoneySend && transactionType == constant.TransactionTypeDirectCredit {
		return &dto.TransactionCode{
			Domain:  dto.DebitCardDomain,
			Type:    dto.MoneySendRefundTransactionType,
			SubType: dto.MastercardTransactionSubtype,
		}
	}
	if transactionCategory == constant.TransactionCategoryATM && transactionType == constant.TransactionTypeDirectDebit {
		return &dto.TransactionCode{
			Domain:  dto.DebitCardDomain,
			Type:    dto.ATMCashWithdrawalRefundTransactionType,
			SubType: dto.MastercardTransactionSubtype,
		}
	}
	refundTypes := map[constant.TransactionCategory]struct{}{
		constant.TransactionCategoryMOTO:     {},
		constant.TransactionCategoryECOM:     {},
		constant.TransactionCategoryCOF:      {},
		constant.TransactionCategoryPOS:      {},
		constant.TransactionCategoryClearing: {},
	}
	_, ok := refundTypes[transactionCategory]
	if ok {
		return &dto.TransactionCode{
			Domain:  dto.DebitCardDomain,
			Type:    dto.SpendRefundTransactionType,
			SubType: dto.MastercardTransactionSubtype,
		}
	}
	return &dto.TransactionCode{
		Domain:  dto.DebitCardDomain,
		Type:    dto.UnknownTransactionType,
		SubType: dto.MastercardTransactionSubtype,
	}
}

func getPaynetRefundTransactionCodes(transactionCategory constant.TransactionCategory, _ constant.TransactionType) *dto.TransactionCode {
	if transactionCategory == constant.TransactionCategoryATM {
		return &dto.TransactionCode{
			Domain:  dto.DebitCardDomain,
			Type:    dto.ATMCashWithdrawalRefundTransactionType,
			SubType: dto.PaynetSANTransactionSubtype,
		}
	}
	refundTypes := map[constant.TransactionCategory]struct{}{
		constant.TransactionCategoryECOM: {},
		constant.TransactionCategoryPOS:  {},
	}
	_, ok := refundTypes[transactionCategory]
	if ok {
		return &dto.TransactionCode{
			Domain:  dto.DebitCardDomain,
			Type:    dto.SpendRefundTransactionType,
			SubType: dto.PaynetMyDebitTransactionSubtype,
		}
	}
	return &dto.TransactionCode{
		Domain:  dto.DebitCardDomain,
		Type:    dto.UnknownTransactionType,
		SubType: dto.PaynetMyDebitTransactionSubtype,
	}
}

func createRefundDto(req *api.RefundRequest, origChargeMetadata api.MetaData) *dto.CreateRefund {
	transactionCode := &dto.TransactionCode{
		Domain:  dto.DebitCardDomain,
		Type:    dto.UnknownTransactionType,
		SubType: dto.UnknownSubtype,
	}
	switch origChargeMetadata.NetworkID {
	case constant.NETWORK_MYDEBIT, constant.NETWORK_SAN:
		transactionCode = getPaynetRefundTransactionCodes(
			constant.TransactionCategory(origChargeMetadata.TransactionCategory),
			constant.TransactionType(origChargeMetadata.TransactionType))
	case constant.NETWORK_MASTERCARD:
		transactionCode = getMasterCardRefundTransactionCodes(
			constant.TransactionCategory(origChargeMetadata.TransactionCategory),
			constant.TransactionType(origChargeMetadata.TransactionType))
	}
	refundTransfer := dto.CreateRefund{
		CardID:                       req.CardID,
		Amount:                       req.RefundAmount,
		ChargeID:                     req.ChargeID,
		TransactionCode:              transactionCode,
		IdempotencyKey:               req.IdempotencyKey,
		RefundType:                   constant.Merchant,
		CardAcceptorTermNameLocation: req.CardAcceptorTermNameLocation,
		MetaData: dto.RefundMetaData{
			TransactionCategory: origChargeMetadata.TransactionCategory,
		},
	}
	if req.Metadata != nil {
		refundTransfer.MetaData.AcquirerReferenceData = req.Metadata.AcquirerReferenceData
		refundTransfer.MetaData.OriginalAmount = req.Metadata.OriginalAmount
		refundTransfer.MetaData.OriginalCurrency = req.Metadata.OriginalCurrency
		refundTransfer.MetaData.ConversionRate = req.Metadata.McConversionRate
		refundTransfer.MetaData.CardProxyNumber = req.Metadata.CardProxyNumber
		refundTransfer.MetaData.AuthorizationID = req.Metadata.AuthorizationID
		refundTransfer.MetaData.RetailerID = req.Metadata.RetailerID
		refundTransfer.MetaData.NetworkID = req.Metadata.NetworkID
		refundTransfer.MetaData.CurrencyConvType = req.Metadata.CurrencyConvType
	}
	return &refundTransfer
}

func refundRequestValidator(req *api.RefundRequest) []servus.ErrorDetail {
	var errs []servus.ErrorDetail
	refundRequestBodyValidator(req, &errs)
	return errs
}

func refundRequestBodyValidator(req *api.RefundRequest, errs *[]servus.ErrorDetail) {
	if req.IdempotencyKey == "" {
		*errs = append(*errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "'idempotencyKey' is a mandatory field.",
			Path:      "idempotencyKey",
		})
	}
	if req.CardID == "" {
		*errs = append(*errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "'cardID' is a mandatory field.",
			Path:      "cardID",
		})
	}

	if req.RefundAmount <= 0 {
		*errs = append(*errs, servus.ErrorDetail{
			ErrorCode: string(api.FieldMissing),
			Message:   "'refundAmount' is a mandatory non-zero and non-negative field.",
			Path:      "refundAmount",
		})
	}
}

// WorkflowResponseToRefundResponse converts existing refund workflow to RefundResponse
func WorkflowResponseToRefundResponse(ctx context.Context, workflowResponse *dto.WorkflowResponse) (*api.RefundResponse, error) {
	executionData := workflowResponse.Payload.(*ExecutionData)
	refundStorage := executionData.Refund
	resp := api.RefundResponse{
		Data: &api.RefundResource{
			RefundID:                refundStorage.RefundID,
			RefundStatus:            refundStorage.Status,
			RefundAmount:            refundStorage.Amount,
			StatusReason:            refundStorage.StatusReason,
			StatusReasonDescription: refundStorage.StatusReasonDescription,
			CreationTimestamp:       refundStorage.CreationTimestamp.Truncate(time.Second),
			RefundTimestamp:         refundStorage.ValueTimestamp.Truncate(time.Second),
		},
	}

	return &resp, nil
}

func handleRefundWithOrigChargeID(ctx context.Context, chargeID string, cardID string) (*storage.Charge, error) {
	chargeData, err := transaction.LoadTransactionByChargeID(ctx, chargeID)
	if err != nil {
		if err == data.ErrNoData {
			message := "The requested charge transaction resource is not found."
			return chargeData, logic.BuildErrorResponse(api.ResourceNotFound, message)
		}
		return chargeData, logic.ErrGenericServer
	}
	if chargeData.Status != string(logic.Completed) {
		slog.FromContext(ctx).Warn(logRefund, "charge transaction status is not completed")
		return chargeData, logic.BuildErrorResponse(api.BadRequest, "charge transaction status is not completed")
	}

	var errs []servus.ErrorDetail
	if cardID != chargeData.CardID {
		errs = append(errs, servus.ErrorDetail{
			ErrorCode: string(api.ValueInvalid),
			Message:   "'cardID' is a invalid.",
			Path:      "cardID",
		})
		slog.FromContext(ctx).Warn(logRefund, "Invalid cardID in refund request")
		return nil, logic.CustomError(http.StatusBadRequest, errs)
	}
	return chargeData, nil
}
