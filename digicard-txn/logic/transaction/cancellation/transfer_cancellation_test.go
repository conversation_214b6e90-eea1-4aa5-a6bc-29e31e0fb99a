package cancellation

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
	paymentcoreAPI "gitlab.myteksi.net/dakota/payment/payment-core/api"
	paymentcore "gitlab.myteksi.net/dakota/payment/payment-core/api/mock"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
	"gitlab.myteksi.net/dakota/workflowengine"
)

func TestCancellationTransfer(t *testing.T) {
	errDummy := errors.New("simulate error")
	testcardID := uuid.NewString()
	defaultChargeStorage := &storage.Charge{
		CardID:        testcardID,
		RequestAmount: 50,
	}
	defaultRefundStorage := storage.Refund{
		TransactionDomain:  string(dto.DebitCardDomain),
		TransactionType:    string(dto.SpendRefundTransactionType),
		TransactionSubtype: string(dto.MastercardTransactionSubtype),
	}
	defaultCreateRefund := &dto.CreateRefund{
		IdempotencyKey: uuid.NewString(),
		TransactionCode: &dto.TransactionCode{
			Domain:  dto.DebitCardDomain,
			Type:    dto.SpendRefundTransactionType,
			SubType: dto.MastercardTransactionSubtype,
		},
	}
	testcases := []struct {
		chargeStorage  *storage.Charge
		creatRefund    *dto.CreateRefund
		refundStorage  storage.Refund
		workflowState  workflowengine.State
		errClient      error
		errStorage     error
		expectedErr    error
		expectedStatus string
	}{
		{
			chargeStorage: defaultChargeStorage,
			creatRefund:   defaultCreateRefund,
			refundStorage: defaultRefundStorage,
			errClient:     errDummy,
			expectedErr:   errDummy,
		},
		{
			chargeStorage:  defaultChargeStorage,
			creatRefund:    defaultCreateRefund,
			refundStorage:  defaultRefundStorage,
			expectedStatus: string(logic.Success),
			workflowState:  stCancelCompleted,
		},
		{
			chargeStorage:  defaultChargeStorage,
			creatRefund:    defaultCreateRefund,
			refundStorage:  defaultRefundStorage,
			expectedStatus: string(logic.Processing),
			workflowState:  stCancelled,
		},
		{
			chargeStorage:  defaultChargeStorage,
			creatRefund:    defaultCreateRefund,
			refundStorage:  defaultRefundStorage,
			expectedStatus: string(logic.Failed),
			workflowState:  stFailed,
		},
		{
			chargeStorage:  defaultChargeStorage,
			creatRefund:    defaultCreateRefund,
			refundStorage:  defaultRefundStorage,
			expectedStatus: "UNKNOWN",
			expectedErr:    logic.ErrTransactionResultNotReady,
		},
		{
			chargeStorage:  defaultChargeStorage,
			creatRefund:    defaultCreateRefund,
			refundStorage:  defaultRefundStorage,
			expectedStatus: string(logic.Failed),
			errStorage:     errDummy,
			expectedErr:    errDummy,
		},
	}

	for index, scenario := range testcases {
		testcase := scenario
		t.Run(fmt.Sprintf("test case #%d", index), func(t *testing.T) {
			mockStorageDAO := &storage.MockIRefundDAO{}
			mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(testcase.errStorage).Once()
			storage.RefundD = mockStorageDAO

			mockPaymentCoreClient := &paymentcore.PaymentCore{}
			response := paymentcoreAPI.InternalResponse{
				TxID:   "1",
				Status: testcase.expectedStatus,
				Error: &paymentcoreAPI.Error{
					Code:    "1018",
					Message: "Dummy Error Message.",
				},
			}
			mockPaymentCoreClient.On("InternalTransfer", mock.Anything, mock.Anything).
				Return(&response, testcase.errClient).Once()

			w := WorkflowImpl{
				PaymentCoreClient: mockPaymentCoreClient,
				StatsD:            statsd.NewNoop(),
			}
			resp, err := w.cancel(context.Background(), "", &ExecutionData{
				Refund:       testcase.refundStorage,
				Charge:       testcase.chargeStorage,
				CreateRefund: testcase.creatRefund,
			}, "")
			if testcase.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, testcase.expectedErr, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, testcase.workflowState, resp.GetState())
			}
		})
	}
}

func TestPersistCancellationStream(t *testing.T) {
	errDummy := errors.New("simulate error")
	scenarios := []struct {
		WorkflowState workflowengine.State
		streamMessage interface{}
		errParams     error
		expectedErr   error
	}{
		{
			errParams:     errDummy,
			expectedErr:   logic.ErrInvalidParams,
			streamMessage: "Stream Message",
		},
		{
			WorkflowState: stCancellationStreamPersisted,
			streamMessage: &dto.StreamMessage{
				ReferenceID: uuid.NewString(),
				TxID:        uuid.NewString(),
			},
		},
	}

	for index, test := range scenarios {
		scenario := test
		t.Run(fmt.Sprintf("test case #%d", index), func(t *testing.T) {
			w := WorkflowImpl{}
			resp, err := w.persistCancellationStream(context.Background(), "", &ExecutionData{}, scenario.streamMessage)
			if scenario.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, scenario.expectedErr, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, scenario.WorkflowState, resp.GetState())
			}
		})
	}
}

func TestCompleteCancellation(t *testing.T) {
	errDummy := errors.New("simulate error")
	scenarios := []struct {
		workflowState workflowengine.State
		streamMessage *dto.StreamMessage
		errStorage    error
		expectedErr   error
		createRefund  *dto.CreateRefund
	}{
		{
			workflowState: stCancelCompleted,
			streamMessage: &dto.StreamMessage{
				Status: "SUCCESS",
			},
			createRefund: &dto.CreateRefund{
				RefundType: constant.Merchant,
			},
		},
		{
			workflowState: stFailed,
			streamMessage: &dto.StreamMessage{
				Status: "FAILED",
			},
			createRefund: &dto.CreateRefund{
				RefundType: constant.Merchant,
			},
		},
		{
			streamMessage: &dto.StreamMessage{
				Status: "SUCCESS",
			},
			errStorage:  errDummy,
			expectedErr: errDummy,
			createRefund: &dto.CreateRefund{
				RefundType: constant.Merchant,
			},
		},
	}

	for index, test := range scenarios {
		scenario := test
		t.Run(fmt.Sprintf("test case #%d", index), func(t *testing.T) {
			mockStorageDAO := &storage.MockIRefundDAO{}
			mockStorageDAO.On("UpdateEntity", mock.Anything, mock.Anything, mock.Anything).Return(scenario.errStorage).Once()
			storage.RefundD = mockStorageDAO

			w := WorkflowImpl{
				StatsD: statsd.NewNoop(),
			}
			resp, err := w.completeCancellation(context.Background(), "", &ExecutionData{
				StreamMessage: scenario.streamMessage,
				CreateRefund:  scenario.createRefund,
			}, "")
			if scenario.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, scenario.expectedErr, err)
				assert.Nil(t, resp)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, resp)
				assert.Equal(t, scenario.workflowState, resp.GetState())
			}
		})
	}
}
