package autoreversal

import (
	"context"
	"fmt"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"

	"gitlab.myteksi.net/dakota/servus/v2/slog"
	"gitlab.myteksi.net/dakota/workflowengine"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
)

func persistStream(ctx context.Context, nextCtx *ExecutionData, logTag string, params interface{}, nextState workflowengine.State) error {
	streamMessage, ok := params.(*dto.StreamMessage)
	if !ok {
		slog.FromContext(ctx).Warn(logTag, fmt.Sprintf("Wrong params passed in state: %d", nextCtx.State))
		return logic.ErrInvalidParams
	}
	slog.FromContext(ctx).Info(logTag, fmt.Sprintf("Stream persisted successfully for state: %d", nextCtx.State))
	nextCtx.StreamMessage = streamMessage
	nextCtx.State = nextState

	return nil
}

func (w *WorkflowImpl) publish(ctx context.Context, _ string, execData workflowengine.ExecutionData, _ interface{}) (workflowengine.ExecutionData, error) {
	slog.FromContext(ctx).Info(logAutoReversal, "publish handler start")
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logAutoReversal, "Invalid context passed in publish state")
		return nil, logic.ErrInvalidContext
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.IdempotencyKeyHeader, currCtx.OriginalTransaction.ReferenceID))
	nextCtx := currCtx.Clone()
	nextCtx.State = stCancelPublished
	txnType := string(constant.CancelTx)
	txnStatus := string(logic.Canceled)
	transactionDTO := dto.ChargeStorageToDTO(nextCtx.OriginalTransaction)
	if currCtx.State == stRefundHistoryPublished {
		nextCtx.State = stRefundPublished
		txnType = string(constant.TransferTx)
		txnStatus = string(logic.Completed)
		transactionDTO = dto.RefundStorageToDTO(ctx, &nextCtx.Refund, nextCtx.OriginalTransaction, nextCtx.OriginalTransaction.ReferenceID)
	}

	publishErr := w.Publisher.PublishCompletedAutoReversal(ctx, transactionDTO, logAutoReversal, txnType, txnStatus)

	if publishErr != nil {
		slog.FromContext(ctx).Warn(logAutoReversal, "error publishing charge transaction status to kafka: "+transactionDTO.Status, slog.Error(publishErr))
		return nil, publishErr
	}
	slog.FromContext(ctx).Info(logAutoReversal, "publish handler end")

	return nextCtx, nil

}

func (w *WorkflowImpl) publishTxnHistory(ctx context.Context, _ string, execData workflowengine.ExecutionData, _ interface{}) (workflowengine.ExecutionData, error) {
	slog.FromContext(ctx).Info(logAutoReversal, "publishTxnHistory: publish handler start")
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logAutoReversal, "Invalid context passed in publish state")
		return nil, logic.ErrInvalidContext
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.IdempotencyKeyHeader, currCtx.OriginalTransaction.ReferenceID))
	nextCtx := currCtx.Clone()
	nextCtx.State = stCancelHistoryPublished
	txnType := string(constant.CancelTx)
	txnStatus := string(logic.Canceled)
	transactionDTO := dto.ChargeStorageToDTO(nextCtx.OriginalTransaction)
	if currCtx.State == stRefunded {
		nextCtx.State = stRefundHistoryPublished
		txnType = string(constant.TransferTx)
		txnStatus = string(logic.Completed)
		transactionDTO = dto.RefundStorageToDTO(ctx, &nextCtx.Refund, nextCtx.OriginalTransaction, nextCtx.OriginalTransaction.ReferenceID)
	}

	publishErr := w.Publisher.Publish(ctx, transactionDTO, logAutoReversal, txnType, txnStatus)

	if publishErr != nil {
		slog.FromContext(ctx).Warn(logAutoReversal, "publishTxnHistory: error publishing charge transaction status to kafka: "+transactionDTO.Status, slog.Error(publishErr))
		return nil, publishErr
	}
	slog.FromContext(ctx).Info(logAutoReversal, "publishTxnHistory: publish handler end")

	return nextCtx, nil
}
