package transactioninfo

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
	"gitlab.myteksi.net/dakota/servus/v2/data"
	"gitlab.myteksi.net/dakota/servus/v2/statsd"
)

func TestFetchTransactionDetailsByCustomer(t *testing.T) {
	testCustomerID := uuid.NewString()
	testTransactionID := uuid.NewString()
	testChargeID := uuid.NewString()
	mockSourceAccountID := "mock-source-account-id"
	defaultChargeStorages := []*storage.Charge{{
		ChargeID:             testChargeID,
		CustomerID:           testCustomerID,
		PaymentTransactionID: testTransactionID,
		CardID:               uuid.NewString(),
		Status:               string(logic.Completed),
		CreationTimestamp:    time.Now(),
		ValueTimestamp:       time.Now(),
		SourceAccountID:      mockSourceAccountID,
		OriginalCurrency:     "SGD",
		OriginalAmount:       100,
		RequestCurrency:      "SGD",
		RequestAmount:        100,
		CaptureAmount:        100,
		MCC:                  "Test MCC",
		MerchantDescription:  "Test MCC Description",
		Metadata:             []byte("{\"authorizationID\": \"SampleAuth\", \"threeDsValidation\": \"Y\", \"transactionCategory\": \"SampleType\", \"retrievalReferenceNumber\": \"Sample RRN\"}"),
	}, {
		CustomerID:           testCustomerID,
		PaymentTransactionID: uuid.NewString(),
		CardID:               uuid.NewString(),
		Status:               string(logic.Authorised),
		CreationTimestamp:    time.Now(),
		ValueTimestamp:       time.Now(),
		SourceAccountID:      mockSourceAccountID,
		OriginalCurrency:     "SGD",
		OriginalAmount:       200,
		RequestCurrency:      "SGD",
		RequestAmount:        200,
		CaptureAmount:        200,
		MCC:                  "Test MCC",
		MerchantDescription:  "Test MCC Description",
		Metadata:             []byte("{\"authorizationID\": \"SampleAuth\", \"threeDsValidation\": \"Y\", \"transactionCategory\": \"SampleType\", \"retrievalReferenceNumber\": \"Sample RRN\"}"),
	}}

	mockStorage := &storage.MockIChargeDAO{}
	storage.ChargeD = mockStorage
	mockRefund := &storage.MockIRefundDAO{}
	storage.RefundD = mockRefund
	mockDipsute := &storage.MockIDisputeDAO{}
	storage.DisputeD = mockDipsute
	c := ClientImpl{
		StatsD: statsd.NewNoop(),
	}
	t.Run("Valid flow", func(t *testing.T) {
		mockStorage.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(defaultChargeStorages, nil).Once()
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		response, err := c.FetchTransactionDetails(context.Background(), &api.FetchTransactionDetailsRequest{TransactionID: testTransactionID, CustomerID: testCustomerID})
		assert.Nil(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, mockSourceAccountID, response.TransactionDetails[0].AccountID)
	})
	t.Run("Valid flow - Refunds found", func(t *testing.T) {
		mockStorage.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(defaultChargeStorages, nil).Once()
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Refund{
			{
				RefundID:          uuid.NewString(),
				OriginalChargeID:  testChargeID,
				CreationTimestamp: time.Now(),
				Amount:            100,
			},
		}, nil).Once()
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		response, err := c.FetchTransactionDetails(context.Background(), &api.FetchTransactionDetailsRequest{TransactionID: testTransactionID, CustomerID: testCustomerID})
		assert.Nil(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, response.TransactionDetails[0].Refunds[0].RefundAmount, int64(100))
		assert.Equal(t, mockSourceAccountID, response.TransactionDetails[0].AccountID)
	})
	t.Run("Valid flow - Refunds and Disputes found", func(t *testing.T) {
		mockStorage.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(defaultChargeStorages, nil).Once()
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Refund{
			{
				RefundID:          uuid.NewString(),
				OriginalChargeID:  testChargeID,
				CreationTimestamp: time.Now(),
				Amount:            100,
			},
		}, nil).Once()
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Dispute{
			{
				ChargeID:        testChargeID,
				CustomerID:      testCustomerID,
				RefundID:        uuid.NewString(),
				RefundType:      "Goodwill",
				DisputeComments: "Sample Comments",
			},
		}, nil).Once()
		response, err := c.FetchTransactionDetails(context.Background(), &api.FetchTransactionDetailsRequest{TransactionID: testTransactionID, CustomerID: testCustomerID})
		assert.Nil(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, response.TransactionDetails[0].Refunds[0].RefundAmount, int64(100))
		assert.Equal(t, response.TransactionDetails[0].RefundType, "Goodwill")
		assert.Equal(t, mockSourceAccountID, response.TransactionDetails[0].AccountID)
	})
	t.Run("When no transaction found", func(t *testing.T) {
		mockStorage.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNoData).Once()
		response, err := c.FetchTransactionDetails(context.Background(), &api.FetchTransactionDetailsRequest{TransactionID: testTransactionID, CustomerID: testCustomerID})
		assert.NotNil(t, err)
		assert.Equal(t, logic.BuildErrorResponse(api.InvalidTransactionRequest, noTransactionDataFound), err)
		assert.Nil(t, response)
	})
	t.Run("When storage.ChargeD.FindOnSlave Failed", func(t *testing.T) {
		mockStorage.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		response, err := c.FetchTransactionDetails(context.Background(), &api.FetchTransactionDetailsRequest{TransactionID: testTransactionID, CustomerID: testCustomerID})
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, response)
	})
	t.Run("When storage.RefundD.FindOnSlave Failed", func(t *testing.T) {
		mockStorage.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(defaultChargeStorages, nil).Once()
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		response, err := c.FetchTransactionDetails(context.Background(), &api.FetchTransactionDetailsRequest{TransactionID: testTransactionID, CustomerID: testCustomerID})
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, response)
	})
	t.Run("When storage.DisputeD.FindOnSlave Failed", func(t *testing.T) {
		mockStorage.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(defaultChargeStorages, nil).Once()
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		response, err := c.FetchTransactionDetails(context.Background(), &api.FetchTransactionDetailsRequest{TransactionID: testTransactionID, CustomerID: testCustomerID})
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, response)
	})
	t.Run("When dto.ConvertJSONToMetaData Failed", func(t *testing.T) {
		defaultChargeStorages[0].Metadata = json.RawMessage(`[]`)
		mockStorage.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(defaultChargeStorages, nil).Once()
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		response, err := c.FetchTransactionDetails(context.Background(), &api.FetchTransactionDetailsRequest{TransactionID: testTransactionID, CustomerID: testCustomerID})
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, response)
	})
	t.Run("When dto.ConvertDisputeListJSONToArray Failed", func(t *testing.T) {
		defaultChargeStorages[0].Metadata = []byte("{\"authorizationID\": \"SampleAuth\", \"threeDsValidation\": \"Y\", \"transactionCategory\": \"SampleType\", \"retrievalReferenceNumber\": \"Sample RRN\"}")
		defaultChargeStorages[0].DisputeList = json.RawMessage(`{}`)
		mockStorage.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(defaultChargeStorages, nil).Once()
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		response, err := c.FetchTransactionDetails(context.Background(), &api.FetchTransactionDetailsRequest{TransactionID: testTransactionID, CustomerID: testCustomerID})
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, response)
	})
}

func TestConvertToFetchTransactionDetailResponse(t *testing.T) {
	testCustomerID := uuid.NewString()
	testTransactionID := uuid.NewString()
	testChargeID := uuid.NewString()
	defaultChargeStorages := []*storage.Charge{{
		ChargeID:             testChargeID,
		CustomerID:           testCustomerID,
		PaymentTransactionID: testTransactionID,
		CardID:               uuid.NewString(),
		Status:               string(logic.Completed),
		CreationTimestamp:    time.Now(),
		ValueTimestamp:       time.Now(),
		OriginalCurrency:     "SGD",
		OriginalAmount:       100,
		RequestCurrency:      "SGD",
		RequestAmount:        100,
		CaptureAmount:        100,
		MCC:                  "Test MCC",
		MerchantDescription:  "Test MCC Description",
		Metadata:             []byte("{\"authorizationID\": \"SampleAuth\", \"threeDsValidation\": \"Y\", \"transactionCategory\": \"SampleType\", \"retrievalReferenceNumber\": \"Sample RRN\"}"),
	}, {
		CustomerID:           testCustomerID,
		PaymentTransactionID: uuid.NewString(),
		CardID:               uuid.NewString(),
		Status:               string(logic.Authorised),
		CreationTimestamp:    time.Now(),
		ValueTimestamp:       time.Now(),
		OriginalCurrency:     "SGD",
		OriginalAmount:       200,
		RequestCurrency:      "SGD",
		RequestAmount:        200,
		CaptureAmount:        200,
		MCC:                  "Test MCC",
		MerchantDescription:  "Test MCC Description",
		Metadata:             []byte("{\"authorizationID\": \"SampleAuth\", \"threeDsValidation\": \"Y\", \"transactionCategory\": \"SampleType\", \"retrievalReferenceNumber\": \"Sample RRN\"}"),
	}}
	mockStorage := &storage.MockIChargeDAO{}
	storage.ChargeD = mockStorage
	mockRefund := &storage.MockIRefundDAO{}
	storage.RefundD = mockRefund
	mockDipsute := &storage.MockIDisputeDAO{}
	storage.DisputeD = mockDipsute
	c := ClientImpl{
		StatsD: statsd.NewNoop(),
	}

	t.Run("Valid flow", func(t *testing.T) {
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		response, err := c.convertToFetchTransactionDetailResponse(context.Background(), defaultChargeStorages)
		assert.Nil(t, err)
		assert.NotNil(t, response)
	})
	t.Run("Valid flow - Refunds found", func(t *testing.T) {
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Refund{
			{
				RefundID:          uuid.NewString(),
				OriginalChargeID:  testChargeID,
				CreationTimestamp: time.Now(),
				Amount:            100,
			},
		}, nil).Once()
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		response, err := c.convertToFetchTransactionDetailResponse(context.Background(), defaultChargeStorages)
		assert.Nil(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, response[0].Refunds[0].RefundAmount, int64(100))
	})
	t.Run("Valid flow - Refunds and Disputes found", func(t *testing.T) {
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Refund{
			{
				RefundID:          uuid.NewString(),
				OriginalChargeID:  testChargeID,
				CreationTimestamp: time.Now(),
				Amount:            100,
			},
		}, nil).Once()
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Dispute{
			{
				ChargeID:        testChargeID,
				CustomerID:      testCustomerID,
				RefundID:        uuid.NewString(),
				RefundType:      "Goodwill",
				DisputeComments: "Sample Comments",
			},
		}, nil).Once()
		response, err := c.convertToFetchTransactionDetailResponse(context.Background(), defaultChargeStorages)
		assert.Nil(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, response[0].Refunds[0].RefundAmount, int64(100))
		assert.Equal(t, response[0].RefundType, "Goodwill")
	})
	t.Run("When storage.RefundD.FindOnSlave Failed", func(t *testing.T) {
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		response, err := c.convertToFetchTransactionDetailResponse(context.Background(), defaultChargeStorages)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, response)
	})
	t.Run("When storage.DisputeD.FindOnSlave Failed", func(t *testing.T) {
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		response, err := c.convertToFetchTransactionDetailResponse(context.Background(), defaultChargeStorages)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, response)
	})
	t.Run("When dto.ConvertJSONToMetaData Failed", func(t *testing.T) {
		defaultChargeStorages[0].Metadata = json.RawMessage(`[]`)
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		response, err := c.convertToFetchTransactionDetailResponse(context.Background(), defaultChargeStorages)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, response)
	})
	t.Run("When dto.ConvertDisputeListJSONToArray Failed", func(t *testing.T) {
		defaultChargeStorages[0].Metadata = []byte("{\"authorizationID\": \"SampleAuth\", \"threeDsValidation\": \"Y\", \"transactionCategory\": \"SampleType\", \"retrievalReferenceNumber\": \"Sample RRN\"}")
		defaultChargeStorages[0].DisputeList = json.RawMessage(`{}`)
		mockRefund.On("FindOnSlave", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
		response, err := c.convertToFetchTransactionDetailResponse(context.Background(), defaultChargeStorages)
		assert.NotNil(t, err)
		assert.Equal(t, api.DefaultInternalServerError, err)
		assert.Nil(t, response)
	})
}

func TestFetchRefundDisputes(t *testing.T) {
	mockDipsute := &storage.MockIDisputeDAO{}
	storage.DisputeD = mockDipsute
	c := ClientImpl{
		StatsD: statsd.NewNoop(),
	}
	chargeArray := make([]string, 3)
	testChargeID := uuid.NewString()
	t.Run("valid flow", func(t *testing.T) {
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return([]*storage.Dispute{
			{
				ChargeID:        testChargeID,
				CustomerID:      uuid.NewString(),
				RefundID:        uuid.NewString(),
				RefundType:      "Goodwill",
				DisputeComments: "Sample Comments",
			},
		}, nil).Once()

		response, err := c.fetchRefundDisputesMap(context.Background(), chargeArray)
		assert.NotNil(t, response)
		assert.Equal(t, response[testChargeID].DisputeComments, "Sample Comments")
		assert.Nil(t, err)
	})
	t.Run("Invalid flow", func(t *testing.T) {
		mockDipsute.On("Find", mock.Anything, mock.Anything, mock.Anything).Return(nil, data.ErrNotSupported).Once()
		response, err := c.fetchRefundDisputesMap(context.Background(), chargeArray)
		assert.Nil(t, response)
		assert.NotNil(t, err)
		assert.Equal(t, data.ErrNotSupported, err)
	})
}

func TestFetchDisputeIDList(t *testing.T) {
	charge := &storage.Charge{
		CustomerID:           uuid.NewString(),
		PaymentTransactionID: uuid.NewString(),
		CardID:               uuid.NewString(),
		Status:               string(logic.Authorised),
		CreationTimestamp:    time.Now(),
		ValueTimestamp:       time.Now(),
		OriginalCurrency:     "SGD",
		OriginalAmount:       200,
		RequestCurrency:      "SGD",
		RequestAmount:        200,
		CaptureAmount:        200,
		MCC:                  "Test MCC",
		MerchantDescription:  "Test MCC Description",
		Metadata:             []byte("{\"authorizationID\": \"SampleAuth\", \"threeDsValidation\": \"Y\", \"transactionCategory\": \"SampleType\", \"retrievalReferenceNumber\": \"Sample RRN\"}"),
		DisputeList:          json.RawMessage(`["1234","4567"]`),
	}
	t.Run("Valid flow", func(t *testing.T) {
		response, err := fetchDisputeIDList(charge)
		assert.Nil(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, 2, len(response))
	})
	t.Run("InValid flow", func(t *testing.T) {
		charge.DisputeList = json.RawMessage(`{}`)
		response, err := fetchDisputeIDList(charge)
		assert.NotNil(t, err)
		assert.Nil(t, response)
	})
}

func TestConstructRefundDetail(t *testing.T) {
	testChargeID := uuid.NewString()
	refunds := []*storage.Refund{
		{
			RefundID:          uuid.NewString(),
			OriginalChargeID:  testChargeID,
			CreationTimestamp: time.Now(),
			Amount:            100,
		},
	}
	t.Run("Valid flow", func(t *testing.T) {
		response := constructRefundDetail(refunds)
		assert.NotNil(t, response)
		assert.Equal(t, response[testChargeID][0].RefundAmount, int64(100))
	})
}
