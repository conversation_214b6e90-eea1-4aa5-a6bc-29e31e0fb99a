package charge

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic/transaction"

	"gitlab.com/gx-regional/dbmy/digicard/common/constant"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/api"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/dto"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/logic"
	"gitlab.com/gx-regional/dbmy/digicard/digicard-txn/storage"
	commonCtx "gitlab.myteksi.net/dakota/common/context"
	"gitlab.myteksi.net/dakota/servus/v2/slog"
	txnLimit "gitlab.myteksi.net/dakota/transaction-limit/api"
	"gitlab.myteksi.net/dakota/workflowengine"
)

//nolint:funlen
func (w *WorkflowImpl) checkLimit(ctx context.Context, _ string, execData workflowengine.ExecutionData, _ interface{}) (workflowengine.ExecutionData, error) {
	logTag := logHandler + ".checkLimit"
	slog.FromContext(ctx).Info(logTag, "checkLimit handler start")
	currCtx, ok := execData.(*ExecutionData)
	if !ok {
		slog.FromContext(ctx).Warn(logTag, "Invalid context passed in checkLimit state")
		return nil, logic.ErrInvalidContext
	}
	ctx = slog.AddTagsToContext(ctx, slog.CustomTag(logic.IdempotencyKeyHeader, currCtx.CreateCharge.IdempotencyKey))
	nextCtx := currCtx.Clone()
	chargeStorage := currCtx.Charge

	chargeMetadata := &api.MetaData{}
	err := json.Unmarshal(chargeStorage.Metadata, chargeMetadata)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "Invalid transaction metadata")
		return nil, logic.ErrGenericServer
	}

	if !w.canCheckTransactionLimit(ctx, chargeStorage, logTag, chargeMetadata) {
		nextCtx.State = stTransactionLimitChecked
		return nextCtx, nil
	}

	cardIDCondition := txnLimit.Condition{Key: constant.TxnLimitConditionKey, Value: chargeStorage.CardID}

	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXUserID, currCtx.Charge.CustomerID)
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderIdempotencyKey, currCtx.CreateCharge.IdempotencyKey)
	ctx = commonCtx.WithHTTPHeader(ctx, commonCtx.HeaderXServiceID, constant.ServiceID)

	limitName, err := transaction.ResolveLimitName(chargeMetadata.TransactionCategory, chargeMetadata.TransactionSubCategory)
	if err != nil {
		slog.FromContext(ctx).Warn(logTag, "Unsupported transaction limit")
		nextCtx.State = stLimitCheckFailed
		toUnsupportedTxnResp(nextCtx)
		return nextCtx, nil
	}

	if constant.TxnLimitContactless == limitName && logic.PinValidationPresent == chargeMetadata.PinValidation {
		limitName = constant.TxnLimitPinAndPay
	}

	checkLimitResp, err := w.TransactionLimitClient.CheckTransactionLimits(ctx, &txnLimit.CheckTransactionLimitRequest{
		LimitName:  limitName,
		Amount:     chargeStorage.RequestAmount,
		Currency:   chargeStorage.RequestCurrency,
		Conditions: []txnLimit.Condition{cardIDCondition},
	})

	if err != nil {
		slog.FromContext(ctx).Error(logTag, "TransactionLimitClient.CheckTransactionLimit", slog.Error(err))
		nextCtx.State = stLimitCheckFailed
		toUnsupportedTxnResp(nextCtx)
		return nextCtx, nil
	}
	if checkLimitResp == nil || checkLimitResp.Data == nil {
		slog.FromContext(ctx).Error(logTag, fmt.Sprintf("TransactionLimitClient.CheckTransactionLimit Response Emptyc %v", checkLimitResp))
		nextCtx.State = stLimitCheckFailed
		toUnsupportedTxnResp(nextCtx)
		return nextCtx, nil
	}
	// for Paynet ATM withdrawal, we need to return daily remaining limit
	if transaction.IsPaynetATM(chargeMetadata.TransactionCategory, chargeMetadata.TransactionSubCategory, chargeMetadata.NetworkID) {
		remainingWithdrawalLimit, getLimitErr := w.getCardRemainingWithdrawalLimit(ctx, cardIDCondition)
		if getLimitErr != nil {
			slog.FromContext(ctx).Warn(logTag, "TransactionLimitClient.SearchTransactionLimitRules error", slog.Error(err))
		}
		nextCtx.CreateCharge.AmountParams.RemainingLimitAmount = remainingWithdrawalLimit
	}

	switch logic.TransactionStatus(checkLimitResp.Data.Result) {
	case logic.Success:
		nextCtx.State = stTransactionLimitChecked
		slog.FromContext(ctx).Info(logTag, "TransactionLimitClient success verdict")
	case logic.Failed:
		nextCtx.State = stLimitCheckFailed
		nextCtx.Charge.Status = dto.StateToStatus(nextCtx.State)
		nextCtx.Charge.StatusReason = getErrorCode(&checkLimitResp.Data.Errors, checkLimitResp.Data.LimitName)
		nextCtx.Charge.StatusReasonDescription = buildErrorMsg(&checkLimitResp.Data.Errors)
		slog.FromContext(ctx).Debug(logTag, fmt.Sprintf("TransactionLimitClient failed verdict. Error message: %s", nextCtx.Charge.StatusReasonDescription))
	default:
		return nil, logic.BuildErrorResponse(api.InternalServerError, "invalid response from transaction limit service")
	}

	nextCtx.Charge.Properties = dto.UpdateProperties(nextCtx.Charge.Properties, "", "", int(nextCtx.State), checkLimitResp.Data.VerdictId)
	if err := UpdateDBCharge(ctx, logTag, &currCtx.Charge, &nextCtx.Charge); err != nil {
		return nil, err
	}
	slog.FromContext(ctx).Info(logTag, "checkLimit handler end")
	return nextCtx, nil
}

func toUnsupportedTxnResp(execData *ExecutionData) {
	execData.State = stLimitCheckFailed
	execData.Charge.Status = string(logic.Failed)
	execData.Charge.StatusReason = logic.ErrUnsupportedTransactionLimit.Error()
}

func buildErrorMsg(errorsList *[]txnLimit.Errors) string {
	var errMessages []string
	for _, err := range *errorsList {
		errMessages = append(errMessages, err.Message)
	}
	description := strings.Join(errMessages, " & ")
	return description
}

func (w *WorkflowImpl) canCheckTransactionLimit(ctx context.Context, chargeStorage storage.Charge, logTag string, chargeMetaData *api.MetaData) bool {
	if !w.FeatureFlags.EnableTransactionLimit {
		slog.FromContext(ctx).Info(logTag, "Transaction Limit not enabled.")
		return false
	}
	if chargeStorage.Type == string(dto.Moneysend) {
		slog.FromContext(ctx).Info(logTag, "Transaction Limit check is ignored for MONEYSEND.")
		return false
	}

	if constant.TransactionCategoryClearing == constant.TransactionCategory(chargeMetaData.TransactionCategory) {
		slog.FromContext(ctx).Info(logTag, "Transaction Limit check is ignored for CLEARING file.")
		return false
	}

	return true
}

func getErrorCode(errorsList *[]txnLimit.Errors, limitName string) string {
	if limitName == constant.TxnLimitContactless {
		return constant.ContactlessLimitBreach
	}
	for _, err := range *errorsList {
		return err.Type
	}
	return limitName
}

func (w *WorkflowImpl) getCardRemainingWithdrawalLimit(ctx context.Context, cardIDCondition txnLimit.Condition) (int64, error) {
	resp, err := w.TransactionLimitClient.SearchTransactionLimitRules(ctx, &txnLimit.GetTransactionLimitRequest{
		LimitName:  constant.TxnLimitAtmWithdrawal,
		Conditions: []txnLimit.Condition{cardIDCondition},
	})
	if err != nil {
		return 0, err
	}

	if resp != nil && len(resp.Data) > 0 {
		remainingAmount := resp.Data[0].MaximumAmount - resp.Data[0].CumulativeAmount
		// return 0 if remaining amount is less than 0
		if remainingAmount < 0 {
			return 0, nil
		}
		return remainingAmount, nil
	}
	return 0, nil
}
